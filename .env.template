######################################################################
#  WARNING
#  ------------------------------------------------------------------
#  This is a template for a `.env` file.  
#  • **Do NOT commit or push a populated version of this file (with real
#    secrets) to any public repository or shared location.**  
#  • Replace the placeholder values (`<your-...>` or `<...>`) with your
#    actual credentials in a private copy only.  
######################################################################

# === LLM Provider API Keys ===
OPENAI_API_KEY=<your-api-key-here>         # OpenAI LLM
ANTHROPIC_API_KEY=<your-api-key-here>      # Anthropic LLM
TOGETHER_API_KEY=<your-api-key-here>       # TogetherAI LLM
DEEPSEEK_API_KEY=<your-api-key-here>       # DeepSeek LLM
GOOGLE_API_KEY=<your-api-key-here>         # Gemini (Google) LLM
XAI_API_KEY=<your-api-key-here>            # Grok (xAI) LLM

# === Google Search Tool Configuration ===
GOOGLE_API_KEY=<your-api-key-here>         # Google Programmable Search API Key
GOOGLE_CX=<your-cx-here>                   # Google Programmable Search Engine CX

# === Azure OpenAI Configuration ===
AZURE_OPENAI_API_KEY=<your-api-key-here>   # Azure OpenAI API Key
AZURE_OPENAI_ENDPOINT=https://<your-resource-name>.openai.azure.com/  # Azure OpenAI Endpoint
AZURE_OPENAI_API_VERSION=<your-api-version-here>                      # Azure OpenAI API Version
AZURE_OPENAI_DEPLOYMENT=<your-deployment-name-here>                  # Azure OpenAI Deployment Name
