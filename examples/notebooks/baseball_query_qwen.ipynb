{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Remember to put your API keys in .env\n", "import dotenv\n", "dotenv.load_dotenv()\n", "\n", "# Or, you can set the API keys directly\n", "# import os\n", "# os.environ[\"TOGETHER_API_KEY\"] = \"your_api_key\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==> Initializing octotools...\n", "Enabled tools: ['Generalist_Solution_Generator_Tool', 'Image_Captioner_Tool', 'Object_Detector_Tool']\n", "LLM engine name: together-Qwen/Qwen2-VL-72B-Instruct\n", "\n", "==> Setting up tools...\n", "Loading tools and getting metadata...\n", "Updated Python path: ['/root/Projects/octotools', '/root/Projects/octotools/octotools', '/root/Projects/octotools', '/root/Projects/octotools/octotools', '/opt/conda/envs/octotools/lib/python310.zip', '/opt/conda/envs/octotools/lib/python3.10', '/opt/conda/envs/octotools/lib/python3.10/lib-dynload', '', '/opt/conda/envs/octotools/lib/python3.10/site-packages', '/root/Projects/octotools', '/opt/conda/envs/octotools/lib/python3.10/site-packages/setuptools/_vendor', '/tmp/tmp_33x21y_']\n", "\n", "==> Attempting to import: tools.generalist_solution_generator.tool\n", "Found tool class: Generalist_Solution_Generator_Tool\n", "Metadata for Generalist_Solution_Generator_Tool: {'tool_name': 'Generalist_Solution_Generator_Tool', 'tool_description': 'A generalized tool that takes query from the user as prompt, and answers the question step by step to the best of its ability. It can also accept an image.', 'tool_version': '1.0.0', 'input_types': {'prompt': \"str - The prompt that includes query from the user to guide the agent to generate response (Examples: 'Describe this image in detail').\", 'image': 'str - The path to the image file if applicable (default: None).'}, 'output_type': 'str - The generated response to the original query prompt', 'demo_commands': [{'command': 'execution = tool.execute(prompt=\"Summarize the following text in a few lines\")', 'description': 'Generate a short summary given the prompt from the user.'}, {'command': 'execution = tool.execute(prompt=\"Explain the mood of this scene.\", image=\"path/to/image1.png\")', 'description': 'Generate a caption focusing on the mood using a specific prompt and image.'}, {'command': 'execution = tool.execute(prompt=\"Give your best coordinate estimate for the pacemaker in the image and return (x1, y1, x2, y2)\", image=\"path/to/image2.png\")', 'description': 'Generate bounding box coordinates given the image and prompt from the user. The format should be (x1, y1, x2, y2).'}, {'command': 'execution = tool.execute(prompt=\"Is the number of tiny objects that are behind the small metal jet less than the number of tiny things left of the tiny sedan?\", image=\"path/to/image2.png\")', 'description': 'Answer a question step by step given the image.'}], 'user_metadata': {'limitation': 'The Generalist_Solution_Generator_Tool may provide hallucinated or incorrect responses.', 'best_practice': \"Use the Generalist_Solution_Generator_Tool for general queries or tasks that don't require specialized knowledge or specific tools in the toolbox. For optimal results:\\n\\n1) Provide clear, specific prompts.\\n2) Use it to answer the original query through step by step reasoning for tasks without complex or multi-step reasoning.\\n3) For complex queries, break them down into subtasks and use the tool multiple times.\\n4) Use it as a starting point for complex tasks, then refine with specialized tools.\\n5) Verify important information from its responses.\\n6) For image-related tasks, ensure the image path is correct and the prompt is relevant to the image content.\"}, 'require_llm_engine': True}\n", "\n", "==> Attempting to import: tools.image_captioner.tool\n", "Found tool class: Image_Captioner_Tool\n", "Initializing Image Captioner Tool with model: together-Qwen/Qwen2-VL-72B-Instruct\n", "Metadata for Image_Captioner_Tool: {'tool_name': 'Image_Captioner_Tool', 'tool_description': \"A tool that generates captions for images using OpenAI's multimodal model.\", 'tool_version': '1.0.0', 'input_types': {'image': 'str - The path to the image file.', 'prompt': \"str - The prompt to guide the image captioning (default: 'Describe this image in detail.').\"}, 'output_type': 'str - The generated caption for the image.', 'demo_commands': [{'command': 'execution = tool.execute(image=\"path/to/image.png\")', 'description': 'Generate a caption for an image using the default prompt and model.'}, {'command': 'execution = tool.execute(image=\"path/to/image.png\", prompt=\"Explain the mood of this scene.\")', 'description': 'Generate a caption focusing on the mood using a specific prompt and model.'}], 'user_metadata': {'limitation': 'The Image_Captioner_Tool provides general image descriptions but has limitations: 1) May make mistakes in complex scenes, counting, attribute detection, and understanding object relationships. 2) Might not generate comprehensive captions, especially for images with multiple objects or abstract concepts. 3) Performance varies with image complexity. 4) Struggles with culturally specific or domain-specific content. 5) May overlook details or misinterpret object relationships. For precise descriptions, consider: using it with other tools for context/verification, as an initial step before refinement, or in multi-step processes for ambiguity resolution. Verify critical information with specialized tools or human expertise when necessary.'}, 'require_llm_engine': True}\n", "\n", "==> Attempting to import: tools.object_detector.tool\n", "Found tool class: Object_Detector_Tool\n", "Metadata for Object_Detector_Tool: {'tool_name': 'Object_Detector_Tool', 'tool_description': 'A tool that detects objects in an image using the Grounding DINO model and saves individual object images with empty padding.', 'tool_version': '1.0.0', 'input_types': {'image': 'str - The path to the image file.', 'labels': 'list - A list of object labels to detect.', 'threshold': 'float - The confidence threshold for detection (default: 0.35).', 'model_size': \"str - The size of the model to use ('tiny' or 'base', default: 'tiny').\", 'padding': 'int - The number of pixels to add as empty padding around detected objects (default: 20).'}, 'output_type': 'list - A list of detected objects with their scores, bounding boxes, and saved image paths.', 'demo_commands': [{'command': 'execution = tool.execute(image=\"path/to/image.png\", labels=[\"baseball\", \"basket\"])', 'description': 'Detect baseball and basket in an image, save the detected objects with default empty padding, and return their paths.'}, {'command': 'execution = tool.execute(image=\"path/to/image.png\", labels=[\"car\", \"person\"], threshold=0.5, model_size=\"base\", padding=15)', 'description': 'Detect car and person in an image using the base model, save the detected objects with 15 pixels of empty padding, and return their paths.'}], 'user_metadata': {'limitation': 'The model may not always detect objects accurately, and its performance can vary depending on the input image and the associated labels. It typically struggles with detecting small objects, objects that are uncommon, or objects with limited or specific attributes. For improved accuracy or better detection in certain situations, consider using supplementary tools or image processing techniques to provide additional information for verification.'}, 'require_llm_engine': False}\n", "\n", "==> Total number of tools imported: 3\n", "\n", "==> Running demo commands for each tool...\n", "Checking availability of Generalist_Solution_Generator_Tool...\n", "Checking availability of Image_Captioner_Tool...\n", "Initializing Image Captioner Tool with model: gpt-4o-mini\n", "Checking availability of Object_Detector_Tool...\n", "\n", "✅ Finished running demo commands for each tool.\n", "✅ Finished setting up tools.\n", "✅ Total number of final available tools: 3\n", "✅ Final available tools: ['Generalist_Solution_Generator_Tool', 'Image_Captioner_Tool', 'Object_Detector_Tool']\n"]}], "source": ["from octotools.solver import construct_solver\n", "\n", "# Set the LLM engine name\n", "llm_engine_name = \"together-Qwen/Qwen2-VL-72B-Instruct\"\n", "\n", "# Construct the solver\n", "solver = construct_solver(\n", "    llm_engine_name=llm_engine_name, \n", "    enabled_tools=[\"Generalist_Solution_Generator_Tool\", \"Image_Captioner_Tool\", \"Object_Detector_Tool\"],\n", "    verbose=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==> 🔍 Received Query: How many baseballs are there?\n", "\n", "==> 🖼️ Received Image: baseball.png\n", "\n", "==> 🐙 Reasoning Steps from OctoTools (Deep Thinking...)\n", "\n", "==> 🔍 Step 0: Query Analysis\n", "\n", "### Query Analysis\n", "\n", "The query asks for the number of baseballs in the provided image. The image shows four blue buckets, each containing multiple baseballs. The main objective is to count the total number of baseballs in the image.\n", "\n", "### Required Skills\n", "\n", "1. **Object Detection**: The ability to identify and count the number of baseballs in the image.\n", "2. **Image Analysis**: The ability to analyze the image and understand the context of the objects present.\n", "\n", "### Relevant <PERSON><PERSON>\n", "\n", "1. **Object_Detector_Tool**: This tool can detect objects in an image using the Grounding DINO model. It can be used to detect baseballs in the image and count them. The tool can be executed with the following command:\n", "   ```python\n", "   execution = tool.execute(image=\"baseball.png\", labels=[\"baseball\"])\n", "   ```\n", "   This tool is relevant because it can accurately detect and count the baseballs in the image. However, it may have limitations in detecting small objects or objects with limited or specific attributes.\n", "\n", "2. **Generalist_Solution_Generator_Tool**: This tool can generate a response to the query based on the image and prompt. It can be used to provide a step-by-step reasoning for counting the baseballs. The tool can be executed with the following command:\n", "   ```python\n", "   execution = tool.execute(prompt=\"How many baseballs are there in the image?\", image=\"baseball.png\")\n", "   ```\n", "   This tool is relevant because it can provide a comprehensive response to the query. However, it may provide hallucinated or incorrect responses, so the results should be verified.\n", "\n", "### Additional Considerations\n", "\n", "- **Image Quality**: The quality of the image may affect the accuracy of the object detection. Ensure that the image is clear and well-lit.\n", "- **Object Attributes**: The tool may struggle with detecting objects with limited or specific attributes. In this case, the baseballs are clearly visible and have distinct features, so the tool should be able to detect them accurately.\n", "- **Verification**: The results from the Object_Detector_Tool should be verified to ensure accuracy. The Generalist_Solution_Generator_Tool can be used to provide a step-by-step reasoning for counting the baseballs, which can be used to verify the results.\n", "[Time]: 11.95s\n", "\n", "==> 🎯 Step 1: Action Prediction (Object_Detector_Tool)\n", "\n", "[Context]: Image path: \"baseball.png\"\n", "[Sub Goal]: Detect and count the number of baseballs in the image \"baseball.png\"\n", "[Tool]: Object_Detector_Tool\n", "[Time]: 4.69s\n", "\n", "==> 📝 Step 1: Command Generation (Object_Detector_Tool)\n", "\n", "[Analysis]: The tool requires an image path and a list of labels for object detection. In this case, we need to detect and count the number of baseballs in the image \"baseball.png\".\n", "[Explanation]: We pass the image path and a list containing \"baseball\" as the label to detect. The tool will return a list of detected objects with their scores, bounding boxes, and saved image paths.\n", "[Command]: execution = tool.execute(image=\"baseball.png\", labels=[\"baseball\"])\n", "[Time]: 2.74s\n", "\n", "==> 🛠️ Step 1: Command Execution (Object_Detector_Tool)\n", "\n", "[Result]:\n", "[\n", "    [\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.69,\n", "            \"box\": \"(632, 67, 689, 126)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_1.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.69,\n", "            \"box\": \"(614, 137, 671, 191)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_2.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.69,\n", "            \"box\": \"(557, 48, 614, 107)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_3.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.69,\n", "            \"box\": \"(535, 111, 591, 170)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_4.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(57, 289, 114, 346)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_5.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(364, 137, 421, 191)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_6.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(307, 49, 365, 107)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_7.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(132, 67, 189, 126)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_8.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(114, 137, 171, 191)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_9.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(35, 351, 91, 410)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_10.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(57, 48, 115, 107)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_11.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(35, 111, 91, 170)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_12.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(382, 67, 439, 126)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_13.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(285, 111, 342, 170)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_14.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.68,\n", "            \"box\": \"(114, 377, 171, 430)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_15.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.67,\n", "            \"box\": \"(132, 307, 189, 366)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_16.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.66,\n", "            \"box\": \"(586, 94, 643, 153)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_17.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.65,\n", "            \"box\": \"(86, 94, 143, 153)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_18.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.64,\n", "            \"box\": \"(86, 335, 143, 393)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_19.png\"\n", "        },\n", "        {\n", "            \"label\": \"baseball\",\n", "            \"confidence score\": 0.64,\n", "            \"box\": \"(336, 95, 392, 153)\",\n", "            \"saved_image_path\": \"solver_cache/baseball_baseball_20.png\"\n", "        }\n", "    ]\n", "]\n", "[Time]: 5.54s\n", "\n", "==> 🤖 Step 1: Context Verification\n", "\n", "[Analysis]: Explanation: \n", "The memory is complete and accurate enough to generate the final output. The Object_Detector_Tool was used to detect and count the number of baseballs in the image. The tool detected 20 baseballs with confidence scores ranging from 0.64 to 0.69. The results are consistent and provide a clear count of the baseballs in the image. There are no inconsistencies or ambiguities in the information provided, and the tool's limitations have been considered. The Generalist_Solution_Generator_Tool was not used, but it is not necessary in this case as the Object_Detector_Tool provided a clear and accurate count of the baseballs.\n", "\n", "Conclusion: STOP\n", "[Conclusion]: STOP ✅\n", "[Time]: 4.05s\n", "\n", "==> 🐙 Detailed Solution:\n", "\n", "Summary:\n", "The query asks for the total number of baseballs in the provided image. The image contains four buckets, each filled with baseballs. The Object_Detector_Tool was used to detect and count the number of baseballs in the image.\n", "\n", "Detailed Analysis:\n", "1. The Object_Detector_Tool was executed with the image \"baseball.png\" and the label \"baseball\" to detect and count the number of baseballs in the image.\n", "2. The tool detected 20 baseballs in the image, with confidence scores ranging from 0.64 to 0.69.\n", "3. The tool saved the detected baseballs in separate images for further analysis if needed.\n", "\n", "Key Findings:\n", "- The Object_Detector_Tool successfully detected and counted 20 baseballs in the image.\n", "- The confidence scores for the detected baseballs were relatively high, indicating a high level of accuracy in the detection.\n", "\n", "Answer to the Query:\n", "There are 20 baseballs in the image.\n", "\n", "Additional Insights:\n", "- The image contains four buckets, each filled with baseballs.\n", "- The Object_Detector_Tool was able to accurately detect and count the baseballs in the image.\n", "\n", "Conclusion:\n", "The query was successfully addressed by using the Object_Detector_Tool to detect and count the number of baseballs in the image. The tool detected 20 baseballs with high confidence scores, confirming the total number of baseballs in the image.\n", "\n", "==> 🐙 Final Answer:\n", "\n", "There are 20 baseballs in the image.\n", "\n", "[Total Time]: 37.5s\n", "\n", "==> ✅ Query Solved!\n"]}], "source": ["# Solve the user query\n", "output = solver.solve(question=\"How many baseballs are there?\", image_path=\"baseball.png\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Summary:\n", "The query asks for the total number of baseballs in the provided image. The image contains four buckets, each filled with baseballs. The Object_Detector_Tool was used to detect and count the number of baseballs in the image.\n", "\n", "Detailed Analysis:\n", "1. The Object_Detector_Tool was executed with the image \"baseball.png\" and the label \"baseball\" to detect and count the number of baseballs in the image.\n", "2. The tool detected 20 baseballs in the image, with confidence scores ranging from 0.64 to 0.69.\n", "3. The tool saved the detected baseballs in separate images for further analysis if needed.\n", "\n", "Key Findings:\n", "- The Object_Detector_Tool successfully detected and counted 20 baseballs in the image.\n", "- The confidence scores for the detected baseballs were relatively high, indicating a high level of accuracy in the detection.\n", "\n", "Answer to the Query:\n", "There are 20 baseballs in the image.\n", "\n", "Additional Insights:\n", "- The image contains four buckets, each filled with baseballs.\n", "- The Object_Detector_Tool was able to accurately detect and count the baseballs in the image.\n", "\n", "Conclusion:\n", "The query was successfully addressed by using the Object_Detector_Tool to detect and count the number of baseballs in the image. The tool detected 20 baseballs with high confidence scores, confirming the total number of baseballs in the image.\n"]}], "source": ["print(output[\"final_output\"])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 20 baseballs in the image.\n"]}], "source": ["print(output[\"direct_output\"])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step count: 1 step(s)\n", "Execution time: 17.02 seconds\n"]}], "source": ["print(f\"Step count: {output['step_count']} step(s)\")\n", "print(f\"Execution time: {output['execution_time']} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "octotools", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}