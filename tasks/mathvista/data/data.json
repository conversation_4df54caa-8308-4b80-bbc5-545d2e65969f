[{"pid": "1", "question": "When a spring does work on an object, we cannot find the work by simply multiplying the spring force by the object's displacement. The reason is that there is no one value for the force-it changes. However, we can split the displacement up into an infinite number of tiny parts and then approximate the force in each as being constant. Integration sums the work done in all those parts. Here we use the generic result of the integration.\r\n\r\nIn Figure, a cumin canister of mass $m=0.40 \\mathrm{~kg}$ slides across a horizontal frictionless counter with speed $v=0.50 \\mathrm{~m} / \\mathrm{s}$. It then runs into and compresses a spring of spring constant $k=750 \\mathrm{~N} / \\mathrm{m}$. When the canister is momentarily stopped by the spring, by what distance $d$ is the spring compressed?", "image": "images/1.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "1.2", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 720, "img_width": 1514, "language": "english", "skills": ["scientific reasoning"], "source": "SciBench", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: When a spring does work on an object, we cannot find the work by simply multiplying the spring force by the object's displacement. The reason is that there is no one value for the force-it changes. However, we can split the displacement up into an infinite number of tiny parts and then approximate the force in each as being constant. Integration sums the work done in all those parts. Here we use the generic result of the integration.\r\n\r\nIn Figure, a cumin canister of mass $m=0.40 \\mathrm{~kg}$ slides across a horizontal frictionless counter with speed $v=0.50 \\mathrm{~m} / \\mathrm{s}$. It then runs into and compresses a spring of spring constant $k=750 \\mathrm{~N} / \\mathrm{m}$. When the canister is momentarily stopped by the spring, by what distance $d$ is the spring compressed?"}, {"pid": "2", "question": "what is the total volume of the measuring cup?", "image": "images/2.jpg", "choices": null, "unit": "g", "precision": null, "answer": "1000", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 768, "img_width": 1024, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "TextVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: what is the total volume of the measuring cup? (Unit: g)"}, {"pid": "3", "question": "△ABC的两内角平分线OB、OC相交于点O，若∠A＝110°，则∠BOC＝（）", "image": "images/3.jpg", "choices": ["135°", "140°", "145°", "150°"], "unit": null, "precision": null, "answer": "145°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 60, "img_width": 131, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: △ABC的两内角平分线OB、OC相交于点O，若∠A＝110°，则∠BOC＝（）\nChoices:\n(A) 135°\n(B) 140°\n(C) 145°\n(D) 150°"}, {"pid": "4", "question": "Subtract all large rubber spheres. Subtract all big shiny cylinders. How many objects are left?", "image": "images/4.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["geometry reasoning", "arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all large rubber spheres. Subtract all big shiny cylinders. How many objects are left?"}, {"pid": "5", "question": "Find $m\\angle H$", "image": "images/5.jpg", "choices": ["97", "102", "107", "122"], "unit": null, "precision": null, "answer": "97", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 245, "img_width": 322, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find $m\\angle H$\nChoices:\n(A) 97\n(B) 102\n(C) 107\n(D) 122"}, {"pid": "6", "question": "As shown in the figure, if CB = 4.0, DB = 7.0, and D is the midpoint of AC, then the length of AC is ()", "image": "images/6.jpg", "choices": ["6cm", "7cm", "4cm", "5cm"], "unit": null, "precision": null, "answer": "6cm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 30, "img_width": 203, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, if CB = 4.0, DB = 7.0, and D is the midpoint of AC, then the length of AC is ()\nChoices:\n(A) 6cm\n(B) 7cm\n(C) 4cm\n(D) 5cm"}, {"pid": "7", "question": "What time is shown? Answer by typing a time word, not a number. It is (_) after eight.", "image": "images/7.jpg", "choices": ["half", "quarter", "o'clock", "quarter to", "quarter past"], "unit": null, "precision": null, "answer": "quarter", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 204, "img_width": 203, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What time is shown? Answer by typing a time word, not a number. It is (_) after eight.\nChoices:\n(A) half\n(B) quarter\n(C) o'clock\n(D) quarter to\n(E) quarter past"}, {"pid": "8", "question": "Is the number of tiny gray bicycles that are on the left side of the brown metal sedan greater than the number of things that are to the left of the tiny green bicycle?", "image": "images/8.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of tiny gray bicycles that are on the left side of the brown metal sedan greater than the number of things that are to the left of the tiny green bicycle?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "9", "question": "如图是一株美丽的勾股树，其中所有四边形都是正方形，所有的三角形都是直角三角形，若正方形A、B的面积分别为5、3，则最大正方形C的面积是（）", "image": "images/9.jpg", "choices": ["15", "13", "11", "8"], "unit": null, "precision": null, "answer": "8", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 155, "img_width": 134, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图是一株美丽的勾股树，其中所有四边形都是正方形，所有的三角形都是直角三角形，若正方形A、B的面积分别为5、3，则最大正方形C的面积是（）\nChoices:\n(A) 15\n(B) 13\n(C) 11\n(D) 8"}, {"pid": "10", "question": "Which object comes next?", "image": "images/10.jpg", "choices": ["A", "B", "C", "D", "E"], "unit": null, "precision": null, "answer": "E", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 418, "img_width": 376, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which object comes next?\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E"}, {"pid": "11", "question": "Subtract all red things. Subtract all tiny matte balls. How many objects are left?", "image": "images/11.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all red things. Subtract all tiny matte balls. How many objects are left?"}, {"pid": "12", "question": "Are there fewer metallic fighters than rubber objects?", "image": "images/12.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Are there fewer metallic fighters than rubber objects?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "13", "question": "How many objects are preferred by more than 90 percent of people in at least one category?", "image": "images/13.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many objects are preferred by more than 90 percent of people in at least one category?"}, {"pid": "14", "question": "Is the number of tiny objects that are behind the small metal jet less than the number of tiny things left of the tiny sedan?", "image": "images/14.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of tiny objects that are behind the small metal jet less than the number of tiny things left of the tiny sedan?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "15", "question": "Which organism with be most affected if algae was eliminated?", "image": "images/15.jpg", "choices": ["Tilapia", "Common water flea", "Great diving beetle", "Tadpole"], "unit": null, "precision": null, "answer": "Common water flea", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 232, "img_width": 400, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which organism with be most affected if algae was eliminated?\nChoices:\n(A) Tilapia\n(B) Common water flea\n(C) Great diving beetle\n(D) Tadpole"}, {"pid": "16", "question": "How many items sold less than 5 units in at least one store?", "image": "images/16.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many items sold less than 5 units in at least one store?"}, {"pid": "17", "question": "如图，在Rt△ABC中，∠ACB＝90°，D是AB的中点，AB＝10，则CD的长为（）", "image": "images/17.jpg", "choices": ["5", "6", "8", "10"], "unit": null, "precision": null, "answer": "5", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 172, "img_width": 125, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，在Rt△ABC中，∠ACB＝90°，D是AB的中点，AB＝10，则CD的长为（）\nChoices:\n(A) 5\n(B) 6\n(C) 8\n(D) 10"}, {"pid": "18", "question": "The passage below describes an experiment. Read the passage and then follow the instructions below.\n\n<PERSON> applied a thin layer of wax to the underside of her snowboard and rode the board straight down a hill. Then, she removed the wax and rode the snowboard straight down the hill again. She repeated the rides four more times, alternating whether she rode with a thin layer of wax on the board or not. Her friend <PERSON> timed each ride. <PERSON> and <PERSON> calculated the average time it took to slide straight down the hill on the snowboard with wax compared to the average time on the snowboard without wax.\nFigure: snowboarding down a hill. Identify the question that <PERSON> and <PERSON>'s experiment can best answer.", "image": "images/18.jpg", "choices": ["Does <PERSON>'s snowboard slide down a hill in less time when it has a thin layer of wax or a thick layer of wax?", "Does <PERSON>'s snowboard slide down a hill in less time when it has a layer of wax or when it does not have a layer of wax?"], "unit": null, "precision": null, "answer": "Does <PERSON>'s snowboard slide down a hill in less time when it has a layer of wax or when it does not have a layer of wax?", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "elementary school", "img_height": 232, "img_width": 302, "language": "english", "skills": ["scientific reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The passage below describes an experiment. Read the passage and then follow the instructions below.\n\n<PERSON> applied a thin layer of wax to the underside of her snowboard and rode the board straight down a hill. Then, she removed the wax and rode the snowboard straight down the hill again. She repeated the rides four more times, alternating whether she rode with a thin layer of wax on the board or not. Her friend <PERSON> timed each ride. <PERSON> and <PERSON> calculated the average time it took to slide straight down the hill on the snowboard with wax compared to the average time on the snowboard without wax.\nFigure: snowboarding down a hill. Identify the question that <PERSON> and <PERSON>'s experiment can best answer.\nChoices:\n(A) Does <PERSON>'s snowboard slide down a hill in less time when it has a thin layer of wax or a thick layer of wax?\n(B) Does <PERSON>'s snowboard slide down a hill in less time when it has a layer of wax or when it does not have a layer of wax?"}, {"pid": "19", "question": "what is the highest amount this class measures?", "image": "images/19.jpg", "choices": null, "unit": null, "precision": null, "answer": "400", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 684, "img_width": 1024, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "TextVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: what is the highest amount this class measures?"}, {"pid": "20", "question": "Is the sum of smallest two bar is greater then the largest bar?", "image": "images/20.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 600, "img_width": 850, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the sum of smallest two bar is greater then the largest bar?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "21", "question": "Fill in the blank to describe the model. The model has 4 dots divided into 2 equal groups. There are (_) dots in each group.", "image": "images/21.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 204, "img_width": 418, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Fill in the blank to describe the model. The model has 4 dots divided into 2 equal groups. There are (_) dots in each group."}, {"pid": "22", "question": "How many people in the image were born after the end of World War II?", "image": "images/22.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 785, "img_width": 555, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many people in the image were born after the end of World War II?"}, {"pid": "23", "question": "The derivative of f(x) at x=2 is ____ that at x=5", "image": "images/23.jpg", "choices": ["larger than", "equal to", "smaller than"], "unit": null, "precision": null, "answer": "equal to", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 393, "img_width": 552, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The derivative of f(x) at x=2 is ____ that at x=5\nChoices:\n(A) larger than\n(B) equal to\n(C) smaller than"}, {"pid": "24", "question": "Is Periwinkle the maximum?", "image": "images/24.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 709, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is <PERSON>iwinkle the maximum?\nChoices:\n(A) yes\n(B) no"}, {"pid": "25", "question": "Is Medium Periwinkle the smoothest?", "image": "images/25.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 400, "img_width": 770, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Medium Periwinkle the smoothest?\nChoices:\n(A) yes\n(B) no"}, {"pid": "26", "question": "Is Black greater than Deep Sky Blue?", "image": "images/26.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 761, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Black greater than Deep Sky Blue?\nChoices:\n(A) yes\n(B) no"}, {"pid": "27", "question": "What is the age gap between these two people in image?", "image": "images/27.jpg", "choices": null, "unit": "years", "precision": null, "answer": "11", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 1752, "img_width": 2628, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "28", "question": "$\\overline{AB}$ is a diameter, $AC=8$ inches, and $BC=15$ inches. Find the radius of the circle.", "image": "images/28.jpg", "choices": ["7.5", "8", "8.5", "17"], "unit": null, "precision": null, "answer": "8.5", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 431, "img_width": 519, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: $\\overline{AB}$ is a diameter, $AC=8$ inches, and $BC=15$ inches. Find the radius of the circle.\nChoices:\n(A) 7.5\n(B) 8\n(C) 8.5\n(D) 17"}, {"pid": "29", "question": "What is the age gap between these two people in image?", "image": "images/29.jpg", "choices": null, "unit": "years", "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 440, "img_width": 670, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "30", "question": "As shown in the figure, the two chords AB and CD in the circle intersect at E, ∠D = 35.0, ∠AEC = 105.0, then ∠C = ()", "image": "images/30.jpg", "choices": ["60°", "70°", "80°", "85°"], "unit": null, "precision": null, "answer": "70°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 113, "img_width": 117, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, the two chords AB and CD in the circle intersect at E, ∠D = 35.0, ∠AEC = 105.0, then ∠C = ()\nChoices:\n(A) 60°\n(B) 70°\n(C) 80°\n(D) 85°"}, {"pid": "31", "question": "Are there more big red rubber double buss in front of the large red double bus than big green things?", "image": "images/31.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Are there more big red rubber double buss in front of the large red double bus than big green things?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "32", "question": "如图，在ABCD中，AB＝AC，∠CAB＝40°，则∠D的度数是（）", "image": "images/32.jpg", "choices": ["40°", "50°", "60°", "70°"], "unit": null, "precision": null, "answer": "70°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 100, "img_width": 168, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，在ABCD中，AB＝AC，∠CAB＝40°，则∠D的度数是（）\nChoices:\n(A) 40°\n(B) 50°\n(C) 60°\n(D) 70°"}, {"pid": "33", "question": "Use a sector paper sheet with a central angle of 120.0 and a radius of 6.0 to roll into a conical bottomless paper cap (as shown in the picture), then the bottom perimeter of the paper cap is ()", "image": "images/33.jpg", "choices": ["2πcm", "3πcm", "4πcm", "5πcm"], "unit": null, "precision": null, "answer": "4πcm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 95, "img_width": 331, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Use a sector paper sheet with a central angle of 120.0 and a radius of 6.0 to roll into a conical bottomless paper cap (as shown in the picture), then the bottom perimeter of the paper cap is ()\nChoices:\n(A) 2πcm\n(B) 3πcm\n(C) 4πcm\n(D) 5πcm"}, {"pid": "34", "question": "Is this function continuous at each point?", "image": "images/34.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 479, "img_width": 479, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is this function continuous at each point?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "35", "question": "如图，AB是⊙O的直径，EF，EB是⊙O的弦，点E是FEB的中点，EF与AB交于点C，连接OF，若∠AOF＝40°，则∠F的度数是（）", "image": "images/35.jpg", "choices": ["20°", "35°", "40°", "55°"], "unit": null, "precision": null, "answer": "35°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 141, "img_width": 151, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，AB是⊙O的直径，EF，EB是⊙O的弦，点E是FEB的中点，EF与AB交于点C，连接OF，若∠AOF＝40°，则∠F的度数是（）\nChoices:\n(A) 20°\n(B) 35°\n(C) 40°\n(D) 55°"}, {"pid": "36", "question": "What is the age gap between these two people in image?", "image": "images/36.jpg", "choices": null, "unit": "years", "precision": null, "answer": "9", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 480, "img_width": 800, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "37", "question": "What is the limit as x approaches -1?", "image": "images/37.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 410, "img_width": 408, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the limit as x approaches -1?"}, {"pid": "38", "question": "How many bars have values smaller than 6?", "image": "images/38.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many bars have values smaller than 6?"}, {"pid": "39", "question": "Is this function odd or even?", "image": "images/39.jpg", "choices": ["odd", "even"], "unit": null, "precision": null, "answer": "odd", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 304, "img_width": 433, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is this function odd or even?\nChoices:\n(A) odd\n(B) even"}, {"pid": "40", "question": "Subtract all brown blocks. Subtract all large blue rubber things. How many objects are left?", "image": "images/40.jpg", "choices": null, "unit": null, "precision": null, "answer": "7", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all brown blocks. Subtract all large blue rubber things. How many objects are left?"}, {"pid": "41", "question": "What is the age gap between these two people in image?", "image": "images/41.jpg", "choices": null, "unit": "years", "precision": null, "answer": "7", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 3491, "img_width": 5236, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "42", "question": "What is the age gap between these two people in image?", "image": "images/42.jpg", "choices": null, "unit": "years", "precision": null, "answer": "8", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 539, "img_width": 401, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "43", "question": "Use the graph to answer the question below. Which month is the wettest on average in Christchurch?", "image": "images/43.jpg", "choices": ["August", "April", "May"], "unit": null, "precision": null, "answer": "May", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "elementary school", "img_height": 323, "img_width": 449, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Use the graph to answer the question below. Which month is the wettest on average in Christchurch?\nChoices:\n(A) August\n(B) April\n(C) May"}, {"pid": "44", "question": "<PERSON> wants to buy 4 kilograms of oval beads and 5 kilograms of star-shaped beads. How much will he spend? (Unit: $)", "image": "images/44.jpg", "choices": null, "unit": null, "precision": null, "answer": "18", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 226, "img_width": 305, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: <PERSON> wants to buy 4 kilograms of oval beads and 5 kilograms of star-shaped beads. How much will he spend? (Unit: $)"}, {"pid": "45", "question": "An administrator at the Department of Motor Vehicles (DMV) tracked the average wait time from month to month. According to the table, what was the rate of change between August and September? (Unit: minutes per month)", "image": "images/45.jpg", "choices": null, "unit": null, "precision": null, "answer": "-3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 218, "img_width": 273, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: An administrator at the Department of Motor Vehicles (DMV) tracked the average wait time from month to month. According to the table, what was the rate of change between August and September? (Unit: minutes per month)"}, {"pid": "46", "question": "What would happen to the population of adult spiders if predator ate all the spider eggs?", "image": "images/46.jpg", "choices": ["Adult spider population would remain the same", "Adult spider population would double.", "Adults spider population would decrease", "Adult spider population would increase."], "unit": null, "precision": null, "answer": "Adults spider population would decrease", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 829, "img_width": 1024, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What would happen to the population of adult spiders if predator ate all the spider eggs?\nChoices:\n(A) Adult spider population would remain the same\n(B) Adult spider population would double.\n(C) Adults spider population would decrease\n(D) Adult spider population would increase."}, {"pid": "47", "question": "Subtract all rubber balls. Subtract all yellow shiny things. How many objects are left?", "image": "images/47.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all rubber balls. Subtract all yellow shiny things. How many objects are left?"}, {"pid": "48", "question": "Find $m \\angle 3$.", "image": "images/48.jpg", "choices": ["28", "38", "52", "62"], "unit": null, "precision": null, "answer": "38", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 426, "img_width": 596, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find $m \\angle 3$.\nChoices:\n(A) 28\n(B) 38\n(C) 52\n(D) 62"}, {"pid": "49", "question": "What is the sum of the digits on either end of the sign in the corner?", "image": "images/49.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 476, "img_width": 626, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of the digits on either end of the sign in the corner?"}, {"pid": "50", "question": "Based on the food web, what would likely happen if the number of large roach would decrease?", "image": "images/50.jpg", "choices": ["The population of steelheads would decrease.", "The population of stickleback fry would increase.", "The population of predatory insects would increase.", "The population of predatory insects would decrease."], "unit": null, "precision": null, "answer": "The population of predatory insects would decrease.", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 600, "img_width": 633, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Based on the food web, what would likely happen if the number of large roach would decrease?\nChoices:\n(A) The population of steelheads would decrease.\n(B) The population of stickleback fry would increase.\n(C) The population of predatory insects would increase.\n(D) The population of predatory insects would decrease."}, {"pid": "51", "question": "Is the number of gray rubber objects in front of the small yellow aeroplane greater than the number of big cyan matte fighters?", "image": "images/51.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of gray rubber objects in front of the small yellow aeroplane greater than the number of big cyan matte fighters?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "52", "question": "Subtract all big red metallic spheres. Subtract all big brown matte things. How many objects are left?", "image": "images/52.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all big red metallic spheres. Subtract all big brown matte things. How many objects are left?"}, {"pid": "53", "question": "What is the age gap between these two people in image?", "image": "images/53.jpg", "choices": null, "unit": "years", "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 593, "img_width": 800, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "54", "question": "In the figure above, the ratio of the length of line AB to the length of line AC is 2 : 5. If AC = 25, what is the length of line AB?", "image": "images/54.jpg", "choices": ["8", "10", "15", "18", "20"], "unit": null, "precision": null, "answer": "10", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 310, "img_width": 433, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GEOS", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In the figure above, the ratio of the length of line AB to the length of line AC is 2 : 5. If AC = 25, what is the length of line AB?\nChoices:\n(A) 8\n(B) 10\n(C) 15\n(D) 18\n(E) 20"}, {"pid": "55", "question": "如图，一块直角三角板60°的角的顶点A与直角顶点C分别在两平行线FG，DE上，斜边AB平分∠CAG，交直线DE于点H，则∠BCH的大小为（）", "image": "images/55.jpg", "choices": ["60°", "45°", "30°", "25°"], "unit": null, "precision": null, "answer": "30°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 125, "img_width": 175, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，一块直角三角板60°的角的顶点A与直角顶点C分别在两平行线FG，DE上，斜边AB平分∠CAG，交直线DE于点H，则∠BCH的大小为（）\nChoices:\n(A) 60°\n(B) 45°\n(C) 30°\n(D) 25°"}, {"pid": "56", "question": "What is the perimeter of the rectangle?", "image": "images/56.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "elementary school", "img_height": 295, "img_width": 202, "language": "english", "skills": ["geometry reasoning", "arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the perimeter of the rectangle?"}, {"pid": "57", "question": "Subtract all small balls. Subtract all blue rubber things. How many objects are left?", "image": "images/57.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all small balls. Subtract all blue rubber things. How many objects are left?"}, {"pid": "58", "question": "Does Firebrick have the maximum area under the curve?", "image": "images/58.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 400, "img_width": 760, "language": "english", "skills": ["algebraic reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Does Firebrick have the maximum area under the curve?\nChoices:\n(A) yes\n(B) no"}, {"pid": "59", "question": "As shown in the figure, AB is the diameter of ⊙O, CD is the chord of ⊙O, ∠ADC = 26.0, then the degree of ∠CAB is ()", "image": "images/59.jpg", "choices": ["26°", "74°", "64°", "54°"], "unit": null, "precision": null, "answer": "64°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 146, "img_width": 157, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, AB is the diameter of ⊙O, CD is the chord of ⊙O, ∠ADC = 26.0, then the degree of ∠CAB is ()\nChoices:\n(A) 26°\n(B) 74°\n(C) 64°\n(D) 54°"}, {"pid": "60", "question": "What is the age gap between these two people in image?", "image": "images/60.jpg", "choices": null, "unit": "years", "precision": null, "answer": "22", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 381, "img_width": 477, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "61", "question": "Is <PERSON> the roughest?", "image": "images/61.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 400, "img_width": 427, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is <PERSON> the roughest?\nChoices:\n(A) yes\n(B) no"}, {"pid": "62", "question": "如图，E，F分别是菱形ABCD的边AB，AD的中点，且AB＝5，AC＝6．则EF的长为（）", "image": "images/62.jpg", "choices": ["4", "5", "5.5", "6"], "unit": null, "precision": null, "answer": "4", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 138, "img_width": 160, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，E，F分别是菱形ABCD的边AB，AD的中点，且AB＝5，AC＝6．则EF的长为（）\nChoices:\n(A) 4\n(B) 5\n(C) 5.5\n(D) 6"}, {"pid": "63", "question": "Subtract all red matte cubes. Subtract all small green metal objects. How many objects are left?", "image": "images/63.jpg", "choices": null, "unit": null, "precision": null, "answer": "7", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all red matte cubes. Subtract all small green metal objects. How many objects are left?"}, {"pid": "64", "question": "The diagrams below show two pure samples of gas in identical closed, rigid containers. Each colored ball represents one gas particle. Both samples have the same number of particles. Compare the average kinetic energies of the particles in each sample. Which sample has the higher temperature?", "image": "images/64.jpg", "choices": ["neither; the samples have the same temperature", "sample A", "sample B"], "unit": null, "precision": null, "answer": "sample A", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "elementary school", "img_height": 405, "img_width": 550, "language": "english", "skills": ["scientific reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The diagrams below show two pure samples of gas in identical closed, rigid containers. Each colored ball represents one gas particle. Both samples have the same number of particles. Compare the average kinetic energies of the particles in each sample. Which sample has the higher temperature?\nChoices:\n(A) neither; the samples have the same temperature\n(B) sample A\n(C) sample B"}, {"pid": "65", "question": "is f(3) > 0?", "image": "images/65.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 325, "img_width": 327, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: is f(3) > 0?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "66", "question": "Choose the answer for the missing picture.", "image": "images/66.jpg", "choices": ["A", "B", "C", "D", "E", "F"], "unit": null, "precision": null, "answer": "A", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 562, "img_width": 320, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Choose the answer for the missing picture.\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E\n(F) F"}, {"pid": "67", "question": "What is the perimeter of the square?", "image": "images/67.jpg", "choices": null, "unit": null, "precision": null, "answer": "16", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "elementary school", "img_height": 292, "img_width": 320, "language": "english", "skills": ["geometry reasoning", "arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the perimeter of the square?"}, {"pid": "68", "question": "将一根长度为16cm自然伸直的弹性皮筋AB两端固定在水平的桌面上，然后把中点C竖直向上拉升6cm至D点（如图），则该弹性皮筋被拉长了（）", "image": "images/68.jpg", "choices": ["2cm", "4cm", "6cm", "8cm"], "unit": null, "precision": null, "answer": "4cm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 84, "img_width": 252, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 将一根长度为16cm自然伸直的弹性皮筋AB两端固定在水平的桌面上，然后把中点C竖直向上拉升6cm至D点（如图），则该弹性皮筋被拉长了（）\nChoices:\n(A) 2cm\n(B) 4cm\n(C) 6cm\n(D) 8cm"}, {"pid": "69", "question": "Subtract all big matte balls. Subtract all green rubber objects. How many objects are left?", "image": "images/69.jpg", "choices": null, "unit": null, "precision": null, "answer": "7", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all big matte balls. Subtract all green rubber objects. How many objects are left?"}, {"pid": "70", "question": "What is the age gap between these two people in image?", "image": "images/70.jpg", "choices": null, "unit": "years", "precision": null, "answer": "8", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 2600, "img_width": 2266, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "71", "question": "What is the perimeter of the rectangle?", "image": "images/71.jpg", "choices": null, "unit": null, "precision": null, "answer": "18", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "elementary school", "img_height": 292, "img_width": 187, "language": "english", "skills": ["geometry reasoning", "arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the perimeter of the rectangle?"}, {"pid": "72", "question": "A real estate agent drove around the neighborhood and counted the number of houses on each block. How many blocks have exactly 36 houses? (Unit: blocks)", "image": "images/72.jpg", "choices": null, "unit": null, "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 136, "img_width": 197, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: A real estate agent drove around the neighborhood and counted the number of houses on each block. How many blocks have exactly 36 houses? (Unit: blocks)"}, {"pid": "73", "question": "Complete the matrix.", "image": "images/73.jpg", "choices": ["A", "B", "C", "D", "E", "F"], "unit": null, "precision": null, "answer": "D", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 654, "img_width": 387, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Complete the matrix.\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E\n(F) F"}, {"pid": "74", "question": "What is the difference of largest and smallest bar?", "image": "images/74.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "47.6", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 600, "img_width": 850, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: What is the difference of largest and smallest bar?"}, {"pid": "75", "question": "Is Sky Blue less than Web Maroon?", "image": "images/75.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "pie chart", "grade": "daily life", "img_height": 400, "img_width": 532, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Sky Blue less than Web Maroon?\nChoices:\n(A) yes\n(B) no"}, {"pid": "76", "question": "What happens to fish if pelicans increase?", "image": "images/76.jpg", "choices": ["decrease", "nothing", "increase", "none of the above"], "unit": null, "precision": null, "answer": "decrease", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 947, "img_width": 850, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What happens to fish if pelicans increase?\nChoices:\n(A) decrease\n(B) nothing\n(C) increase\n(D) none of the above"}, {"pid": "77", "question": "Which year showed the largest difference in the data points between the two lines", "image": "images/77.jpg", "choices": null, "unit": null, "precision": null, "answer": "2019", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Which year showed the largest difference in the data points between the two lines"}, {"pid": "78", "question": "Find the missing value.", "image": "images/78.jpg", "choices": null, "unit": null, "precision": null, "answer": "8", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 394, "img_width": 1062, "language": "english", "skills": ["logical reasoning", "arithmetic reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Find the missing value."}, {"pid": "79", "question": "As shown in the figure, points A, B, C, and D are on circle O, and point E is on the extended line of AD. If ∠ABC = 60.0, then the degree of ∠CDE is ()", "image": "images/79.jpg", "choices": ["30°", "45°", "60°", "70°"], "unit": null, "precision": null, "answer": "60°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 104, "img_width": 123, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, points A, B, C, and D are on circle O, and point E is on the extended line of AD. If ∠ABC = 60.0, then the degree of ∠CDE is ()\nChoices:\n(A) 30°\n(B) 45°\n(C) 60°\n(D) 70°"}, {"pid": "80", "question": "According to the food web, what will happen if all the algae died due to pesticides?", "image": "images/80.jpg", "choices": ["Crabs and limpets will decrease", "Dolphins will increase", "Sea gulls will become extinct", "Star fish will increase"], "unit": null, "precision": null, "answer": "Crabs and limpets will decrease", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 199, "img_width": 372, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: According to the food web, what will happen if all the algae died due to pesticides?\nChoices:\n(A) Crabs and limpets will decrease\n(B) Dolphins will increase\n(C) Sea gulls will become extinct\n(D) Star fish will increase"}, {"pid": "81", "question": "What is the value of r at theta=3*pi/2?", "image": "images/81.jpg", "choices": null, "unit": null, "precision": null, "answer": "-1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 460, "img_width": 616, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the value of r at theta=3*pi/2?"}, {"pid": "82", "question": "A square is inscribed in a circle of area 18$\\pi$ square units. Find the length of a side of the square.", "image": "images/82.jpg", "choices": ["3", "3 \\sqrt 2", "6", "6 \\sqrt 2"], "unit": null, "precision": null, "answer": "6", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 202, "img_width": 200, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: A square is inscribed in a circle of area 18$\\pi$ square units. Find the length of a side of the square.\nChoices:\n(A) 3\n(B) 3 \\sqrt 2\n(C) 6\n(D) 6 \\sqrt 2"}, {"pid": "83", "question": "Is the number of shiny buss less than the number of matte things?", "image": "images/83.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of shiny buss less than the number of matte things?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "84", "question": "ABCD is a square. Inscribed Circle center is O. Find the the angle of ∠AMK. Return the numeric value.", "image": "images/84.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "130.9", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 1220, "img_width": 1194, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "TheoremQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: ABCD is a square. Inscribed Circle center is O. Find the the angle of ∠AMK. Return the numeric value."}, {"pid": "85", "question": "How many countries have people working for more than 35 hours over the years?", "image": "images/85.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 600, "img_width": 850, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many countries have people working for more than 35 hours over the years?"}, {"pid": "86", "question": "Which model has the highest Acc score when Pretrain Loss is equal to 1.80?", "image": "images/86.jpg", "choices": ["ICL", "SFT", "SFT 1/8", "RFT k=100", "RFT k=25", "RET k=6", "RFT U13B"], "unit": null, "precision": null, "answer": "RFT U13B", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "line plot", "grade": "college", "img_height": 1046, "img_width": 1734, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which model has the highest Acc score when Pretrain Loss is equal to 1.80?\nChoices:\n(A) ICL\n(B) SFT\n(C) SFT 1/8\n(D) RFT k=100\n(E) RFT k=25\n(F) RET k=6\n(G) RFT U13B"}, {"pid": "87", "question": "Look at the table. Then answer the question. At a price of $790, is there a shortage or a surplus?'", "image": "images/87.jpg", "choices": ["shortage", "surplus"], "unit": null, "precision": null, "answer": "surplus", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 187, "img_width": 353, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Look at the table. Then answer the question. At a price of $790, is there a shortage or a surplus?'\nChoices:\n(A) shortage\n(B) surplus"}, {"pid": "88", "question": "A square is tangent to a line at point P in the figure above. What is the value of x?", "image": "images/88.jpg", "choices": null, "unit": null, "precision": null, "answer": "30", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 277, "img_width": 442, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GEOS", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: A square is tangent to a line at point P in the figure above. What is the value of x?"}, {"pid": "89", "question": "How many miles per gallon do an average city bus get?", "image": "images/89.jpg", "choices": null, "unit": null, "precision": null, "answer": "25", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 384, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many miles per gallon do an average city bus get?"}, {"pid": "90", "question": "Subtract all yellow matte blocks. Subtract all tiny brown cylinders. How many objects are left?", "image": "images/90.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all yellow matte blocks. Subtract all tiny brown cylinders. How many objects are left?"}, {"pid": "91", "question": "Is the number of brown suvs less than the number of brown rubber school buss?", "image": "images/91.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of brown suvs less than the number of brown rubber school buss?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "92", "question": "Do the windows have a geometric shape that most houses have?", "image": "images/92.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 375, "img_width": 500, "language": "english", "skills": ["numeric commonsense", "geometry reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Do the windows have a geometric shape that most houses have?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "93", "question": "What's the computing and wirless total for semiconductor demand in 2014?", "image": "images/93.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "197.3", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: What's the computing and wirless total for semiconductor demand in 2014?"}, {"pid": "94", "question": "如图，D为△ABC内一点，CD平分∠ACB，BD⊥CD，∠A＝∠ABD，若∠DBC＝54°，则∠A的度数为（）", "image": "images/94.jpg", "choices": ["36°", "44°", "27°", "54°"], "unit": null, "precision": null, "answer": "27°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 74, "img_width": 160, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，D为△ABC内一点，CD平分∠ACB，BD⊥CD，∠A＝∠ABD，若∠DBC＝54°，则∠A的度数为（）\nChoices:\n(A) 36°\n(B) 44°\n(C) 27°\n(D) 54°"}, {"pid": "95", "question": "As shown in the figure, the straight lines AB and CD intersect at point O, OD bisects ∠AOE, ∠BOC = 50.0, then ∠EOB = ()", "image": "images/95.jpg", "choices": ["50°", "60°", "70°", "80°"], "unit": null, "precision": null, "answer": "80°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 118, "img_width": 162, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, the straight lines AB and CD intersect at point O, OD bisects ∠AOE, ∠BOC = 50.0, then ∠EOB = ()\nChoices:\n(A) 50°\n(B) 60°\n(C) 70°\n(D) 80°"}, {"pid": "96", "question": "How many times Dissatisfied more than satisfied?", "image": "images/96.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "3.9", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "pie chart", "grade": "daily life", "img_height": 328, "img_width": 186, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: How many times Dissatisfied more than satisfied?"}, {"pid": "97", "question": "How many algorithms have accuracies higher than 9?", "image": "images/97.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many algorithms have accuracies higher than 9?"}, {"pid": "98", "question": "Find the value of the square in the figure.", "image": "images/98.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 506, "img_width": 900, "language": "english", "skills": ["logical reasoning", "algebraic reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Find the value of the square in the figure."}, {"pid": "99", "question": "Which cat is larger?", "image": "images/99.jpg", "choices": ["white five", "white three", "white four", "white one", "white two"], "unit": null, "precision": null, "answer": "white one", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "abstract scene", "grade": "daily life", "img_height": 400, "img_width": 700, "language": "english", "skills": ["arithmetic reasoning"], "source": "VQA-AS", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which cat is larger?\nChoices:\n(A) white five\n(B) white three\n(C) white four\n(D) white one\n(E) white two"}, {"pid": "100", "question": "What is the sum of all the values in the ruling group?", "image": "images/100.jpg", "choices": null, "unit": null, "precision": null, "answer": "12", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of all the values in the ruling group?"}, {"pid": "101", "question": "Which shape is most erect?", "image": "images/101.jpg", "choices": ["Lanceolate", "Heart-shaped", "Linear", "Spatulate"], "unit": null, "precision": null, "answer": "Linear", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 1204, "img_width": 376, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which shape is most erect?\nChoices:\n(A) Lanceolate\n(B) Heart-shaped\n(C) Linear\n(D) Spatulate"}, {"pid": "102", "question": "The shape is made of unit squares. What is the area of the shape?", "image": "images/102.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 156, "img_width": 106, "language": "english", "skills": ["geometry reasoning", "arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: The shape is made of unit squares. What is the area of the shape?"}, {"pid": "103", "question": "Subtract all small purple matte blocks. Subtract all blocks. How many objects are left?", "image": "images/103.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all small purple matte blocks. Subtract all blocks. How many objects are left?"}, {"pid": "104", "question": "What is the fraction of females facing the camera?", "image": "images/104.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "0.8", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 427, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: What is the fraction of females facing the camera?"}, {"pid": "105", "question": "Does Dark Violet have the minimum area under the curve?", "image": "images/105.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 400, "img_width": 727, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Does Dark Violet have the minimum area under the curve?\nChoices:\n(A) yes\n(B) no"}, {"pid": "106", "question": "How many bars have values smaller than 1?", "image": "images/106.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many bars have values smaller than 1?"}, {"pid": "107", "question": "What time is shown? Answer by typing a time word, not a number. It is (_) past six.", "image": "images/107.jpg", "choices": ["half", "quarter", "o'clock", "quarter to", "quarter past"], "unit": null, "precision": null, "answer": "quarter", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 204, "img_width": 203, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What time is shown? Answer by typing a time word, not a number. It is (_) past six.\nChoices:\n(A) half\n(B) quarter\n(C) o'clock\n(D) quarter to\n(E) quarter past"}, {"pid": "108", "question": "Find out the average of the bottom two countries ??", "image": "images/108.jpg", "choices": null, "unit": null, "precision": 2.0, "answer": "51.04", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 600, "img_width": 850, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with two decimal places and provide the final value, e.g., 1.23, 1.34, 1.45, at the end.\nQuestion: Find out the average of the bottom two countries ??"}, {"pid": "109", "question": "Subtract all tiny balls. Subtract all green metallic things. How many objects are left?", "image": "images/109.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all tiny balls. Subtract all green metallic things. How many objects are left?"}, {"pid": "110", "question": "Is the sum of two lowest bar is greater then the largest bar?", "image": "images/110.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 600, "img_width": 850, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the sum of two lowest bar is greater then the largest bar?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "111", "question": "Subtract all big gray matte things. Subtract all small metallic cylinders. How many objects are left?", "image": "images/111.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all big gray matte things. Subtract all small metallic cylinders. How many objects are left?"}, {"pid": "112", "question": "Is the number of big cyan airliners less than the number of gray shiny utility bikes?", "image": "images/112.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of big cyan airliners less than the number of gray shiny utility bikes?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "113", "question": "How many baseballs are there?", "image": "images/113.jpg", "choices": null, "unit": null, "precision": null, "answer": "20", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 458, "img_width": 721, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many baseballs are there?"}, {"pid": "114", "question": "In the figure, KL is tangent to $\\odot M$ at K. Find the value of x.", "image": "images/114.jpg", "choices": ["6.00", "9.45", "18.9", "37.8"], "unit": null, "precision": null, "answer": "9.45", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 273, "img_width": 347, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In the figure, KL is tangent to $\\odot M$ at K. Find the value of x.\nChoices:\n(A) 6.00\n(B) 9.45\n(C) 18.9\n(D) 37.8"}, {"pid": "115", "question": "What is the age gap between these two people in image?", "image": "images/115.jpg", "choices": null, "unit": "years", "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 1079, "img_width": 826, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "116", "question": "Which leaf has the most veins?", "image": "images/116.jpg", "choices": ["Acuminate", "Truncate", "Mucronate", "Acute"], "unit": null, "precision": null, "answer": "Acuminate", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 187, "img_width": 350, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which leaf has the most veins?\nChoices:\n(A) Acuminate\n(B) Truncate\n(C) Mucronate\n(D) Acute"}, {"pid": "117", "question": "What is the range of this function?", "image": "images/117.jpg", "choices": ["[0, 2]", "[3, 2]", "[2, 4]", "[-3, 4]"], "unit": null, "precision": null, "answer": "[0, 2]", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 356, "img_width": 460, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What is the range of this function?\nChoices:\n(A) [0, 2]\n(B) [3, 2]\n(C) [2, 4]\n(D) [-3, 4]"}, {"pid": "118", "question": "What is the maximum value of this function?", "image": "images/118.jpg", "choices": null, "unit": null, "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 296, "img_width": 600, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the maximum value of this function?"}, {"pid": "119", "question": "As shown in the figure, P is a point outside ⊙O, PA and PB intersect ⊙O at two points C and D respectively. It is known that the central angles of ⁀AB and ⁀CD are 90.0 and 50.0 respectively, then ∠P = ()", "image": "images/119.jpg", "choices": ["45°", "40°", "25°", "20°"], "unit": null, "precision": null, "answer": "20°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 165, "img_width": 103, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, P is a point outside ⊙O, PA and PB intersect ⊙O at two points C and D respectively. It is known that the central angles of ⁀AB and ⁀CD are 90.0 and 50.0 respectively, then ∠P = ()\nChoices:\n(A) 45°\n(B) 40°\n(C) 25°\n(D) 20°"}, {"pid": "120", "question": "What is the degree of this function?", "image": "images/120.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 320, "img_width": 312, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the degree of this function?"}, {"pid": "121", "question": "In trying to calculate how much money could be saved by packing lunch, <PERSON> recorded the amount he spent on lunch each day. According to the table, what was the rate of change between Wednesday and Thursday? (Unit: $, per day)", "image": "images/121.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 218, "img_width": 235, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: In trying to calculate how much money could be saved by packing lunch, <PERSON> recorded the amount he spent on lunch each day. According to the table, what was the rate of change between Wednesday and Thursday? (Unit: $, per day)"}, {"pid": "122", "question": "Are there fewer yellow regular buss than small yellow metallic school buss?", "image": "images/122.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Are there fewer yellow regular buss than small yellow metallic school buss?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "123", "question": "The diagram represents successive rotations, starting from the top down. Which shape comes next?", "image": "images/123.jpg", "choices": ["A", "B", "C", "D", "E"], "unit": null, "precision": null, "answer": "D", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 579, "img_width": 412, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The diagram represents successive rotations, starting from the top down. Which shape comes next?\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E"}, {"pid": "124", "question": "This type of leaf arrangement consists of at least three leaves attached to a node.", "image": "images/124.jpg", "choices": ["Whorled", "Simple", "Opposite", "Alternate"], "unit": null, "precision": null, "answer": "Whorled", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 225, "img_width": 576, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: This type of leaf arrangement consists of at least three leaves attached to a node.\nChoices:\n(A) Whorled\n(B) Simple\n(C) Opposite\n(D) Alternate"}, {"pid": "125", "question": "What happens if caterpillars decrease?", "image": "images/125.jpg", "choices": ["plants decrease", "plants increase", "nothing happens", "none of the above"], "unit": null, "precision": null, "answer": "plants increase", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 947, "img_width": 850, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What happens if caterpillars decrease?\nChoices:\n(A) plants decrease\n(B) plants increase\n(C) nothing happens\n(D) none of the above"}, {"pid": "126", "question": "What is the age gap between the leftmost and the rigtmost person?", "image": "images/126.jpg", "choices": null, "unit": "years", "precision": null, "answer": "9", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 800, "img_width": 623, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between the leftmost and the rigtmost person? (Unit: years)"}, {"pid": "127", "question": "How much more accurate is the most accurate algorithm compared the least accurate algorithm?", "image": "images/127.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How much more accurate is the most accurate algorithm compared the least accurate algorithm?"}, {"pid": "128", "question": "Subtract all large metal blocks. Subtract all yellow cylinders. How many objects are left?", "image": "images/128.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all large metal blocks. Subtract all yellow cylinders. How many objects are left?"}, {"pid": "129", "question": "Move the ruler to measure the length of the twig to the nearest inch. The twig is about (_) inches long.", "image": "images/129.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 156, "img_width": 438, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Move the ruler to measure the length of the twig to the nearest inch. The twig is about (_) inches long."}, {"pid": "130", "question": "How many people in the image were born after the end of World War II?", "image": "images/130.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 1403, "img_width": 1063, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many people in the image were born after the end of World War II?"}, {"pid": "131", "question": "How many bars have value below 40?", "image": "images/131.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many bars have value below 40?"}, {"pid": "132", "question": "如图，在4×4的正方形网格中，每个小正方形的边长均为1，点A，B，C都在格点上，AD⊥BC于D，则AD的长为（）", "image": "images/132.jpg", "choices": ["1", "1.5", "2", "\\frac{7}{3}"], "unit": null, "precision": null, "answer": "2", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 160, "img_width": 155, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，在4×4的正方形网格中，每个小正方形的边长均为1，点A，B，C都在格点上，AD⊥BC于D，则AD的长为（）\nChoices:\n(A) 1\n(B) 1.5\n(C) 2\n(D) \\frac{7}{3}"}, {"pid": "133", "question": "In how many years, is the merchandise exports greater than 0.92 %?", "image": "images/133.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 650, "img_width": 1268, "language": "english", "skills": ["statistical reasoning"], "source": "PlotQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: In how many years, is the merchandise exports greater than 0.92 %?"}, {"pid": "134", "question": "People can use the engineering-design process to develop solutions to problems. One step in the process is testing if a potential solution meets the requirements of the design.\nThe passage below describes how the engineering-design process was used to test a solution to a problem. Read the passage. Then answer the question below.\n\n<PERSON> was a landscape architect who was hired to design a new city park. The city council wanted the park to have space for outdoor concerts and to have at least 20% of the park shaded by trees. <PERSON> thought the concert area should be at least 150 meters from the road so traffic noise didn't interrupt the music. He developed three possible designs for the park with the concert area in a different location in each design. Then, he tested each design by measuring the distance between the road and the concert area.\nFigure: studying an architect's design. Which of the following could <PERSON>'s test show?", "image": "images/134.jpg", "choices": ["if at least 20% of the park would be shaded by trees in each design", "which design would have the least traffic noise in the concert area", "which design would have the greatest distance between the concert area and the road"], "unit": null, "precision": null, "answer": "which design would have the greatest distance between the concert area and the road", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "high school", "img_height": 232, "img_width": 302, "language": "english", "skills": ["scientific reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: People can use the engineering-design process to develop solutions to problems. One step in the process is testing if a potential solution meets the requirements of the design.\nThe passage below describes how the engineering-design process was used to test a solution to a problem. Read the passage. Then answer the question below.\n\n<PERSON> was a landscape architect who was hired to design a new city park. The city council wanted the park to have space for outdoor concerts and to have at least 20% of the park shaded by trees. <PERSON> thought the concert area should be at least 150 meters from the road so traffic noise didn't interrupt the music. He developed three possible designs for the park with the concert area in a different location in each design. Then, he tested each design by measuring the distance between the road and the concert area.\nFigure: studying an architect's design. Which of the following could <PERSON>'s test show?\nChoices:\n(A) if at least 20% of the park would be shaded by trees in each design\n(B) which design would have the least traffic noise in the concert area\n(C) which design would have the greatest distance between the concert area and the road"}, {"pid": "135", "question": "Is the number of buss that are in front of the big yellow aeroplane less than the number of matte bicycles that are on the right side of the tiny thing?", "image": "images/135.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of buss that are in front of the big yellow aeroplane less than the number of matte bicycles that are on the right side of the tiny thing?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "136", "question": "What is the difference between the highest and the lowest value of blue bar?", "image": "images/136.jpg", "choices": null, "unit": null, "precision": null, "answer": "64", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 443, "img_width": 415, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the difference between the highest and the lowest value of blue bar?"}, {"pid": "137", "question": "Is the function (f: R to R) injective?", "image": "images/137.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 291, "img_width": 258, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the function (f: R to R) injective?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "138", "question": "Is the sandwich cut in half?", "image": "images/138.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the sandwich cut in half?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "139", "question": "Does Indigo have the lowest value?", "image": "images/139.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 400, "img_width": 543, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Does Indigo have the lowest value?\nChoices:\n(A) yes\n(B) no"}, {"pid": "140", "question": "Which food has the least carbs?", "image": "images/140.jpg", "choices": ["soup", "water", "sandwich", "buns"], "unit": null, "precision": null, "answer": "soup", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 428, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "A-OKVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which food has the least carbs?\nChoices:\n(A) soup\n(B) water\n(C) sandwich\n(D) buns"}, {"pid": "141", "question": "As shown in the figure, AB is a long ladder leaning on the wall, the foot of the ladder B is away from the wall 1.6, the point D on the ladder is away from the wall 1.4, the length of BD is 0.55, then the length of the ladder is ()", "image": "images/141.jpg", "choices": ["3.85米", "4.00米", "4.40米", "4.50米"], "unit": null, "precision": null, "answer": "4.40米", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 128, "img_width": 78, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, AB is a long ladder leaning on the wall, the foot of the ladder B is away from the wall 1.6, the point D on the ladder is away from the wall 1.4, the length of BD is 0.55, then the length of the ladder is ()\nChoices:\n(A) 3.85米\n(B) 4.00米\n(C) 4.40米\n(D) 4.50米"}, {"pid": "142", "question": "Is it split in half?", "image": "images/142.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 425, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is it split in half?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "143", "question": "As shown in the figure, in the parallelogram ABCD, CE bisects ∠BCD and it intersects the AD edge at point E, and DE = 3.0, then the length of AB is ()", "image": "images/143.jpg", "choices": ["1", "2", "3", "6"], "unit": null, "precision": null, "answer": "3", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 85, "img_width": 204, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, in the parallelogram ABCD, CE bisects ∠BCD and it intersects the AD edge at point E, and DE = 3.0, then the length of AB is ()\nChoices:\n(A) 1\n(B) 2\n(C) 3\n(D) 6"}, {"pid": "144", "question": "<PERSON> buys 4.6 kilograms of turmeric. What is the total cost? (Unit: $)", "image": "images/144.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "13.8", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 162, "img_width": 210, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: <PERSON> buys 4.6 kilograms of turmeric. What is the total cost? (Unit: $)"}, {"pid": "145", "question": "Can you find the missing term?", "image": "images/145.jpg", "choices": null, "unit": null, "precision": null, "answer": "10", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 506, "img_width": 900, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Can you find the missing term?"}, {"pid": "146", "question": "<PERSON>'s classmates revealed how many science articles they read. What is the range of the numbers?'", "image": "images/146.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 218, "img_width": 286, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: <PERSON>'s classmates revealed how many science articles they read. What is the range of the numbers?'"}, {"pid": "147", "question": "The diagrams below show two pure samples of gas in identical closed, rigid containers. Each colored ball represents one gas particle. Both samples have the same number of particles. Compare the average kinetic energies of the particles in each sample. Which sample has the higher temperature?", "image": "images/147.jpg", "choices": ["neither; the samples have the same temperature", "sample B", "sample A"], "unit": null, "precision": null, "answer": "sample B", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 405, "img_width": 563, "language": "english", "skills": ["scientific reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The diagrams below show two pure samples of gas in identical closed, rigid containers. Each colored ball represents one gas particle. Both samples have the same number of particles. Compare the average kinetic energies of the particles in each sample. Which sample has the higher temperature?\nChoices:\n(A) neither; the samples have the same temperature\n(B) sample B\n(C) sample A"}, {"pid": "148", "question": "Which leaf shape has the smallest base?", "image": "images/148.jpg", "choices": ["Hastate", "Cordate", "Sagittate", "Decurrent"], "unit": null, "precision": null, "answer": "Decurrent", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 161, "img_width": 600, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which leaf shape has the smallest base?\nChoices:\n(A) Hastate\n(B) Cordate\n(C) Sagittate\n(D) Decurrent"}, {"pid": "149", "question": "如图，直线l1∥l2，∠1＝50°，∠2＝75°，则∠3＝（）", "image": "images/149.jpg", "choices": ["55°", "60°", "65°", "70°"], "unit": null, "precision": null, "answer": "55°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 93, "img_width": 156, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，直线l1∥l2，∠1＝50°，∠2＝75°，则∠3＝（）\nChoices:\n(A) 55°\n(B) 60°\n(C) 65°\n(D) 70°"}, {"pid": "150", "question": "As shown in the figure, points A, B, and C are three points on ⊙O, and the straight line CD and ⊙O are tangent to point C. If ∠DCB = 40.0, then the degree of ∠CAB is ()", "image": "images/150.jpg", "choices": ["40°", "50°", "80°", "100°"], "unit": null, "precision": null, "answer": "40°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 144, "img_width": 110, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, points A, B, and C are three points on ⊙O, and the straight line CD and ⊙O are tangent to point C. If ∠DCB = 40.0, then the degree of ∠CAB is ()\nChoices:\n(A) 40°\n(B) 50°\n(C) 80°\n(D) 100°"}, {"pid": "151", "question": "When does the function reach its local maximum?", "image": "images/151.jpg", "choices": ["(u1, u2) = (0, 0)", "(u1, u2) = (1, 0)", "(u1, u2) = (0, 1)", "(u1, u2) = (1, 1)"], "unit": null, "precision": null, "answer": "(u1, u2) = (0, 0)", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 325, "img_width": 458, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: When does the function reach its local maximum?\nChoices:\n(A) (u1, u2) = (0, 0)\n(B) (u1, u2) = (1, 0)\n(C) (u1, u2) = (0, 1)\n(D) (u1, u2) = (1, 1)"}, {"pid": "152", "question": "如图，直线l1∥l2，将含30°角的直角三角板按如图方式放置，直角顶点在l2上，若∠1＝76°，则∠2＝（）", "image": "images/152.jpg", "choices": ["36°", "45°", "44°", "64°"], "unit": null, "precision": null, "answer": "44°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 208, "img_width": 229, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，直线l1∥l2，将含30°角的直角三角板按如图方式放置，直角顶点在l2上，若∠1＝76°，则∠2＝（）\nChoices:\n(A) 36°\n(B) 45°\n(C) 44°\n(D) 64°"}, {"pid": "153", "question": "What would be impacted by an increase in owls?", "image": "images/153.jpg", "choices": ["sun", "grasshoppers", "grass", "mice"], "unit": null, "precision": null, "answer": "mice", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 423, "img_width": 600, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What would be impacted by an increase in owls?\nChoices:\n(A) sun\n(B) grasshoppers\n(C) grass\n(D) mice"}, {"pid": "154", "question": "Is this an odd function?", "image": "images/154.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 744, "img_width": 1114, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is this an odd function?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "155", "question": "Does Web Green have the minimum area under the curve?", "image": "images/155.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 400, "img_width": 601, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Does Web Green have the minimum area under the curve?\nChoices:\n(A) yes\n(B) no"}, {"pid": "156", "question": "What is the limit of the as x approaches 1 from the left side?", "image": "images/156.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 291, "img_width": 327, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the limit of the as x approaches 1 from the left side?"}, {"pid": "157", "question": "What number is shown?", "image": "images/157.jpg", "choices": null, "unit": null, "precision": null, "answer": "9335", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 279, "img_width": 637, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What number is shown?"}, {"pid": "158", "question": "What is the age gap between these two people in image?", "image": "images/158.jpg", "choices": null, "unit": "years", "precision": null, "answer": "7", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 685, "img_width": 911, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "159", "question": "What is the difference between two consecutive major ticks on the Y-axis ?", "image": "images/159.jpg", "choices": null, "unit": null, "precision": null, "answer": "100", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 650, "img_width": 1000, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "PlotQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the difference between two consecutive major ticks on the Y-axis ?"}, {"pid": "160", "question": "Find x.", "image": "images/160.jpg", "choices": ["10", "11", "12", "13"], "unit": null, "precision": null, "answer": "12", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 227, "img_width": 270, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find x.\nChoices:\n(A) 10\n(B) 11\n(C) 12\n(D) 13"}, {"pid": "161", "question": "What is the sum of the two numbers visible in the picture?", "image": "images/161.jpg", "choices": null, "unit": null, "precision": null, "answer": "71", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of the two numbers visible in the picture?"}, {"pid": "162", "question": "The bird watcher counted the number of birds in each flock that passed overhead. How many flocks had at least 17 birds but fewer than 33 birds? (Unit: flocks)", "image": "images/162.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 202, "img_width": 117, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: The bird watcher counted the number of birds in each flock that passed overhead. How many flocks had at least 17 birds but fewer than 33 birds? (Unit: flocks)"}, {"pid": "163", "question": "What number is shown?", "image": "images/163.jpg", "choices": null, "unit": null, "precision": null, "answer": "7519", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 285, "img_width": 637, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What number is shown?"}, {"pid": "164", "question": "As shown in the figure, in ▱ABCD, CE ⊥ AB, point E is the foot of perpendicular, if ∠D = 55.0, then ∠BCE = ()", "image": "images/164.jpg", "choices": ["55°", "35°", "25°", "30°"], "unit": null, "precision": null, "answer": "35°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 84, "img_width": 161, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, in ▱ABCD, CE ⊥ AB, point E is the foot of perpendicular, if ∠D = 55.0, then ∠BCE = ()\nChoices:\n(A) 55°\n(B) 35°\n(C) 25°\n(D) 30°"}, {"pid": "165", "question": "Subtract all cyan rubber cylinders. Subtract all tiny shiny cubes. How many objects are left?", "image": "images/165.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all cyan rubber cylinders. Subtract all tiny shiny cubes. How many objects are left?"}, {"pid": "166", "question": "Which <PERSON><PERSON><PERSON> is missing?", "image": "images/166.jpg", "choices": ["A", "B", "C", "D", "E", "F"], "unit": null, "precision": null, "answer": "B", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 816, "img_width": 2028, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which Shape is missing?\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E\n(F) F"}, {"pid": "167", "question": "What is the biggest zero of this function?", "image": "images/167.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 1920, "img_width": 1920, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the biggest zero of this function?"}, {"pid": "168", "question": "Given that the Hue-Saturation subspace shown in Fig. Q2 is a perfect circle and that colors A, B and C can be represented as the 3 points shown in the subspace. Which color has the smallest saturation coefficient?", "image": "images/168.jpg", "choices": ["(c)", "(a)", "(e)", "(d)", "(b)"], "unit": null, "precision": null, "answer": "(b)", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 454, "img_width": 414, "language": "english", "skills": ["scientific reasoning"], "source": "TheoremQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Given that the Hue-Saturation subspace shown in Fig. Q2 is a perfect circle and that colors A, B and C can be represented as the 3 points shown in the subspace. Which color has the smallest saturation coefficient?\nChoices:\n(A) (c)\n(B) (a)\n(C) (e)\n(D) (d)\n(E) (b)"}, {"pid": "169", "question": "What is the difference between two consecutive major ticks on the Y-axis ?", "image": "images/169.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 650, "img_width": 1049, "language": "english", "skills": ["statistical reasoning"], "source": "PlotQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the difference between two consecutive major ticks on the Y-axis ?"}, {"pid": "170", "question": "f(-1) is ____ f(0).", "image": "images/170.jpg", "choices": ["larger than", "equal to", "smaller than"], "unit": null, "precision": null, "answer": "smaller than", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 296, "img_width": 600, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: f(-1) is ____ f(0).\nChoices:\n(A) larger than\n(B) equal to\n(C) smaller than"}, {"pid": "171", "question": "How many cinnamon rolls are there?", "image": "images/171.jpg", "choices": null, "unit": null, "precision": null, "answer": "20", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 190, "img_width": 467, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many cinnamon rolls are there?"}, {"pid": "172", "question": "Is Light Seafoam less than Dark Salmon?", "image": "images/172.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 524, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Light Seafoam less than Dark Salmon?\nChoices:\n(A) yes\n(B) no"}, {"pid": "173", "question": "Is the number of small rubber buss behind the big green road bike less than the number of suvs that are behind the large brown matte truck?", "image": "images/173.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of small rubber buss behind the big green road bike less than the number of suvs that are behind the large brown matte truck?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "174", "question": "Are there fewer tiny cyan suvs that are behind the aeroplane than cyan utility bikes?", "image": "images/174.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Are there fewer tiny cyan suvs that are behind the aeroplane than cyan utility bikes?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "175", "question": "What is the sum of accuracies of the algorithm liver for all the datasets?", "image": "images/175.jpg", "choices": null, "unit": null, "precision": null, "answer": "24", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of accuracies of the algorithm liver for all the datasets?"}, {"pid": "176", "question": "Find $RS$ if $\\triangle QRS$ is an equilateral triangle.", "image": "images/176.jpg", "choices": ["0.5", "1", "1.5", "2"], "unit": null, "precision": null, "answer": "2", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 292, "img_width": 305, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find $RS$ if $\\triangle QRS$ is an equilateral triangle.\nChoices:\n(A) 0.5\n(B) 1\n(C) 1.5\n(D) 2"}, {"pid": "177", "question": "Is the number of brown tandem bikes that are to the left of the small blue matte car greater than the number of tiny blue biplanes?", "image": "images/177.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of brown tandem bikes that are to the left of the small blue matte car greater than the number of tiny blue biplanes?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "178", "question": "如图，点A、C在∠FBD的两条边BF、BD上，BE平分∠FBD，CE平分∠ACD，连接AE，若∠BEC＝35°，则∠FAE的度数为（）", "image": "images/178.jpg", "choices": ["35°", "45°", "55°", "65°"], "unit": null, "precision": null, "answer": "55°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 99, "img_width": 129, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，点A、C在∠FBD的两条边BF、BD上，BE平分∠FBD，CE平分∠ACD，连接AE，若∠BEC＝35°，则∠FAE的度数为（）\nChoices:\n(A) 35°\n(B) 45°\n(C) 55°\n(D) 65°"}, {"pid": "179", "question": "如图，在ABCD中，已知AC＝4cm，若△ACD的周长为14cm，则ABCD的周长为（）", "image": "images/179.jpg", "choices": ["14cm", "28cm", "10cm", "20cm"], "unit": null, "precision": null, "answer": "20cm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 94, "img_width": 157, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，在ABCD中，已知AC＝4cm，若△ACD的周长为14cm，则ABCD的周长为（）\nChoices:\n(A) 14cm\n(B) 28cm\n(C) 10cm\n(D) 20cm"}, {"pid": "180", "question": "Subtract all tiny brown cylinders. Subtract all tiny brown objects. How many objects are left?", "image": "images/180.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all tiny brown cylinders. Subtract all tiny brown objects. How many objects are left?"}, {"pid": "181", "question": "Which option is correct?", "image": "images/181.jpg", "choices": ["A", "B", "C"], "unit": null, "precision": null, "answer": "C", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 332, "img_width": 864, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which option is correct?\nChoices:\n(A) A\n(B) B\n(C) C"}, {"pid": "182", "question": "Is Web Green greater than Yellow?", "image": "images/182.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 400, "img_width": 589, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Web Green greater than Yellow?\nChoices:\n(A) yes\n(B) no"}, {"pid": "183", "question": "Subtract all brown cubes. Subtract all gray cylinders. How many objects are left?", "image": "images/183.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all brown cubes. Subtract all gray cylinders. How many objects are left?"}, {"pid": "184", "question": "How many bars have values smaller than 0?", "image": "images/184.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many bars have values smaller than 0?"}, {"pid": "185", "question": "An image has the gray level PDF $p_r(r)$ shown in Fig. Q1a. One wants to do histogram specification SO that the processed image will have the specified $p_z(z)$ shown in Fig. Q1b. Can we use intensity mapping function $T: z=1-r$ to achieve the goal?", "image": "images/185.jpg", "choices": ["True", "False"], "unit": null, "precision": null, "answer": "False", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 376, "img_width": 724, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "TheoremQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: An image has the gray level PDF $p_r(r)$ shown in Fig. Q1a. One wants to do histogram specification SO that the processed image will have the specified $p_z(z)$ shown in Fig. Q1b. Can we use intensity mapping function $T: z=1-r$ to achieve the goal?\nChoices:\n(A) True\n(B) False"}, {"pid": "186", "question": "As shown in the figure, CD is a plane mirror, the light is emitted from point A, reflected by point E on CD, and irradiated to point B. If the incident angle is α, AC ⊥ CD, BD ⊥ CD, the feet of perpendicular are C, D, and AC = 3.0, BD = 6.0, CD = 10.0, then the length of the line segment ED is ()", "image": "images/186.jpg", "choices": ["\\frac{20}{3}", "\\frac{10}{3}", "7", "\\frac{14}{3}"], "unit": null, "precision": null, "answer": "\\frac{20}{3}", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 112, "img_width": 183, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, CD is a plane mirror, the light is emitted from point A, reflected by point E on CD, and irradiated to point B. If the incident angle is α, AC ⊥ CD, BD ⊥ CD, the feet of perpendicular are C, D, and AC = 3.0, BD = 6.0, CD = 10.0, then the length of the line segment ED is ()\nChoices:\n(A) \\frac{20}{3}\n(B) \\frac{10}{3}\n(C) 7\n(D) \\frac{14}{3}"}, {"pid": "187", "question": "What number is shown?", "image": "images/187.jpg", "choices": null, "unit": null, "precision": null, "answer": "9015", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 279, "img_width": 634, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What number is shown?"}, {"pid": "188", "question": "How many methods in the table achieve an A-847 score higher than 20.0?", "image": "images/188.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "college", "img_height": 634, "img_width": 2226, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many methods in the table achieve an A-847 score higher than 20.0?"}, {"pid": "189", "question": "What is the lowest accuracy reported in the whole chart?", "image": "images/189.jpg", "choices": null, "unit": null, "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the lowest accuracy reported in the whole chart?"}, {"pid": "190", "question": "Move the ruler to measure the length of the nail to the nearest inch. The nail is about (_) inches long.", "image": "images/190.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 132, "img_width": 438, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Move the ruler to measure the length of the nail to the nearest inch. The nail is about (_) inches long."}, {"pid": "191", "question": "In how many years, is the volume of the air carriers in Ethiopia greater than the average volume of the air carriers in Ethiopia taken over all years ?", "image": "images/191.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 700, "img_width": 1116, "language": "english", "skills": ["statistical reasoning"], "source": "PlotQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: In how many years, is the volume of the air carriers in Ethiopia greater than the average volume of the air carriers in Ethiopia taken over all years ?"}, {"pid": "192", "question": "As shown in the figure, the diameter CD of ⊙O crosses the midpoint G of chord EF, ∠DCF = 20.0, then ∠EOD is equal to ()", "image": "images/192.jpg", "choices": ["10°", "20°", "40°", "80°"], "unit": null, "precision": null, "answer": "40°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 127, "img_width": 101, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, the diameter CD of ⊙O crosses the midpoint G of chord EF, ∠DCF = 20.0, then ∠EOD is equal to ()\nChoices:\n(A) 10°\n(B) 20°\n(C) 40°\n(D) 80°"}, {"pid": "193", "question": "Subtract all red things. Subtract all cylinders. How many objects are left?", "image": "images/193.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all red things. Subtract all cylinders. How many objects are left?"}, {"pid": "194", "question": "On average, how many people can commute on this vehicle?", "image": "images/194.jpg", "choices": null, "unit": null, "precision": null, "answer": "50", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 408, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: On average, how many people can commute on this vehicle?"}, {"pid": "195", "question": "如图，AB是⊙O的直径，C，D两点在⊙O上，∠BCD＝25°，则∠AOD的度数为（）", "image": "images/195.jpg", "choices": ["120°", "125°", "130°", "135°"], "unit": null, "precision": null, "answer": "130°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 95, "img_width": 110, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，AB是⊙O的直径，C，D两点在⊙O上，∠BCD＝25°，则∠AOD的度数为（）\nChoices:\n(A) 120°\n(B) 125°\n(C) 130°\n(D) 135°"}, {"pid": "196", "question": "如图所示，在△ABC中，已知点D，E，F分别为边BC，AD，CE的中点，且S△ABC＝4cm2，则S△DEF等于（）", "image": "images/196.jpg", "choices": ["2cm2", "1cm2", "0.5cm2", "0.25cm2"], "unit": null, "precision": null, "answer": "0.5cm2", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 81, "img_width": 110, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图所示，在△ABC中，已知点D，E，F分别为边BC，AD，CE的中点，且S△ABC＝4cm2，则S△DEF等于（）\nChoices:\n(A) 2cm2\n(B) 1cm2\n(C) 0.5cm2\n(D) 0.25cm2"}, {"pid": "197", "question": "How many sequences have negative Influence Scores?", "image": "images/197.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "bar chart", "grade": "college", "img_height": 772, "img_width": 1766, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many sequences have negative Influence Scores?"}, {"pid": "198", "question": "Calculate the missing value.", "image": "images/198.jpg", "choices": ["1", "2", "3", "4"], "unit": null, "precision": null, "answer": "1", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 756, "img_width": 890, "language": "english", "skills": ["logical reasoning", "arithmetic reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Calculate the missing value.\nChoices:\n(A) 1\n(B) 2\n(C) 3\n(D) 4"}, {"pid": "199", "question": "Figure 23-42 is a section of a conducting rod of radius $R_1=1.30 \\mathrm{~mm}$ and length $L=$ $11.00 \\mathrm{~m}$ inside a thin-walled coaxial conducting cylindrical shell of radius $R_2=10.0 R_1$ and the (same) length $L$. The net charge on the rod is $Q_1=+3.40 \\times 10^{-12} \\mathrm{C}$; that on the shell is $Q_2=-2.00 Q_1$. What is the magnitude $E$ of the electric field at radial distance $r=2.00 R_2$?", "image": "images/199.jpg", "choices": null, "unit": null, "precision": 2.0, "answer": "0.21", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 303, "img_width": 262, "language": "english", "skills": ["algebraic reasoning", "arithmetic reasoning", "scientific reasoning"], "source": "SciBench", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with two decimal places and provide the final value, e.g., 1.23, 1.34, 1.45, at the end.\nQuestion: Figure 23-42 is a section of a conducting rod of radius $R_1=1.30 \\mathrm{~mm}$ and length $L=$ $11.00 \\mathrm{~m}$ inside a thin-walled coaxial conducting cylindrical shell of radius $R_2=10.0 R_1$ and the (same) length $L$. The net charge on the rod is $Q_1=+3.40 \\times 10^{-12} \\mathrm{C}$; that on the shell is $Q_2=-2.00 Q_1$. What is the magnitude $E$ of the electric field at radial distance $r=2.00 R_2$?"}, {"pid": "200", "question": "Is Light Sky Blue the high median?", "image": "images/200.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 404, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Light Sky Blue the high median?\nChoices:\n(A) yes\n(B) no"}, {"pid": "201", "question": "What is the sum of all the values in the border group?", "image": "images/201.jpg", "choices": null, "unit": null, "precision": null, "answer": "19", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of all the values in the border group?"}, {"pid": "202", "question": "What is the value of the smallest bar?", "image": "images/202.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the value of the smallest bar?"}, {"pid": "203", "question": "如图，在5×4的正方形网格中，每个小正方形的边长都是1，△ABC的顶点都在这些小正方形的顶点上，则tan∠BAC的值为（）", "image": "images/203.jpg", "choices": ["\\frac{4}{3}", "0.75", "0.6", "0.8"], "unit": null, "precision": null, "answer": "\\frac{4}{3}", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 151, "img_width": 172, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，在5×4的正方形网格中，每个小正方形的边长都是1，△ABC的顶点都在这些小正方形的顶点上，则tan∠BAC的值为（）\nChoices:\n(A) \\frac{4}{3}\n(B) 0.75\n(C) 0.6\n(D) 0.8"}, {"pid": "204", "question": "از سمت راست تصویر درب دوم چند شیشه بدون رنگ داره؟", "image": "images/204.jpg", "choices": null, "unit": null, "precision": null, "answer": "12", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 376, "img_width": 564, "language": "persian", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "ParsVQA-Caps", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: از سمت راست تصویر درب دوم چند شیشه بدون رنگ داره؟"}, {"pid": "205", "question": "A statistician analyzed the number of runs scored by players last season. How many players scored more than 2 runs last season?'", "image": "images/205.jpg", "choices": null, "unit": null, "precision": null, "answer": "24", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 190, "img_width": 351, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: A statistician analyzed the number of runs scored by players last season. How many players scored more than 2 runs last season?'"}, {"pid": "206", "question": "Find the scale factor from $Q$ to $Q'$.", "image": "images/206.jpg", "choices": ["2", "3", "4", "5"], "unit": null, "precision": null, "answer": "3", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 611, "img_width": 731, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find the scale factor from $Q$ to $Q'$.\nChoices:\n(A) 2\n(B) 3\n(C) 4\n(D) 5"}, {"pid": "207", "question": "What is the sum of the accuracies of the algorithms magic and secure?", "image": "images/207.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the sum of the accuracies of the algorithms magic and secure?"}, {"pid": "208", "question": "What is the age gap between the leftmost and the rigtmost person?", "image": "images/208.jpg", "choices": null, "unit": "years", "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 195, "img_width": 300, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between the leftmost and the rigtmost person? (Unit: years)"}, {"pid": "209", "question": "What is the highest value in black line chart ?", "image": "images/209.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "28.3", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: What is the highest value in black line chart ?"}, {"pid": "210", "question": "How many people in the image were born after the end of World War II?", "image": "images/210.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 370, "img_width": 493, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many people in the image were born after the end of World War II?"}, {"pid": "211", "question": "How many algorithms have accuracies higher than 2?", "image": "images/211.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many algorithms have accuracies higher than 2?"}, {"pid": "212", "question": "Is Cornflower the minimum?", "image": "images/212.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 403, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is <PERSON><PERSON>flower the minimum?\nChoices:\n(A) yes\n(B) no"}, {"pid": "213", "question": "In which year there was lowest per capita real gross domestic product of ohio?", "image": "images/213.jpg", "choices": null, "unit": null, "precision": null, "answer": "2001", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: In which year there was lowest per capita real gross domestic product of ohio?"}, {"pid": "214", "question": "In how many years, is the percentage of amount earned from merchandise imports in Canada greater than the average percentage of amount earned from merchandise imports in Canada taken over all years ?", "image": "images/214.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "scatter plot", "grade": "daily life", "img_height": 700, "img_width": 1109, "language": "english", "skills": ["statistical reasoning"], "source": "PlotQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: In how many years, is the percentage of amount earned from merchandise imports in Canada greater than the average percentage of amount earned from merchandise imports in Canada taken over all years ?"}, {"pid": "215", "question": "Layla went on a camping trip and logged the number of miles she hiked each day. What is the range of the numbers?'", "image": "images/215.jpg", "choices": null, "unit": null, "precision": null, "answer": "8", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "high school", "img_height": 249, "img_width": 212, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: <PERSON> went on a camping trip and logged the number of miles she hiked each day. What is the range of the numbers?'"}, {"pid": "216", "question": "What percentage of people like the most preferred object in the whole chart?", "image": "images/216.jpg", "choices": null, "unit": null, "precision": null, "answer": "90", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What percentage of people like the most preferred object in the whole chart?"}, {"pid": "217", "question": "What is the degree of this function?", "image": "images/217.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 202, "img_width": 304, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the degree of this function?"}, {"pid": "218", "question": "Subtract all large red rubber blocks. Subtract all tiny red matte objects. How many objects are left?", "image": "images/218.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all large red rubber blocks. Subtract all tiny red matte objects. How many objects are left?"}, {"pid": "219", "question": "What is the value of the smallest bar?", "image": "images/219.jpg", "choices": null, "unit": null, "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the value of the smallest bar?"}, {"pid": "220", "question": "As shown in the figure, ⊙O is the circumscribed circle of the quadrilateral ABCD, if ∠O = 110.0, then the degree of ∠C is ()", "image": "images/220.jpg", "choices": ["125°", "120°", "105°", "90°"], "unit": null, "precision": null, "answer": "125°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 128, "img_width": 124, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, ⊙O is the circumscribed circle of the quadrilateral ABCD, if ∠O = 110.0, then the degree of ∠C is ()\nChoices:\n(A) 125°\n(B) 120°\n(C) 105°\n(D) 90°"}, {"pid": "221", "question": "As shown in the figure, A, B, C are three points on ⊙O, ∠ACB = 25.0, then the degree of ∠BAO is ()", "image": "images/221.jpg", "choices": ["50°", "55°", "60°", "65°"], "unit": null, "precision": null, "answer": "65°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 108, "img_width": 102, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, A, B, C are three points on ⊙O, ∠ACB = 25.0, then the degree of ∠BAO is ()\nChoices:\n(A) 50°\n(B) 55°\n(C) 60°\n(D) 65°"}, {"pid": "222", "question": "Subtract all blue shiny spheres. Subtract all big blue shiny cubes. How many objects are left?", "image": "images/222.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all blue shiny spheres. Subtract all big blue shiny cubes. How many objects are left?"}, {"pid": "223", "question": "Is this an even function?", "image": "images/223.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 776, "img_width": 1430, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is this an even function?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "224", "question": "Is this a periodic function?", "image": "images/224.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 744, "img_width": 1114, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is this a periodic function?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "225", "question": "Fig. Q4 shows the contour of an object. Represent it with an 8-directional chain code. The resultant chain code should be normalized with respect to the starting point of the chain code. Represent the answer as a list with each digit as a element.", "image": "images/225.jpg", "choices": null, "unit": null, "precision": null, "answer": "[0, 2, 0, 2, 1, 7, 1, 2, 0, 3, 0, 6]", "question_type": "free_form", "answer_type": "list", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 560, "img_width": 846, "language": "english", "skills": ["scientific reasoning"], "source": "TheoremQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a Python list as an answer and provide the final list, e.g., [1, 2, 3], [1.2, 1.3, 1.4], at the end.\nQuestion: Fig. Q4 shows the contour of an object. Represent it with an 8-directional chain code. The resultant chain code should be normalized with respect to the starting point of the chain code. Represent the answer as a list with each digit as a element."}, {"pid": "226", "question": "What time is shown? Answer by typing a time word, not a number. It is (_) past three.", "image": "images/226.jpg", "choices": ["half", "quarter", "o'clock", "quarter to", "quarter past"], "unit": null, "precision": null, "answer": "quarter", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 95, "img_width": 95, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What time is shown? Answer by typing a time word, not a number. It is (_) past three.\nChoices:\n(A) half\n(B) quarter\n(C) o'clock\n(D) quarter to\n(E) quarter past"}, {"pid": "227", "question": "Is <PERSON>chid the maximum?", "image": "images/227.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 580, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is <PERSON> Orchid the maximum?\nChoices:\n(A) yes\n(B) no"}, {"pid": "228", "question": "As shown in the figure, AB is the diameter of circle O, DB and DC are respectively tangent to circle O at points B and C. If ∠ACE = 25.0, then the degree of ∠D is ()", "image": "images/228.jpg", "choices": ["50°", "55°", "60°", "65°"], "unit": null, "precision": null, "answer": "50°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 97, "img_width": 137, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, AB is the diameter of circle O, DB and DC are respectively tangent to circle O at points B and C. If ∠ACE = 25.0, then the degree of ∠D is ()\nChoices:\n(A) 50°\n(B) 55°\n(C) 60°\n(D) 65°"}, {"pid": "229", "question": "What is the highest lysine level given?", "image": "images/229.jpg", "choices": ["0.33%", "0.31%", "0.29%", "0.32%", "0.30%"], "unit": null, "precision": null, "answer": "0.30%", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "document image", "grade": "daily life", "img_height": 2185, "img_width": 1683, "language": "english", "skills": ["statistical reasoning"], "source": "DocVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What is the highest lysine level given?\nChoices:\n(A) 0.33%\n(B) 0.31%\n(C) 0.29%\n(D) 0.32%\n(E) 0.30%"}, {"pid": "230", "question": "How many algorithms have accuracy higher than 9 in at least one dataset?", "image": "images/230.jpg", "choices": null, "unit": null, "precision": null, "answer": "0", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many algorithms have accuracy higher than 9 in at least one dataset?"}, {"pid": "231", "question": "Which model has the overall best ImageNet 10shot Accuracy score across different training steps?", "image": "images/231.jpg", "choices": ["Identity", "Uniform", "Uniform / Soft", "Soft / Uniform", "Soft", "<PERSON><PERSON>"], "unit": null, "precision": null, "answer": "Soft", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "line plot", "grade": "college", "img_height": 988, "img_width": 2002, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which model has the overall best ImageNet 10shot Accuracy score across different training steps?\nChoices:\n(A) Identity\n(B) Uniform\n(C) Uniform / Soft\n(D) Soft / Uniform\n(E) Soft\n(F) Dense"}, {"pid": "232", "question": "The diagram below is a model of two solutions. Each pink ball represents one particle of solute. Which solution has a higher concentration of pink particles?", "image": "images/232.jpg", "choices": ["neither; their concentrations are the same", "Solution B", "Solution A"], "unit": null, "precision": null, "answer": "Solution B", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 251, "img_width": 378, "language": "english", "skills": ["scientific reasoning"], "source": "ScienceQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The diagram below is a model of two solutions. Each pink ball represents one particle of solute. Which solution has a higher concentration of pink particles?\nChoices:\n(A) neither; their concentrations are the same\n(B) Solution B\n(C) Solution A"}, {"pid": "233", "question": "Move the ruler to measure the length of the nail to the nearest inch. The nail is about (_) inches long.", "image": "images/233.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 199, "img_width": 438, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Move the ruler to measure the length of the nail to the nearest inch. The nail is about (_) inches long."}, {"pid": "234", "question": "In the figure shown above, AC = 6. What is the length of segment AB?", "image": "images/234.jpg", "choices": ["3", "5", "6", "7", "It cannot be determined from the information given"], "unit": null, "precision": null, "answer": "6", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 378, "img_width": 434, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GEOS", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In the figure shown above, AC = 6. What is the length of segment AB?\nChoices:\n(A) 3\n(B) 5\n(C) 6\n(D) 7\n(E) It cannot be determined from the information given"}, {"pid": "235", "question": "Is the epigraph of a function f an infinite set?", "image": "images/235.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 266, "img_width": 412, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the epigraph of a function f an infinite set?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "236", "question": "Find $z$.", "image": "images/236.jpg", "choices": ["7", "9", "12", "15"], "unit": null, "precision": null, "answer": "12", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 423, "img_width": 447, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find $z$.\nChoices:\n(A) 7\n(B) 9\n(C) 12\n(D) 15"}, {"pid": "237", "question": "If the Red squirrel and deer mouse population were to decrease, what would happen to the deer tick population?", "image": "images/237.jpg", "choices": ["increase", "fluctuate", "it would decrease", "stay the same "], "unit": null, "precision": null, "answer": "it would decrease", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 346, "img_width": 400, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: If the Red squirrel and deer mouse population were to decrease, what would happen to the deer tick population?\nChoices:\n(A) increase\n(B) fluctuate\n(C) it would decrease\n(D) stay the same"}, {"pid": "238", "question": "Find PT", "image": "images/238.jpg", "choices": ["6", "\\frac { 20 } { 3 }", "7", "22 / 3"], "unit": null, "precision": null, "answer": "\\frac { 20 } { 3 }", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 250, "img_width": 238, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find PT\nChoices:\n(A) 6\n(B) \\frac { 20 } { 3 }\n(C) 7\n(D) 22 / 3"}, {"pid": "239", "question": "Is Sky Blue the minimum?", "image": "images/239.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 769, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is Sky Blue the minimum?\nChoices:\n(A) yes\n(B) no"}, {"pid": "240", "question": "What is the age gap between these two people in image?", "image": "images/240.jpg", "choices": null, "unit": "years", "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 2387, "img_width": 3500, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "241", "question": "How many models in the table have a model size larger than 10B?", "image": "images/241.jpg", "choices": null, "unit": null, "precision": null, "answer": "11", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "college", "img_height": 1184, "img_width": 1570, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many models in the table have a model size larger than 10B?"}, {"pid": "242", "question": "Find $m \\angle A$ of quadrilateral ABCD", "image": "images/242.jpg", "choices": ["45", "90", "135", "180"], "unit": null, "precision": null, "answer": "135", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 381, "img_width": 621, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find $m \\angle A$ of quadrilateral ABCD\nChoices:\n(A) 45\n(B) 90\n(C) 135\n(D) 180"}, {"pid": "243", "question": "چند عدد شیرینی مثلثی شکل در جعبه است؟", "image": "images/243.jpg", "choices": null, "unit": null, "precision": null, "answer": "5", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 1001, "img_width": 564, "language": "persian", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "ParsVQA-Caps", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: چند عدد شیرینی مثلثی شکل در جعبه است؟"}, {"pid": "244", "question": "Does Aqua have the minimum area under the curve?", "image": "images/244.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 400, "img_width": 500, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Does Aqua have the minimum area under the curve?\nChoices:\n(A) yes\n(B) no"}, {"pid": "245", "question": "How much money does <PERSON> need to buy a grilled steak and a mushroom pizza? (Unit: $)", "image": "images/245.jpg", "choices": null, "unit": null, "precision": null, "answer": "24", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 128, "img_width": 259, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How much money does <PERSON> need to buy a grilled steak and a mushroom pizza? (Unit: $)"}, {"pid": "246", "question": "Assume that all gases are perfect and that data refer to 298 K unless otherwise stated. In 1995, the Intergovernmental Panel on Climate Change (IPCC) considered a global average temperature rise of $1.0-3.5^{\\circ} \\mathrm{C}$ likely by the year 2100 , with $2.0^{\\circ} \\mathrm{C}$ its best estimate. Because water vapour is itself a greenhouse gas, the increase in water vapour content of the atmosphere is of some concern to climate change experts. Predict the relative increase in water vapour in the atmosphere based on a temperature rises of $2.0 \\mathrm{~K}$, assuming that the relative humidity remains constant. (The present global mean temperature is $290 \\mathrm{~K}$, and the equilibrium vapour pressure of water at that temperature is 0.0189 bar.)", "image": "images/246.jpg", "choices": null, "unit": null, "precision": null, "answer": "13", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 216, "img_width": 1098, "language": "english", "skills": ["scientific reasoning"], "source": "SciBench", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Assume that all gases are perfect and that data refer to 298 K unless otherwise stated. In 1995, the Intergovernmental Panel on Climate Change (IPCC) considered a global average temperature rise of $1.0-3.5^{\\circ} \\mathrm{C}$ likely by the year 2100 , with $2.0^{\\circ} \\mathrm{C}$ its best estimate. Because water vapour is itself a greenhouse gas, the increase in water vapour content of the atmosphere is of some concern to climate change experts. Predict the relative increase in water vapour in the atmosphere based on a temperature rises of $2.0 \\mathrm{~K}$, assuming that the relative humidity remains constant. (The present global mean temperature is $290 \\mathrm{~K}$, and the equilibrium vapour pressure of water at that temperature is 0.0189 bar.)"}, {"pid": "247", "question": "A spaceship of mass $m=4.50 \\times 10^3 \\mathrm{~kg}$ is in a circular Earth orbit of radius $r=8.00 \\times 10^6 \\mathrm{~m}$ and period $T_0=118.6 \\mathrm{~min}=$ $7.119 \\times 10^3 \\mathrm{~s}$ when a thruster is fired in the forward direction to decrease the speed to $96.0 \\%$ of the original speed. What is the period $T$ of the resulting elliptical orbit (Figure)?", "image": "images/247.jpg", "choices": null, "unit": null, "precision": 2.0, "answer": "6.36", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 906, "img_width": 914, "language": "english", "skills": ["scientific reasoning"], "source": "SciBench", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with two decimal places and provide the final value, e.g., 1.23, 1.34, 1.45, at the end.\nQuestion: A spaceship of mass $m=4.50 \\times 10^3 \\mathrm{~kg}$ is in a circular Earth orbit of radius $r=8.00 \\times 10^6 \\mathrm{~m}$ and period $T_0=118.6 \\mathrm{~min}=$ $7.119 \\times 10^3 \\mathrm{~s}$ when a thruster is fired in the forward direction to decrease the speed to $96.0 \\%$ of the original speed. What is the period $T$ of the resulting elliptical orbit (Figure)?"}, {"pid": "248", "question": "Is the number of green matte choppers greater than the number of large yellow shiny motorbikes?", "image": "images/248.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of green matte choppers greater than the number of large yellow shiny motorbikes?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "249", "question": "Subtract all green rubber cubes. Subtract all red matte blocks. How many objects are left?", "image": "images/249.jpg", "choices": null, "unit": null, "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all green rubber cubes. Subtract all red matte blocks. How many objects are left?"}, {"pid": "250", "question": "The area $A$ of the shaded region is given. Find $x$. $A = 66$ cm$^2$ .", "image": "images/250.jpg", "choices": ["4.6", "6.5", "13.0", "26.0"], "unit": null, "precision": null, "answer": "13.0", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 286, "img_width": 303, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The area $A$ of the shaded region is given. Find $x$. $A = 66$ cm$^2$ .\nChoices:\n(A) 4.6\n(B) 6.5\n(C) 13.0\n(D) 26.0"}, {"pid": "251", "question": "Subtract all green balls. Subtract all shiny things. How many objects are left?", "image": "images/251.jpg", "choices": null, "unit": null, "precision": null, "answer": "4", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all green balls. Subtract all shiny things. How many objects are left?"}, {"pid": "252", "question": "Consider the infinitely long chain of resistors shown below. What is the resistance between terminals a and b if R=1?", "image": "images/252.jpg", "choices": null, "unit": null, "precision": 2.0, "answer": "0.73", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 169, "img_width": 463, "language": "english", "skills": ["scientific reasoning"], "source": "TheoremQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with two decimal places and provide the final value, e.g., 1.23, 1.34, 1.45, at the end.\nQuestion: Consider the infinitely long chain of resistors shown below. What is the resistance between terminals a and b if R=1?"}, {"pid": "253", "question": "How many objects are preferred by more than 7 people in at least one category?", "image": "images/253.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: How many objects are preferred by more than 7 people in at least one category?"}, {"pid": "254", "question": "Is the number of big objects that are in front of the metal fighter less than the number of things that are behind the big metallic bus?", "image": "images/254.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of big objects that are in front of the metal fighter less than the number of things that are behind the big metallic bus?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "255", "question": "As shown in the figure, ∠BAC = 110.0, if A and B are symmetrical with respect to the line MP, A and C are symmetrical with respect to the line NQ, then the size of ∠PAQ is ()", "image": "images/255.jpg", "choices": ["70°", "55°", "40°", "30°"], "unit": null, "precision": null, "answer": "40°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 92, "img_width": 188, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, ∠BAC = 110.0, if A and B are symmetrical with respect to the line MP, A and C are symmetrical with respect to the line NQ, then the size of ∠PAQ is ()\nChoices:\n(A) 70°\n(B) 55°\n(C) 40°\n(D) 30°"}, {"pid": "256", "question": "如图，△ABC中，AD平分∠BAC，AD交BC于点D，DE⊥AB，垂足为E，若DE＝3，AC＝4，则△ADC的面积为（）", "image": "images/256.jpg", "choices": ["3", "4", "5", "6"], "unit": null, "precision": null, "answer": "6", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 75, "img_width": 148, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，△ABC中，AD平分∠BAC，AD交BC于点D，DE⊥AB，垂足为E，若DE＝3，AC＝4，则△ADC的面积为（）\nChoices:\n(A) 3\n(B) 4\n(C) 5\n(D) 6"}, {"pid": "257", "question": "以直角三角形的三边为边向外作正方形，其中两个正方形的面积如图所示，则正方形A的面积为（）", "image": "images/257.jpg", "choices": ["6", "36", "64", "8"], "unit": null, "precision": null, "answer": "6", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 119, "img_width": 109, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 以直角三角形的三边为边向外作正方形，其中两个正方形的面积如图所示，则正方形A的面积为（）\nChoices:\n(A) 6\n(B) 36\n(C) 64\n(D) 8"}, {"pid": "258", "question": "An employee at the craft store counted the number of red buttons in each bag of mixed buttons. How many bags had at least 60 red buttons but fewer than 81 red buttons?'", "image": "images/258.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 224, "img_width": 156, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: An employee at the craft store counted the number of red buttons in each bag of mixed buttons. How many bags had at least 60 red buttons but fewer than 81 red buttons?'"}, {"pid": "259", "question": "Subtract all large yellow metal blocks. Subtract all gray metallic cylinders. How many objects are left?", "image": "images/259.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "synthetic scene", "grade": "elementary school", "img_height": 240, "img_width": 320, "language": "english", "skills": ["arithmetic reasoning"], "source": "CLEVR-Math", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Subtract all large yellow metal blocks. Subtract all gray metallic cylinders. How many objects are left?"}, {"pid": "260", "question": "Is the derivative of the function positive between [1, 2] assuming that it's differentiable?", "image": "images/260.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 368, "img_width": 412, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the derivative of the function positive between [1, 2] assuming that it's differentiable?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "261", "question": "What is the age gap between these two people in image?", "image": "images/261.jpg", "choices": null, "unit": "years", "precision": null, "answer": "6", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 500, "img_width": 345, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between these two people in image? (Unit: years)"}, {"pid": "262", "question": "What is the difference between genres of tv shows watched by highest female and lowest female?", "image": "images/262.jpg", "choices": null, "unit": null, "precision": null, "answer": "39", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 756, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the difference between genres of tv shows watched by highest female and lowest female?"}, {"pid": "263", "question": "What number is shown?", "image": "images/263.jpg", "choices": null, "unit": null, "precision": null, "answer": "38", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 117, "img_width": 113, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What number is shown?"}, {"pid": "264", "question": "For Group C, in which week is the cumulative increase in weight , the highest?", "image": "images/264.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "document image", "grade": "daily life", "img_height": 2237, "img_width": 1754, "language": "english", "skills": ["statistical reasoning"], "source": "DocVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: For Group C, in which week is the cumulative increase in weight , the highest?"}, {"pid": "265", "question": "<PERSON><PERSON>'s P.E. class participated in a push-up competition, and <PERSON><PERSON> wrote down how many push-ups each person could do. How many people did at least 60 push-ups? (Unit: people)", "image": "images/265.jpg", "choices": null, "unit": null, "precision": null, "answer": "11", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 136, "img_width": 329, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: <PERSON><PERSON>'s P.E. class participated in a push-up competition, and <PERSON><PERSON> wrote down how many push-ups each person could do. How many people did at least 60 push-ups? (Unit: people)"}, {"pid": "266", "question": "Which has the most uneven shape?", "image": "images/266.jpg", "choices": ["oblique", "obtuse", "cordate", "truncate"], "unit": null, "precision": null, "answer": "oblique", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 225, "img_width": 240, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which has the most uneven shape?\nChoices:\n(A) oblique\n(B) obtuse\n(C) cordate\n(D) truncate"}, {"pid": "267", "question": "What shape of a leaf is similar to Serrate, but has smaller, evenly-spaced teeth?", "image": "images/267.jpg", "choices": ["Undulate", "Sinuate", "Serrulate", "Entire"], "unit": null, "precision": null, "answer": "Serrulate", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 306, "img_width": 529, "language": "english", "skills": ["scientific reasoning"], "source": "TQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What shape of a leaf is similar to Serrate, but has smaller, evenly-spaced teeth?\nChoices:\n(A) Undulate\n(B) Sinuate\n(C) Serrulate\n(D) Entire"}, {"pid": "268", "question": "<PERSON> wants to buy 1+3/10 kilograms of English muffins. How much will he spend? (Unit: $)", "image": "images/268.jpg", "choices": null, "unit": null, "precision": 1.0, "answer": "10.4", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "elementary school", "img_height": 194, "img_width": 273, "language": "english", "skills": ["arithmetic reasoning", "statistical reasoning"], "source": "TabMWP", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring a floating-point number with one decimal place and provide the final value, e.g., 1.2, 1.3, 1.4, at the end.\nQuestion: <PERSON> wants to buy 1+3/10 kilograms of English muffins. How much will he spend? (Unit: $)"}, {"pid": "269", "question": "As shown in the figure, the elevation angle of the top of a building is 30.0 when viewed from point A in the air by a hot air balloon, and the depression angle of this building is 60.0. The horizontal distance between the hot air balloon and the building is 120.0. The height of this building is ()", "image": "images/269.jpg", "choices": ["160m", "160√{3}m", "(160-160√{3})m", "360m"], "unit": null, "precision": null, "answer": "160√{3}m", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 159, "img_width": 133, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, the elevation angle of the top of a building is 30.0 when viewed from point A in the air by a hot air balloon, and the depression angle of this building is 60.0. The horizontal distance between the hot air balloon and the building is 120.0. The height of this building is ()\nChoices:\n(A) 160m\n(B) 160√{3}m\n(C) (160-160√{3})m\n(D) 360m"}, {"pid": "270", "question": "As shown in the figure, points A and B are three points on ⊙O and AB = AC. Connect BO and CO, if ∠ABC = 65.0, then the degree of ∠BOC is ()", "image": "images/270.jpg", "choices": ["50°", "65°", "100°", "130°"], "unit": null, "precision": null, "answer": "100°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 114, "img_width": 102, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, points A and B are three points on ⊙O and AB = AC. Connect BO and CO, if ∠ABC = 65.0, then the degree of ∠BOC is ()\nChoices:\n(A) 50°\n(B) 65°\n(C) 100°\n(D) 130°"}, {"pid": "271", "question": "Find y", "image": "images/271.jpg", "choices": ["3", "4.5", "5", "6"], "unit": null, "precision": null, "answer": "5", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 287, "img_width": 448, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Find y\nChoices:\n(A) 3\n(B) 4.5\n(C) 5\n(D) 6"}, {"pid": "272", "question": "What time does the clock show?", "image": "images/272.jpg", "choices": ["9:30", "1:30", "4:30", "5:30", "11:30"], "unit": null, "precision": null, "answer": "4:30", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 261, "img_width": 261, "language": "english", "skills": ["numeric commonsense"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What time does the clock show?\nChoices:\n(A) 9:30\n(B) 1:30\n(C) 4:30\n(D) 5:30\n(E) 11:30"}, {"pid": "273", "question": "One diagonal of a rhombus is twice as long as the other diagonal. If the area of the rhombus is 169 square millimeters, what are the lengths of the diagonals?", "image": "images/273.jpg", "choices": ["6.5", "13", "26", "52"], "unit": null, "precision": null, "answer": "26", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 237, "img_width": 347, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: One diagonal of a rhombus is twice as long as the other diagonal. If the area of the rhombus is 169 square millimeters, what are the lengths of the diagonals?\nChoices:\n(A) 6.5\n(B) 13\n(C) 26\n(D) 52"}, {"pid": "274", "question": "如图，AB、BC、CD、DA都是⊙O的切线，已知AD＝2，BC＝5，则AB+CD的值是（）", "image": "images/274.jpg", "choices": ["14", "12", "9", "7"], "unit": null, "precision": null, "answer": "7", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 119, "img_width": 151, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，AB、BC、CD、DA都是⊙O的切线，已知AD＝2，BC＝5，则AB+CD的值是（）\nChoices:\n(A) 14\n(B) 12\n(C) 9\n(D) 7"}, {"pid": "275", "question": "As shown in the figure, in Rt△ABC, ∠BAC = 90.0, AD ⊥ BC at D, DE ⊥ AB at E, AD = 3.0, DE = 2.0, then the length of CD is ()", "image": "images/275.jpg", "choices": ["\\frac{21}{2}", "\\frac{√{15}}{2}", "\\frac{9}{2}", "\\frac{3√{5}}{2}"], "unit": null, "precision": null, "answer": "\\frac{3√{5}}{2}", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 107, "img_width": 185, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, in Rt△ABC, ∠BAC = 90.0, AD ⊥ BC at D, DE ⊥ AB at E, AD = 3.0, DE = 2.0, then the length of CD is ()\nChoices:\n(A) \\frac{21}{2}\n(B) \\frac{√{15}}{2}\n(C) \\frac{9}{2}\n(D) \\frac{3√{5}}{2}"}, {"pid": "276", "question": "As shown in the figure, it is known that the radius of ⊙O is 5.0 and the chord AB = 8.0, then the distance from the center O to AB is ()", "image": "images/276.jpg", "choices": ["1mm", "2mm", "3mm", "4mm"], "unit": null, "precision": null, "answer": "3mm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 102, "img_width": 102, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, it is known that the radius of ⊙O is 5.0 and the chord AB = 8.0, then the distance from the center O to AB is ()\nChoices:\n(A) 1mm\n(B) 2mm\n(C) 3mm\n(D) 4mm"}, {"pid": "277", "question": "Which cube is identical to the unfolded net?", "image": "images/277.jpg", "choices": ["A", "B", "C", "D", "E"], "unit": null, "precision": null, "answer": "D", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "puzzle test", "grade": "elementary school", "img_height": 591, "img_width": 424, "language": "english", "skills": ["logical reasoning"], "source": "IQTest", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which cube is identical to the unfolded net?\nChoices:\n(A) A\n(B) B\n(C) C\n(D) D\n(E) E"}, {"pid": "278", "question": "Among the following objects, which one has the best PSNR score?", "image": "images/278.jpg", "choices": ["Lego", "Mats", "Mic", "Ship"], "unit": null, "precision": null, "answer": "Mic", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "table", "grade": "college", "img_height": 940, "img_width": 1478, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Among the following objects, which one has the best PSNR score?\nChoices:\n(A) Lego\n(B) Mats\n(C) Mic\n(D) Ship"}, {"pid": "279", "question": "What would be directly affected by a decrease in sunlight?", "image": "images/279.jpg", "choices": ["grass", "mouse", "grasshopper", "owl"], "unit": null, "precision": null, "answer": "grass", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 423, "img_width": 600, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: What would be directly affected by a decrease in sunlight?\nChoices:\n(A) grass\n(B) mouse\n(C) grasshopper\n(D) owl"}, {"pid": "280", "question": "In the figure above, ABCDEF is a regular hexagon, and its center is point O. What is the value of x?", "image": "images/280.jpg", "choices": ["80", "60", "40", "30", "20"], "unit": null, "precision": null, "answer": "60", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 123, "img_width": 130, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GEOS", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In the figure above, ABCDEF is a regular hexagon, and its center is point O. What is the value of x?\nChoices:\n(A) 80\n(B) 60\n(C) 40\n(D) 30\n(E) 20"}, {"pid": "281", "question": "Was this a square pizza?", "image": "images/281.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "No", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 427, "img_width": 640, "language": "english", "skills": ["numeric commonsense", "geometry reasoning"], "source": "VQA2.0", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Was this a square pizza?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "282", "question": "What percent of the sun is showing?", "image": "images/282.jpg", "choices": null, "unit": null, "precision": null, "answer": "100", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "abstract scene", "grade": "daily life", "img_height": 400, "img_width": 700, "language": "english", "skills": ["arithmetic reasoning"], "source": "VQA-AS", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What percent of the sun is showing?"}, {"pid": "283", "question": "$\\overline{WTY} \\cong \\overline{TWY}$. Find $x$.", "image": "images/283.jpg", "choices": ["2", "4", "5", "10"], "unit": null, "precision": null, "answer": "5", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 416, "img_width": 559, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: $\\overline{WTY} \\cong \\overline{TWY}$. Find $x$.\nChoices:\n(A) 2\n(B) 4\n(C) 5\n(D) 10"}, {"pid": "284", "question": "What is the accuracy of the algorithm with lowest accuracy?", "image": "images/284.jpg", "choices": null, "unit": null, "precision": null, "answer": "1", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 448, "img_width": 448, "language": "english", "skills": ["statistical reasoning"], "source": "DVQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the accuracy of the algorithm with lowest accuracy?"}, {"pid": "285", "question": "As shown in the figure, it is known that AB is the diameter of ⊙O, if the degree of ∠BOC is 50.0, then the degree of ∠A is ()", "image": "images/285.jpg", "choices": ["50°", "40°", "30°", "25°"], "unit": null, "precision": null, "answer": "25°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 110, "img_width": 100, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "UniGeo", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: As shown in the figure, it is known that AB is the diameter of ⊙O, if the degree of ∠BOC is 50.0, then the degree of ∠A is ()\nChoices:\n(A) 50°\n(B) 40°\n(C) 30°\n(D) 25°"}, {"pid": "286", "question": "如图，将一根长度为8cm，自然伸直的弹性皮筋AB两端固定在水平的桌面上，然后把皮筋中点C竖直向上拉升3cm到点D，则此时该弹性皮筋被拉长了（）", "image": "images/286.jpg", "choices": ["6cm", "5cm", "4cm", "2cm"], "unit": null, "precision": null, "answer": "2cm", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 82, "img_width": 250, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，将一根长度为8cm，自然伸直的弹性皮筋AB两端固定在水平的桌面上，然后把皮筋中点C竖直向上拉升3cm到点D，则此时该弹性皮筋被拉长了（）\nChoices:\n(A) 6cm\n(B) 5cm\n(C) 4cm\n(D) 2cm"}, {"pid": "287", "question": "Which region is larger? R1 or R2?\nA. R1\nB. R2", "image": "images/287.jpg", "choices": ["R1", "R2", "R5", "R3", "R4"], "unit": null, "precision": null, "answer": "R2", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 325, "img_width": 370, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Which region is larger? R1 or R2?\nA. R1\nB. R2\nChoices:\n(A) R1\n(B) R2\n(C) R5\n(D) R3\n(E) R4"}, {"pid": "288", "question": "In which of the following value ranges of λ2 does the percentage of Attack Effectiveness begin to be lower than that of Diversity?", "image": "images/288.jpg", "choices": ["0.0 - 0.2", "0.2 - 0.4", "0.4 - 0.6", "0.6 - 0.8", "0.8 - 1.0"], "unit": null, "precision": null, "answer": "0.0 - 0.2", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "line plot", "grade": "college", "img_height": 606, "img_width": 2144, "language": "english", "skills": ["scientific reasoning", "statistical reasoning"], "source": "PaperQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In which of the following value ranges of λ2 does the percentage of Attack Effectiveness begin to be lower than that of Diversity?\nChoices:\n(A) 0.0 - 0.2\n(B) 0.2 - 0.4\n(C) 0.4 - 0.6\n(D) 0.6 - 0.8\n(E) 0.8 - 1.0"}, {"pid": "289", "question": "Fill in the blank to describe the model. The model has 4 dots divided into 2 equal groups. There are (_) dots in each group.", "image": "images/289.jpg", "choices": null, "unit": null, "precision": null, "answer": "2", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 204, "img_width": 418, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Fill in the blank to describe the model. The model has 4 dots divided into 2 equal groups. There are (_) dots in each group."}, {"pid": "290", "question": "如图，平行线AB，CD被直线AE所截．若∠1＝105°，则∠2的度数为（）", "image": "images/290.jpg", "choices": ["75°", "85°", "95°", "105°"], "unit": null, "precision": null, "answer": "75°", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 119, "img_width": 132, "language": "chinese", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "GeoQA+", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: 如图，平行线AB，CD被直线AE所截．若∠1＝105°，则∠2的度数为（）\nChoices:\n(A) 75°\n(B) 85°\n(C) 95°\n(D) 105°"}, {"pid": "291", "question": "In which period the number of full time employees is the maximum?", "image": "images/291.jpg", "choices": ["Jul '21", "Jun '21", "Mar '21", "May '21", "Apr '21"], "unit": null, "precision": null, "answer": "May '21", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "line plot", "grade": "daily life", "img_height": 557, "img_width": 800, "language": "english", "skills": ["statistical reasoning"], "source": "ChartQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: In which period the number of full time employees is the maximum?\nChoices:\n(A) Jul '21\n(B) Jun '21\n(C) Mar '21\n(D) May '21\n(E) Apr '21"}, {"pid": "292", "question": "Is <PERSON> greater than <PERSON>?", "image": "images/292.jpg", "choices": ["yes", "no"], "unit": null, "precision": null, "answer": "no", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "bar chart", "grade": "daily life", "img_height": 400, "img_width": 461, "language": "english", "skills": ["statistical reasoning"], "source": "FigureQA", "split": "testmini", "task": "figure question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is <PERSON> greater than <PERSON>?\nChoices:\n(A) yes\n(B) no"}, {"pid": "293", "question": "From the above food web diagram, grasshopper population increase if", "image": "images/293.jpg", "choices": ["grouse decrease", "chipmunk increases", "grasses increases", "elk increase"], "unit": null, "precision": null, "answer": "grasses increases", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 156, "img_width": 456, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: From the above food web diagram, grasshopper population increase if\nChoices:\n(A) grouse decrease\n(B) chipmunk increases\n(C) grasses increases\n(D) elk increase"}, {"pid": "294", "question": "In Fig. 21-25, the particles have charges $q_1=-q_2=100 \\mathrm{nC}$ and $q_3=-q_4=200 \\mathrm{nC}$, and distance $a=$ $5.0 \\mathrm{~cm}$. What is the $x$ component of the net electrostatic force on particle 3?", "image": "images/294.jpg", "choices": null, "unit": null, "precision": 2.0, "answer": "0.17", "question_type": "free_form", "answer_type": "float", "metadata": {"category": "math-targeted-vqa", "context": "scientific figure", "grade": "college", "img_height": 293, "img_width": 247, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning", "scientific reasoning"], "source": "SciBench", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question requiring a floating-point number with two decimal places and provide the final value, e.g., 1.23, 1.34, 1.45, at the end.\nQuestion: In Fig. 21-25, the particles have charges $q_1=-q_2=100 \\mathrm{nC}$ and $q_3=-q_4=200 \\mathrm{nC}$, and distance $a=$ $5.0 \\mathrm{~cm}$. What is the $x$ component of the net electrostatic force on particle 3?"}, {"pid": "295", "question": "Fill in the blank to describe the model. The model has 9 dots divided into 3 equal groups. There are (_) dots in each group.", "image": "images/295.jpg", "choices": null, "unit": null, "precision": null, "answer": "3", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "math-targeted-vqa", "context": "abstract scene", "grade": "elementary school", "img_height": 204, "img_width": 633, "language": "english", "skills": ["arithmetic reasoning"], "source": "IconQA", "split": "testmini", "task": "math word problem"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: Fill in the blank to describe the model. The model has 9 dots divided into 3 equal groups. There are (_) dots in each group."}, {"pid": "296", "question": "The value of f(-3) is ____ the value of f(2)", "image": "images/296.jpg", "choices": ["larger than", "equal to", "smaller than"], "unit": null, "precision": null, "answer": "equal to", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "function plot", "grade": "college", "img_height": 776, "img_width": 1430, "language": "english", "skills": ["algebraic reasoning"], "source": "FunctionQA", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: The value of f(-3) is ____ the value of f(2)\nChoices:\n(A) larger than\n(B) equal to\n(C) smaller than"}, {"pid": "297", "question": "Is the number of green buss greater than the number of blue school buss?", "image": "images/297.jpg", "choices": ["Yes", "No"], "unit": null, "precision": null, "answer": "Yes", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "synthetic scene", "grade": "daily life", "img_height": 480, "img_width": 640, "language": "english", "skills": ["arithmetic reasoning"], "source": "Super-CLEVR", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: Is the number of green buss greater than the number of blue school buss?\nChoices:\n(A) Yes\n(B) No"}, {"pid": "298", "question": "A decrease in rabbits would affect whose food source?", "image": "images/298.jpg", "choices": ["mountain lion", "producer", "decomposer", "energy"], "unit": null, "precision": null, "answer": "mountain lion", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "general-vqa", "context": "scientific figure", "grade": "high school", "img_height": 699, "img_width": 768, "language": "english", "skills": ["scientific reasoning"], "source": "AI2D", "split": "testmini", "task": "textbook question answering"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: A decrease in rabbits would affect whose food source?\nChoices:\n(A) mountain lion\n(B) producer\n(C) decomposer\n(D) energy"}, {"pid": "299", "question": "What is the age gap between the center and the rightmost person?", "image": "images/299.jpg", "choices": null, "unit": "years", "precision": null, "answer": "22", "question_type": "free_form", "answer_type": "integer", "metadata": {"category": "general-vqa", "context": "natural image", "grade": "daily life", "img_height": 1067, "img_width": 1600, "language": "english", "skills": ["numeric commonsense", "arithmetic reasoning"], "source": "KVQA", "split": "testmini", "task": "visual question answering"}, "query": "Hint: Please answer the question requiring an integer answer and provide the final value, e.g., 1, 2, 3, at the end.\nQuestion: What is the age gap between the center and the rightmost person? (Unit: years)"}, {"pid": "300", "question": "$\\overline{HK}$ and $\\overline{IG}$ are diameters of $\\odot L$. Find $m \\widehat {IHJ}$.", "image": "images/300.jpg", "choices": ["59", "135", "270", "301"], "unit": null, "precision": null, "answer": "270", "question_type": "multi_choice", "answer_type": "text", "metadata": {"category": "math-targeted-vqa", "context": "geometry diagram", "grade": "high school", "img_height": 492, "img_width": 510, "language": "english", "skills": ["geometry reasoning", "algebraic reasoning"], "source": "Geometry3K", "split": "testmini", "task": "geometry problem solving"}, "query": "Hint: Please answer the question and provide the correct option letter, e.g., A, B, C, D, at the end.\nQuestion: $\\overline{HK}$ and $\\overline{IG}$ are diameters of $\\odot L$. Find $m \\widehat {IHJ}$.\nChoices:\n(A) 59\n(B) 135\n(C) 270\n(D) 301"}]