{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import json\n", "from tqdm import tqdm\n", "from glob import glob\n", "from octotools.engine.openai import ChatOpenAI\n", "\n", "exp_code = 'YOUR_EXPERIMENT_LABEL'\n", "\n", "local_llm_engine = ChatOpenAI(model_string=\"gpt-3.5-turbo\", is_multimodal=False, enable_cache=True)\n", "\n", "demo_prompt = \"\"\"\n", "You are grading responses to questions about radiology. You will be given the question, the response, and the correct answer and you will respond with CORRECT or INCORRECT only with NO ADDITIONAL EXPLANATION. THe response is generally much longer than the correct answer and it is up to you to determine if the response encompasses the correct answer. If the response encompasses or contains the correct answer (synonyms and rephrasings are allowed), you should respond with CORRECT. If the response does not contain the correct answer, you should respond with INCORRECT. If the response refuses or is unable to answer the question and does not contain the correct answer, you should respond with INCORRECT.\n", "\"\"\"\n", "\n", "def create_test_prompt(demo_prompt, question, response, answer):\n", "    demo_prompt = demo_prompt.strip()\n", "    test_prompt = f\"Question:\\n{question}\\n\\nResponse:\\n{response}\\n\\nCorrect Answer:\\n{answer}\"\n", "    full_prompt = f\"{demo_prompt}\\n\\n{test_prompt}\\n\\nCorrectness:\"\n", "    return full_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Agent exps\n", "results_dir = f'../results/{exp_code}'\n", "outputs = glob(f'{results_dir}/*.json')\n", "\n", "use_col = 'direct_output'\n", "\n", "items = []\n", "for output in tqdm(outputs):\n", "    item_id = os.path.splitext(os.path.basename(output))[0]\n", "    with open(output, 'r') as f:\n", "        out = json.load(f)\n", "\n", "    output = out[use_col]\n", "\n", "    question = out.get('query', out.get('question', ''))\n", "    full_prompt = create_test_prompt(demo_prompt, question, output, out['answer'])\n", "    extraction = local_llm_engine(full_prompt)\n", "\n", "    item = {\n", "        'id': item_id,\n", "        'question': question,\n", "        'correct': out['answer'],\n", "        'extracted_choice': extraction,\n", "        'response': output,\n", "        'use_col': use_col\n", "    }\n", "    items.append(item)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(items)\n", "print((df['extracted_choice'] == 'CORRECT').mean())"]}], "metadata": {"kernelspec": {"display_name": "opentools-path", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}