[{"pid": "0", "question": "Determine all the sets of six consecutive positive integers such that the product of some two of them, added to the product of some other two of them is equal to the product of the remaining two numbers.", "answer": "The sets of six consecutive positive integers that satisfy the given condition are:\n\\[ \\{2, 3, 4, 5, 6, 7", "image": null, "query": "Determine all the sets of six consecutive positive integers such that the product of some two of them, added to the product of some other two of them is equal to the product of the remaining two numbers."}, {"pid": "1", "question": "Does there exist positive reals $a_0, a_1,\\ldots ,a_{19}$, such that the polynomial $P(x)=x^{20}+a_{19}x^{19}+\\ldots +a_1x+a_0$ does not have any real roots, yet all polynomials formed from swapping any two coefficients $a_i,a_j$ has at least one real root?", "answer": "\\text{Yes}", "image": null, "query": "Does there exist positive reals $a_0, a_1,\\ldots ,a_{19}$, such that the polynomial $P(x)=x^{20}+a_{19}x^{19}+\\ldots +a_1x+a_0$ does not have any real roots, yet all polynomials formed from swapping any two coefficients $a_i,a_j$ has at least one real root?"}, {"pid": "2", "question": "<PERSON><PERSON><PERSON><PERSON> has some raisins. After giving some away and eating some, she has 16 left. How many did she start with?", "answer": "54", "image": null, "query": "<PERSON><PERSON><PERSON><PERSON> has some raisins. After giving some away and eating some, she has 16 left. How many did she start with?"}, {"pid": "3", "question": "Compare $\\tan(\\sin x)$ with $\\sin(\\tan x)$, for $x\\in \\left]0,\\frac{\\pi}{2}\\right[$.", "answer": "\\tan(\\sin x) > \\sin(\\tan x) \\text{ for } x \\in \\left(0, \\frac{\\pi}{2}\\right)", "image": null, "query": "Compare $\\tan(\\sin x)$ with $\\sin(\\tan x)$, for $x\\in \\left]0,\\frac{\\pi}{2}\\right[$."}, {"pid": "4", "question": "The average (mean) of two numbers is 7. One of the numbers is 5. What is the other number?", "answer": "9", "image": null, "query": "The average (mean) of two numbers is 7. One of the numbers is 5. What is the other number?"}, {"pid": "5", "question": "Solve the following system of equations:\n$$x+\\frac{1}{x^3}=2y,\\quad y+\\frac{1}{y^3}=2z,\\quad z+\\frac{1}{z^3}=2w,\\quad w+\\frac{1}{w^3}=2x.$$", "answer": "(1, 1, 1, 1) \\text{ and } (-1, -1, -1, -1)", "image": null, "query": "Solve the following system of equations:\n$$x+\\frac{1}{x^3}=2y,\\quad y+\\frac{1}{y^3}=2z,\\quad z+\\frac{1}{z^3}=2w,\\quad w+\\frac{1}{w^3}=2x.$$"}, {"pid": "6", "question": "What is the smallest positive integer $t$ such that there exist integers $x_1,x_2,\\ldots,x_t$ with  \\[x^3_1+x^3_2+\\,\\ldots\\,+x^3_t=2002^{2002}\\,?\\]", "answer": "4", "image": null, "query": "What is the smallest positive integer $t$ such that there exist integers $x_1,x_2,\\ldots,x_t$ with  \\[x^3_1+x^3_2+\\,\\ldots\\,+x^3_t=2002^{2002}\\,?\\]"}, {"pid": "7", "question": "An omino is a 1-by-1 square or a 1-by-2 horizontal rectangle. An omino tiling of a region of the plane is a way of covering it (and only it) by ominoes. How many omino tilings are there of a 2-by-10 horizontal rectangle?", "answer": "7921", "image": null, "query": "An omino is a 1-by-1 square or a 1-by-2 horizontal rectangle. An omino tiling of a region of the plane is a way of covering it (and only it) by ominoes. How many omino tilings are there of a 2-by-10 horizontal rectangle?"}, {"pid": "8", "question": "What is the smallest integer $n$ , greater than one, for which the root-mean-square of the first $n$ positive integers is an integer?\n$\\mathbf{Note.}$ The root-mean-square of $n$ numbers $a_1, a_2, \\cdots, a_n$ is defined to be \\[\\left[\\frac{a_1^2 + a_2^2 + \\cdots + a_n^2}n\\right]^{1/2}\\]", "answer": "\\(\\boxed{337}\\)", "image": null, "query": "What is the smallest integer $n$ , greater than one, for which the root-mean-square of the first $n$ positive integers is an integer?\n$\\mathbf{Note.}$ The root-mean-square of $n$ numbers $a_1, a_2, \\cdots, a_n$ is defined to be \\[\\left[\\frac{a_1^2 + a_2^2 + \\cdots + a_n^2}n\\right]^{1/2}\\]"}, {"pid": "9", "question": "In convex quadrilateral $ ABCD$, $ AB\\equal{}a$, $ BC\\equal{}b$, $ CD\\equal{}c$, $ DA\\equal{}d$, $ AC\\equal{}e$, $ BD\\equal{}f$. If $ \\max \\{a,b,c,d,e,f \\}\\equal{}1$, then find the maximum value of $ abcd$.", "answer": "2 - \\sqrt{3}", "image": null, "query": "In convex quadrilateral $ ABCD$, $ AB\\equal{}a$, $ BC\\equal{}b$, $ CD\\equal{}c$, $ DA\\equal{}d$, $ AC\\equal{}e$, $ BD\\equal{}f$. If $ \\max \\{a,b,c,d,e,f \\}\\equal{}1$, then find the maximum value of $ abcd$."}, {"pid": "10", "question": "Find all pairs $(x,y)$ of nonnegative integers that satisfy \\[x^3y+x+y=xy+2xy^2.\\]", "answer": "(0, 0), (1, 1), (2, 2)", "image": null, "query": "Find all pairs $(x,y)$ of nonnegative integers that satisfy \\[x^3y+x+y=xy+2xy^2.\\]"}, {"pid": "11", "question": "The side lengths of a triangle are distinct positive integers. One of the side lengths is a multiple of 42, and another is a multiple of 72. What is the minimum possible length of the third side?", "answer": "7", "image": null, "query": "The side lengths of a triangle are distinct positive integers. One of the side lengths is a multiple of 42, and another is a multiple of 72. What is the minimum possible length of the third side?"}, {"pid": "12", "question": "Let $f(n)$ be the number of distinct prime divisors of $n$ less than 6. Compute $$\\sum_{n=1}^{2020} f(n)^{2}$$", "answer": "3431", "image": null, "query": "Let $f(n)$ be the number of distinct prime divisors of $n$ less than 6. Compute $$\\sum_{n=1}^{2020} f(n)^{2}$$"}, {"pid": "13", "question": "There are 2017 frogs and 2017 toads in a room. Each frog is friends with exactly 2 distinct toads. Let $N$ be the number of ways to pair every frog with a toad who is its friend, so that no toad is paired with more than one frog. Let $D$ be the number of distinct possible values of $N$, and let $S$ be the sum of all possible values of $N$. Find the ordered pair $(D, S)$.", "answer": "(1009, 2^{1009}-2)", "image": null, "query": "There are 2017 frogs and 2017 toads in a room. Each frog is friends with exactly 2 distinct toads. Let $N$ be the number of ways to pair every frog with a toad who is its friend, so that no toad is paired with more than one frog. Let $D$ be the number of distinct possible values of $N$, and let $S$ be the sum of all possible values of $N$. Find the ordered pair $(D, S)$."}, {"pid": "14", "question": "A number $p$ is $perfect$ if the sum of its divisors, except $p$ is $p$. Let $f$ be a function such that:\n\n$f(n)=0$, if n is perfect\n$f(n)=0$, if the last digit of n is 4\n$f(a.b)=f(a)+f(b)$\n\nFind $f(1998)$", "answer": "0", "image": null, "query": "A number $p$ is $perfect$ if the sum of its divisors, except $p$ is $p$. Let $f$ be a function such that:\n\n$f(n)=0$, if n is perfect\n$f(n)=0$, if the last digit of n is 4\n$f(a.b)=f(a)+f(b)$\n\nFind $f(1998)$"}, {"pid": "15", "question": "Let $x, y$ be complex numbers such that \\frac{x^{2}+y^{2}}{x+y}=4$ and \\frac{x^{4}+y^{4}}{x^{3}+y^{3}}=2$. Find all possible values of \\frac{x^{6}+y^{6}}{x^{5}+y^{5}}$.", "answer": "10 \\pm 2 \\sqrt{17}", "image": null, "query": "Let $x, y$ be complex numbers such that \\frac{x^{2}+y^{2}}{x+y}=4$ and \\frac{x^{4}+y^{4}}{x^{3}+y^{3}}=2$. Find all possible values of \\frac{x^{6}+y^{6}}{x^{5}+y^{5}}$."}, {"pid": "16", "question": "Let $n$ be a positive integer. At most how many distinct unit vectors can be selected in $\\mathbb{R}^{n}$ such that from any three of them, at least two are orthogonal?", "answer": "2n", "image": null, "query": "Let $n$ be a positive integer. At most how many distinct unit vectors can be selected in $\\mathbb{R}^{n}$ such that from any three of them, at least two are orthogonal?"}, {"pid": "17", "question": "Let $c>0$ be a given positive real and $\\mathbb{R}_{>0}$ be the set of all positive reals. Find all functions $f \\colon \\mathbb{R}_{>0} \\to \\mathbb{R}_{>0}$ such that \\[f((c+1)x+f(y))=f(x+2y)+2cx \\quad \\textrm{for all } x,y \\in \\mathbb{R}_{>0}.\\]", "answer": "f(x) = 2x", "image": null, "query": "Let $c>0$ be a given positive real and $\\mathbb{R}_{>0}$ be the set of all positive reals. Find all functions $f \\colon \\mathbb{R}_{>0} \\to \\mathbb{R}_{>0}$ such that \\[f((c+1)x+f(y))=f(x+2y)+2cx \\quad \\textrm{for all } x,y \\in \\mathbb{R}_{>0}.\\]"}, {"pid": "18", "question": "For each \\(i \\in\\{1, \\ldots, 10\\}, a_{i}\\) is chosen independently and uniformly at random from \\([0, i^{2}]\\). Let \\(P\\) be the probability that \\(a_{1}<a_{2}<\\cdots<a_{10}\\). Estimate \\(P\\).", "answer": "0.003679", "image": null, "query": "For each \\(i \\in\\{1, \\ldots, 10\\}, a_{i}\\) is chosen independently and uniformly at random from \\([0, i^{2}]\\). Let \\(P\\) be the probability that \\(a_{1}<a_{2}<\\cdots<a_{10}\\). Estimate \\(P\\)."}, {"pid": "19", "question": "Find the smallest integer $n$ such that $\\sqrt{n+99}-\\sqrt{n}<1$.", "answer": "2402", "image": null, "query": "Find the smallest integer $n$ such that $\\sqrt{n+99}-\\sqrt{n}<1$."}, {"pid": "20", "question": "In the $5 \\times 5$ grid shown, 15 cells contain X's and 10 cells are empty. What is the smallest number of X's that must be moved so that each row and each column contains exactly three X's?", "answer": "2", "image": null, "query": "In the $5 \\times 5$ grid shown, 15 cells contain X's and 10 cells are empty. What is the smallest number of X's that must be moved so that each row and each column contains exactly three X's?"}, {"pid": "21", "question": "Let $S$ be the set of lattice points inside the circle $x^{2}+y^{2}=11$. Let $M$ be the greatest area of any triangle with vertices in $S$. How many triangles with vertices in $S$ have area $M$?", "answer": "16", "image": null, "query": "Let $S$ be the set of lattice points inside the circle $x^{2}+y^{2}=11$. Let $M$ be the greatest area of any triangle with vertices in $S$. How many triangles with vertices in $S$ have area $M$?"}, {"pid": "22", "question": "A sequence $\\left\\{a_{n}\\right\\}_{n \\geq 0}$ of real numbers satisfies the recursion $a_{n+1}=a_{n}^{3}-3 a_{n}^{2}+3$ for all positive integers $n$. For how many values of $a_{0}$ does $a_{2007}=a_{0}$ ?", "answer": "3^{2007}", "image": null, "query": "A sequence $\\left\\{a_{n}\\right\\}_{n \\geq 0}$ of real numbers satisfies the recursion $a_{n+1}=a_{n}^{3}-3 a_{n}^{2}+3$ for all positive integers $n$. For how many values of $a_{0}$ does $a_{2007}=a_{0}$ ?"}, {"pid": "23", "question": "Which of the following is equal to $9^{4}$?", "answer": "3^{8}", "image": null, "query": "Which of the following is equal to $9^{4}$?"}, {"pid": "24", "question": "Let $A B$ be a segment of length 2 with midpoint $M$. Consider the circle with center $O$ and radius $r$ that is externally tangent to the circles with diameters $A M$ and $B M$ and internally tangent to the circle with diameter $A B$. Determine the value of $r$.", "answer": "\\frac{1}{3}", "image": null, "query": "Let $A B$ be a segment of length 2 with midpoint $M$. Consider the circle with center $O$ and radius $r$ that is externally tangent to the circles with diameters $A M$ and $B M$ and internally tangent to the circle with diameter $A B$. Determine the value of $r$."}, {"pid": "25", "question": "A finite set $S$ of points in the coordinate plane is called [i]overdetermined[/i] if $|S|\\ge 2$ and there exists a nonzero polynomial $P(t)$, with real coefficients and of degree at most $|S|-2$, satisfying $P(x)=y$ for every point $(x,y)\\in S$. \n \nFor each integer $n\\ge 2$, find the largest integer $k$ (in terms of $n$) such that there exists a set of $n$ distinct points that is [i]not[/i] overdetermined, but has $k$ overdetermined subsets.", "answer": "2^{n-1} - n", "image": null, "query": "A finite set $S$ of points in the coordinate plane is called [i]overdetermined[/i] if $|S|\\ge 2$ and there exists a nonzero polynomial $P(t)$, with real coefficients and of degree at most $|S|-2$, satisfying $P(x)=y$ for every point $(x,y)\\in S$. \n \nFor each integer $n\\ge 2$, find the largest integer $k$ (in terms of $n$) such that there exists a set of $n$ distinct points that is [i]not[/i] overdetermined, but has $k$ overdetermined subsets."}, {"pid": "26", "question": "<PERSON><PERSON> is a farmer, and she's building a cao (a relative of the cow) pasture. She starts with a triangle $A_{0} A_{1} A_{2}$ where angle $A_{0}$ is $90^{\\circ}$, angle $A_{1}$ is $60^{\\circ}$, and $A_{0} A_{1}$ is 1. She then extends the pasture. First, she extends $A_{2} A_{0}$ to $A_{3}$ such that $A_{3} A_{0}=\\frac{1}{2} A_{2} A_{0}$ and the new pasture is triangle $A_{1} A_{2} A_{3}$. Next, she extends $A_{3} A_{1}$ to $A_{4}$ such that $A_{4} A_{1}=\\frac{1}{6} A_{3} A_{1}$. She continues, each time extending $A_{n} A_{n-2}$ to $A_{n+1}$ such that $A_{n+1} A_{n-2}=\\frac{1}{2^{n}-2} A_{n} A_{n-2}$. What is the smallest $K$ such that her pasture never exceeds an area of $K$?", "answer": "\\sqrt{3}", "image": null, "query": "<PERSON><PERSON> is a farmer, and she's building a cao (a relative of the cow) pasture. She starts with a triangle $A_{0} A_{1} A_{2}$ where angle $A_{0}$ is $90^{\\circ}$, angle $A_{1}$ is $60^{\\circ}$, and $A_{0} A_{1}$ is 1. She then extends the pasture. First, she extends $A_{2} A_{0}$ to $A_{3}$ such that $A_{3} A_{0}=\\frac{1}{2} A_{2} A_{0}$ and the new pasture is triangle $A_{1} A_{2} A_{3}$. Next, she extends $A_{3} A_{1}$ to $A_{4}$ such that $A_{4} A_{1}=\\frac{1}{6} A_{3} A_{1}$. She continues, each time extending $A_{n} A_{n-2}$ to $A_{n+1}$ such that $A_{n+1} A_{n-2}=\\frac{1}{2^{n}-2} A_{n} A_{n-2}$. What is the smallest $K$ such that her pasture never exceeds an area of $K$?"}, {"pid": "27", "question": "<PERSON><PERSON>'s favorite number is a positive integer, and Stebby<PERSON> is trying to guess what it is. <PERSON><PERSON> tells her that when expressed in decimal without any leading zeros, her favorite number satisfies the following: - Adding 1 to the number results in an integer divisible by 210 . - The sum of the digits of the number is twice its number of digits. - The number has no more than 12 digits. - The number alternates in even and odd digits. Given this information, what are all possible values of <PERSON><PERSON>'s favorite number?", "answer": "1010309", "image": null, "query": "<PERSON><PERSON>'s favorite number is a positive integer, and Stebby<PERSON> is trying to guess what it is. <PERSON><PERSON> tells her that when expressed in decimal without any leading zeros, her favorite number satisfies the following: - Adding 1 to the number results in an integer divisible by 210 . - The sum of the digits of the number is twice its number of digits. - The number has no more than 12 digits. - The number alternates in even and odd digits. Given this information, what are all possible values of <PERSON><PERSON>'s favorite number?"}, {"pid": "28", "question": "<PERSON><PERSON><PERSON> has a wooden cube. In each step, she creates a new polyhedron from the previous one by cutting off a pyramid from each vertex of the polyhedron along a plane through the trisection point on each adjacent edge that is closer to the vertex. For example, the polyhedron after the first step has six octagonal faces and eight equilateral triangular faces. How many faces are on the polyhedron after the fifth step?", "answer": "974", "image": null, "query": "<PERSON><PERSON><PERSON> has a wooden cube. In each step, she creates a new polyhedron from the previous one by cutting off a pyramid from each vertex of the polyhedron along a plane through the trisection point on each adjacent edge that is closer to the vertex. For example, the polyhedron after the first step has six octagonal faces and eight equilateral triangular faces. How many faces are on the polyhedron after the fifth step?"}, {"pid": "29", "question": "Define the sequence $f_{1}, f_{2}, \\ldots:[0,1) \\rightarrow \\mathbb{R}$ of continuously differentiable functions by the following recurrence: $$ f_{1}=1 ; \\quad f_{n+1}^{\\prime}=f_{n} f_{n+1} \\quad \\text { on }(0,1), \\quad \\text { and } \\quad f_{n+1}(0)=1 $$ Show that \\(\\lim _{n \\rightarrow \\infty} f_{n}(x)\\) exists for every $x \\in[0,1)$ and determine the limit function.", "answer": "\\frac{1}{1-x}", "image": null, "query": "Define the sequence $f_{1}, f_{2}, \\ldots:[0,1) \\rightarrow \\mathbb{R}$ of continuously differentiable functions by the following recurrence: $$ f_{1}=1 ; \\quad f_{n+1}^{\\prime}=f_{n} f_{n+1} \\quad \\text { on }(0,1), \\quad \\text { and } \\quad f_{n+1}(0)=1 $$ Show that \\(\\lim _{n \\rightarrow \\infty} f_{n}(x)\\) exists for every $x \\in[0,1)$ and determine the limit function."}, {"pid": "30", "question": "Find $x_{2012}$ given that $x_{n+1}=2x_{n}-x_{n-1}+2^{n}$ and $x_{1}=1$, $x_{2}=2$.", "answer": "2^{2013}-6036", "image": null, "query": "Find $x_{2012}$ given that $x_{n+1}=2x_{n}-x_{n-1}+2^{n}$ and $x_{1}=1$, $x_{2}=2$."}, {"pid": "31", "question": "A water tower in the shape of a cylinder has radius 10 m and height 30 m. A spiral staircase, with constant slope, circles once around the outside of the water tower. A vertical ladder of height 5 m then extends to the top of the tower. What is the total distance along the staircase and up the ladder to the top of the tower?", "answer": "72.6 \\mathrm{~m}", "image": null, "query": "A water tower in the shape of a cylinder has radius 10 m and height 30 m. A spiral staircase, with constant slope, circles once around the outside of the water tower. A vertical ladder of height 5 m then extends to the top of the tower. What is the total distance along the staircase and up the ladder to the top of the tower?"}, {"pid": "32", "question": "Find the smallest prime number $p$ that cannot be represented in the form $|3^{a} - 2^{b}|$, where $a$ and $b$ are non-negative integers.", "answer": "41", "image": null, "query": "Find the smallest prime number $p$ that cannot be represented in the form $|3^{a} - 2^{b}|$, where $a$ and $b$ are non-negative integers."}, {"pid": "33", "question": "The walls of a room are in the shape of a triangle $A B C$ with $\\angle A B C=90^{\\circ}, \\angle B A C=60^{\\circ}$, and $A B=6$. Cho<PERSON> stands at the midpoint of $B C$ and rolls a ball toward $A B$. Suppose that the ball bounces off $A B$, then $A C$, then returns exactly to Chong. Find the length of the path of the ball.", "answer": "3\\sqrt{21}", "image": null, "query": "The walls of a room are in the shape of a triangle $A B C$ with $\\angle A B C=90^{\\circ}, \\angle B A C=60^{\\circ}$, and $A B=6$. Cho<PERSON> stands at the midpoint of $B C$ and rolls a ball toward $A B$. Suppose that the ball bounces off $A B$, then $A C$, then returns exactly to Chong. Find the length of the path of the ball."}, {"pid": "34", "question": "If $2 x^{2}=9 x-4$ and $x \neq 4$, what is the value of $2 x$?", "answer": "1", "image": null, "query": "If $2 x^{2}=9 x-4$ and $x \neq 4$, what is the value of $2 x$?"}, {"pid": "35", "question": "Suppose that $(a_{1}, \\ldots, a_{20})$ and $(b_{1}, \\ldots, b_{20})$ are two sequences of integers such that the sequence $(a_{1}, \\ldots, a_{20}, b_{1}, \\ldots, b_{20})$ contains each of the numbers $1, \\ldots, 40$ exactly once. What is the maximum possible value of the sum $\\sum_{i=1}^{20} \\sum_{j=1}^{20} \\min (a_{i}, b_{j})$?", "answer": "5530", "image": null, "query": "Suppose that $(a_{1}, \\ldots, a_{20})$ and $(b_{1}, \\ldots, b_{20})$ are two sequences of integers such that the sequence $(a_{1}, \\ldots, a_{20}, b_{1}, \\ldots, b_{20})$ contains each of the numbers $1, \\ldots, 40$ exactly once. What is the maximum possible value of the sum $\\sum_{i=1}^{20} \\sum_{j=1}^{20} \\min (a_{i}, b_{j})$?"}, {"pid": "36", "question": "A sequence of functions  $\\, \\{f_n(x) \\} \\,$ is defined recursively as follows: \\begin{align*} f_1(x) &= \\sqrt {x^2 + 48}, \\quad \\text{and} \\\\ f_{n + 1}(x) &= \\sqrt {x^2 + 6f_n(x)} \\quad \\text{for } n \\geq 1. \\end{align*} (Recall that $\\sqrt {\\makebox[5mm]{}}$ is understood to represent the positive square root .) For each positive integer $n$ , find all real solutions of the equation $\\, f_n(x) = 2x \\,$ .", "answer": "\\[ x = 4 \\]", "image": null, "query": "A sequence of functions  $\\, \\{f_n(x) \\} \\,$ is defined recursively as follows: \\begin{align*} f_1(x) &= \\sqrt {x^2 + 48}, \\quad \\text{and} \\\\ f_{n + 1}(x) &= \\sqrt {x^2 + 6f_n(x)} \\quad \\text{for } n \\geq 1. \\end{align*} (Recall that $\\sqrt {\\makebox[5mm]{}}$ is understood to represent the positive square root .) For each positive integer $n$ , find all real solutions of the equation $\\, f_n(x) = 2x \\,$ ."}, {"pid": "37", "question": "A circle passes through the points $(2,0)$ and $(4,0)$ and is tangent to the line $y=x$. Find the sum of all possible values for the $y$-coordinate of the center of the circle.", "answer": "-6", "image": null, "query": "A circle passes through the points $(2,0)$ and $(4,0)$ and is tangent to the line $y=x$. Find the sum of all possible values for the $y$-coordinate of the center of the circle."}, {"pid": "38", "question": "What is the sum of all numbers $q$ which can be written in the form $q=\\frac{a}{b}$ where $a$ and $b$ are positive integers with $b \\leq 10$ and for which there are exactly 19 integers $n$ that satisfy $\\sqrt{q}<n<q$?", "answer": "777.5", "image": null, "query": "What is the sum of all numbers $q$ which can be written in the form $q=\\frac{a}{b}$ where $a$ and $b$ are positive integers with $b \\leq 10$ and for which there are exactly 19 integers $n$ that satisfy $\\sqrt{q}<n<q$?"}, {"pid": "39", "question": "What is the value of $\frac{8+4}{8-4}$?", "answer": "3", "image": null, "query": "What is the value of $\frac{8+4}{8-4}$?"}, {"pid": "40", "question": "<PERSON> and <PERSON> are playing ping pong. For each game, there is a $30 \\%$ chance that <PERSON> wins and a $70 \\%$ chance <PERSON> wins. During a match, they play games until someone wins a total of 21 games. What is the expected value of number of games played per match?", "answer": "30", "image": null, "query": "<PERSON> and <PERSON> are playing ping pong. For each game, there is a $30 \\%$ chance that <PERSON> wins and a $70 \\%$ chance <PERSON> wins. During a match, they play games until someone wins a total of 21 games. What is the expected value of number of games played per match?"}, {"pid": "41", "question": "Find all polynomials $f(x)$ with real coefficients for which\n\\[f(x)f(2x^2) = f(2x^3 + x).\\]", "answer": "$\\boxed{f(x)=(x^2+1)^n},n\\in\\mathbb N_0$", "image": null, "query": "Find all polynomials $f(x)$ with real coefficients for which\n\\[f(x)f(2x^2) = f(2x^3 + x).\\]"}, {"pid": "42", "question": "Let $x$ be a real number. Find the maximum value of $2^{x(1-x)}$.", "answer": "\\sqrt[4]{2}", "image": null, "query": "Let $x$ be a real number. Find the maximum value of $2^{x(1-x)}$."}, {"pid": "43", "question": "In how many ways can the numbers $1,2, \\ldots, 2002$ be placed at the vertices of a regular 2002-gon so that no two adjacent numbers differ by more than 2? (Rotations and reflections are considered distinct.)", "answer": "4004", "image": null, "query": "In how many ways can the numbers $1,2, \\ldots, 2002$ be placed at the vertices of a regular 2002-gon so that no two adjacent numbers differ by more than 2? (Rotations and reflections are considered distinct.)"}, {"pid": "44", "question": "What is the probability that in a randomly chosen arrangement of the numbers and letters in \"HMMT2005,\" one can read either \"HMMT\" or \"2005\" from left to right?", "answer": "23/144", "image": null, "query": "What is the probability that in a randomly chosen arrangement of the numbers and letters in \"HMMT2005,\" one can read either \"HMMT\" or \"2005\" from left to right?"}, {"pid": "45", "question": "<PERSON> picked an arbitrary positive integer, multiplied it by 5, multiplied the result by 5, then multiplied the result by 5 again and so on. Is it true that from some moment all the numbers that <PERSON> obtains contain 5 in their decimal representation?", "answer": "\\text{True}", "image": null, "query": "<PERSON> picked an arbitrary positive integer, multiplied it by 5, multiplied the result by 5, then multiplied the result by 5 again and so on. Is it true that from some moment all the numbers that <PERSON> obtains contain 5 in their decimal representation?"}, {"pid": "46", "question": "A [i]permutation[/i] of the set of positive integers $[n] = \\{1, 2, . . . , n\\}$ is a sequence $(a_1 , a_2 , \\ldots, a_n ) $ such that each element of $[n]$ appears precisely one time as a term of the sequence. For example, $(3, 5, 1, 2, 4)$ is a permutation of $[5]$. Let $P (n)$ be the number of permutations of $[n]$ for which $ka_k$ is a perfect square for all $1 \\leq k \\leq n$. Find with proof the smallest $n$ such that $P (n)$ is a multiple of $2010$.", "answer": "4489", "image": null, "query": "A [i]permutation[/i] of the set of positive integers $[n] = \\{1, 2, . . . , n\\}$ is a sequence $(a_1 , a_2 , \\ldots, a_n ) $ such that each element of $[n]$ appears precisely one time as a term of the sequence. For example, $(3, 5, 1, 2, 4)$ is a permutation of $[5]$. Let $P (n)$ be the number of permutations of $[n]$ for which $ka_k$ is a perfect square for all $1 \\leq k \\leq n$. Find with proof the smallest $n$ such that $P (n)$ is a multiple of $2010$."}, {"pid": "47", "question": "Find the number of ways to distribute 4 pieces of candy to 12 children such that no two consecutive children receive candy.", "answer": "105", "image": null, "query": "Find the number of ways to distribute 4 pieces of candy to 12 children such that no two consecutive children receive candy."}, {"pid": "48", "question": "How many positive integers $k$ are there such that $$\\frac{k}{2013}(a+b)=\\operatorname{lcm}(a, b)$$ has a solution in positive integers $(a, b)$?", "answer": "1006", "image": null, "query": "How many positive integers $k$ are there such that $$\\frac{k}{2013}(a+b)=\\operatorname{lcm}(a, b)$$ has a solution in positive integers $(a, b)$?"}, {"pid": "49", "question": "Starting with an empty string, we create a string by repeatedly appending one of the letters $H, M, T$ with probabilities $\\frac{1}{4}, \\frac{1}{2}, \\frac{1}{4}$, respectively, until the letter $M$ appears twice consecutively. What is the expected value of the length of the resulting string?", "answer": "6", "image": null, "query": "Starting with an empty string, we create a string by repeatedly appending one of the letters $H, M, T$ with probabilities $\\frac{1}{4}, \\frac{1}{2}, \\frac{1}{4}$, respectively, until the letter $M$ appears twice consecutively. What is the expected value of the length of the resulting string?"}, {"pid": "50", "question": "There is a grid of height 2 stretching infinitely in one direction. Between any two edge-adjacent cells of the grid, there is a door that is locked with probability $\\frac{1}{2}$ independent of all other doors. <PERSON> starts in a corner of the grid (in the starred cell). Compute the expected number of cells that <PERSON> can reach, assuming he can only travel between cells if the door between them is unlocked.", "answer": "\\frac{32}{7}", "image": null, "query": "There is a grid of height 2 stretching infinitely in one direction. Between any two edge-adjacent cells of the grid, there is a door that is locked with probability $\\frac{1}{2}$ independent of all other doors. <PERSON> starts in a corner of the grid (in the starred cell). Compute the expected number of cells that <PERSON> can reach, assuming he can only travel between cells if the door between them is unlocked."}, {"pid": "51", "question": "One hundred people are in line to see a movie. Each person wants to sit in the front row, which contains one hundred seats, and each has a favorite seat, chosen randomly and independently. They enter the row one at a time from the far right. As they walk, if they reach their favorite seat, they sit, but to avoid stepping over people, if they encounter a person already seated, they sit to that person's right. If the seat furthest to the right is already taken, they sit in a different row. What is the most likely number of people that will get to sit in the first row?", "answer": "10", "image": null, "query": "One hundred people are in line to see a movie. Each person wants to sit in the front row, which contains one hundred seats, and each has a favorite seat, chosen randomly and independently. They enter the row one at a time from the far right. As they walk, if they reach their favorite seat, they sit, but to avoid stepping over people, if they encounter a person already seated, they sit to that person's right. If the seat furthest to the right is already taken, they sit in a different row. What is the most likely number of people that will get to sit in the first row?"}, {"pid": "52", "question": "Given positive integer $n$ and $r$ pairwise distinct primes $p_1,p_2,\\cdots,p_r.$ Initially, there are $(n+1)^r$ numbers written on the blackboard: $p_1^{i_1}p_2^{i_2}\\cdots p_r^{i_r} (0 \\le i_1,i_2,\\cdots,i_r \\le n).$\n\n<PERSON> and <PERSON> play a game by making a move by turns, with <PERSON> going first. In <PERSON>'s round, she erases two numbers $a,b$ (not necessarily different) and write $\\gcd(a,b)$. In <PERSON>'s round, he erases two numbers $a,b$ (not necessarily different) and write $\\mathrm{lcm} (a,b)$. The game ends when only one number remains on the blackboard.\n\nDetermine the minimal possible $M$ such that <PERSON> could guarantee the remaining number no greater than $M$, regardless of <PERSON>'s move.", "answer": "M^{\\lfloor \\frac{n}{2} \\rfloor}", "image": null, "query": "Given positive integer $n$ and $r$ pairwise distinct primes $p_1,p_2,\\cdots,p_r.$ Initially, there are $(n+1)^r$ numbers written on the blackboard: $p_1^{i_1}p_2^{i_2}\\cdots p_r^{i_r} (0 \\le i_1,i_2,\\cdots,i_r \\le n).$\n\n<PERSON> and <PERSON> play a game by making a move by turns, with <PERSON> going first. In <PERSON>'s round, she erases two numbers $a,b$ (not necessarily different) and write $\\gcd(a,b)$. In <PERSON>'s round, he erases two numbers $a,b$ (not necessarily different) and write $\\mathrm{lcm} (a,b)$. The game ends when only one number remains on the blackboard.\n\nDetermine the minimal possible $M$ such that <PERSON> could guarantee the remaining number no greater than $M$, regardless of <PERSON>'s move."}, {"pid": "53", "question": "Compute the number of ways to select 99 cells of a $19 \\times 19$ square grid such that no two selected cells share an edge or vertex.", "answer": "1000", "image": null, "query": "Compute the number of ways to select 99 cells of a $19 \\times 19$ square grid such that no two selected cells share an edge or vertex."}, {"pid": "54", "question": "Suppose we have an (infinite) cone $\\mathcal{C}$ with apex $A$ and a plane $\\pi$. The intersection of $\\pi$ and $\\mathcal{C}$ is an ellipse $\\mathcal{E}$ with major axis $BC$, such that $B$ is closer to $A$ than $C$, and $BC=4, AC=5, AB=3$. Suppose we inscribe a sphere in each part of $\\mathcal{C}$ cut up by $\\mathcal{E}$ with both spheres tangent to $\\mathcal{E}$. What is the ratio of the radii of the spheres (smaller to larger)?", "answer": "\\frac{1}{3}", "image": null, "query": "Suppose we have an (infinite) cone $\\mathcal{C}$ with apex $A$ and a plane $\\pi$. The intersection of $\\pi$ and $\\mathcal{C}$ is an ellipse $\\mathcal{E}$ with major axis $BC$, such that $B$ is closer to $A$ than $C$, and $BC=4, AC=5, AB=3$. Suppose we inscribe a sphere in each part of $\\mathcal{C}$ cut up by $\\mathcal{E}$ with both spheres tangent to $\\mathcal{E}$. What is the ratio of the radii of the spheres (smaller to larger)?"}, {"pid": "55", "question": "An ant starts at the point $(0,0)$ in the Cartesian plane. In the first minute, the ant faces towards $(1,0)$ and walks one unit. Each subsequent minute, the ant chooses an angle $\\theta$ uniformly at random in the interval $\\left[-90^{\\circ}, 90^{\\circ}\\right]$, and then turns an angle of $\\theta$ clockwise (negative values of $\\theta$ correspond to counterclockwise rotations). Then, the ant walks one unit. After $n$ minutes, the ant's distance from $(0,0)$ is $d_{n}$. Let the expected value of $d_{n}^{2}$ be $a_{n}$. Compute the closest integer to $$10 \\lim _{n \\rightarrow \\infty} \\frac{a_{n}}{n}$$", "answer": "45", "image": null, "query": "An ant starts at the point $(0,0)$ in the Cartesian plane. In the first minute, the ant faces towards $(1,0)$ and walks one unit. Each subsequent minute, the ant chooses an angle $\\theta$ uniformly at random in the interval $\\left[-90^{\\circ}, 90^{\\circ}\\right]$, and then turns an angle of $\\theta$ clockwise (negative values of $\\theta$ correspond to counterclockwise rotations). Then, the ant walks one unit. After $n$ minutes, the ant's distance from $(0,0)$ is $d_{n}$. Let the expected value of $d_{n}^{2}$ be $a_{n}$. Compute the closest integer to $$10 \\lim _{n \\rightarrow \\infty} \\frac{a_{n}}{n}$$"}, {"pid": "56", "question": "Let $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}$ be real numbers whose sum is 20. Determine with proof the smallest possible value of \\(\\sum_{1 \\leq i<j \\leq 5}\\left\\lfloor a_{i}+a_{j}\\right\\rfloor\\).", "answer": "\\[\n72\n\\]", "image": null, "query": "Let $a_{1}, a_{2}, a_{3}, a_{4}, a_{5}$ be real numbers whose sum is 20. Determine with proof the smallest possible value of \\(\\sum_{1 \\leq i<j \\leq 5}\\left\\lfloor a_{i}+a_{j}\\right\\rfloor\\)."}, {"pid": "57", "question": "Determine all positive integers $n$ for which there exists an integer $m$ such that ${2^{n}-1}$ is a divisor of ${m^{2}+9}$.", "answer": "n = 2^k", "image": null, "query": "Determine all positive integers $n$ for which there exists an integer $m$ such that ${2^{n}-1}$ is a divisor of ${m^{2}+9}$."}, {"pid": "58", "question": "Find, with proof, the point $P$ in the interior of an acute-angled triangle $ABC$ for which $BL^2+CM^2+AN^2$ is a minimum, where $L,M,N$ are the feet of the perpendiculars from $P$ to $BC,CA,AB$ respectively.\n\n[i]", "answer": "P\\text{ is the circumcenter of }\\triangle{ABC}", "image": null, "query": "Find, with proof, the point $P$ in the interior of an acute-angled triangle $ABC$ for which $BL^2+CM^2+AN^2$ is a minimum, where $L,M,N$ are the feet of the perpendiculars from $P$ to $BC,CA,AB$ respectively.\n\n[i]"}, {"pid": "59", "question": "Real numbers \\(x\\) and \\(y\\) satisfy the following equations: \\(x=\\log_{10}(10^{y-1}+1)-1\\) and \\(y=\\log_{10}(10^{x}+1)-1\\). Compute \\(10^{x-y}\\).", "answer": "\\frac{101}{110}", "image": null, "query": "Real numbers \\(x\\) and \\(y\\) satisfy the following equations: \\(x=\\log_{10}(10^{y-1}+1)-1\\) and \\(y=\\log_{10}(10^{x}+1)-1\\). Compute \\(10^{x-y}\\)."}, {"pid": "60", "question": "Find all positive integers $n$ for which there exists a polynomial $P(x) \\in \\mathbb{Z}[x]$ such that for every positive integer $m\\geq 1$, the numbers $P^m(1), \\ldots, P^m(n)$ leave exactly $\\lceil n/2^m\\rceil$ distinct remainders when divided by $n$. (Here, $P^m$ means $P$ applied $m$ times.)\n\n[i]", "answer": "\\text{ prime } n \\text{ and }n=2^k", "image": null, "query": "Find all positive integers $n$ for which there exists a polynomial $P(x) \\in \\mathbb{Z}[x]$ such that for every positive integer $m\\geq 1$, the numbers $P^m(1), \\ldots, P^m(n)$ leave exactly $\\lceil n/2^m\\rceil$ distinct remainders when divided by $n$. (Here, $P^m$ means $P$ applied $m$ times.)\n\n[i]"}, {"pid": "61", "question": "If \\( x=2 \\) and \\( v=3x \\), what is the value of \\((2v-5)-(2x-5)\\)?", "answer": "8", "image": null, "query": "If \\( x=2 \\) and \\( v=3x \\), what is the value of \\((2v-5)-(2x-5)\\)?"}, {"pid": "62", "question": "In the base 10 arithmetic problem $H M M T+G U T S=R O U N D$, each distinct letter represents a different digit, and leading zeroes are not allowed. What is the maximum possible value of $R O U N D$?", "answer": "16352", "image": null, "query": "In the base 10 arithmetic problem $H M M T+G U T S=R O U N D$, each distinct letter represents a different digit, and leading zeroes are not allowed. What is the maximum possible value of $R O U N D$?"}, {"pid": "63", "question": "Solve the following equation for $x , y , z \\in \\mathbb{N}$ :\n\\[\\left (1+ \\frac{x}{y+z} \\right )^2+\\left (1+ \\frac{y}{z+x} \\right )^2+\\left (1+ \\frac{z}{x+y} \\right )^2=\\frac{27}{4}\\]", "answer": "x=y=z", "image": null, "query": "Solve the following equation for $x , y , z \\in \\mathbb{N}$ :\n\\[\\left (1+ \\frac{x}{y+z} \\right )^2+\\left (1+ \\frac{y}{z+x} \\right )^2+\\left (1+ \\frac{z}{x+y} \\right )^2=\\frac{27}{4}\\]"}, {"pid": "64", "question": "Compute $\\int_{0}^{\\pi} \\frac{2 \\sin \\theta+3 \\cos \\theta-3}{13 \\cos \\theta-5} \\mathrm{d} \\theta$", "answer": "\\frac{3 \\pi}{13}-\\frac{4}{13} \\log \\frac{3}{2}", "image": null, "query": "Compute $\\int_{0}^{\\pi} \\frac{2 \\sin \\theta+3 \\cos \\theta-3}{13 \\cos \\theta-5} \\mathrm{d} \\theta$"}, {"pid": "65", "question": "For positive integers $L$, let $S_{L}=\\sum_{n=1}^{L}\\lfloor n / 2\\rfloor$. Determine all $L$ for which $S_{L}$ is a square number.", "answer": "L=1 \\text{ or } L \\text{ is even}", "image": null, "query": "For positive integers $L$, let $S_{L}=\\sum_{n=1}^{L}\\lfloor n / 2\\rfloor$. Determine all $L$ for which $S_{L}$ is a square number."}, {"pid": "66", "question": "Be<PERSON> is driving from Waterloo, ON to Marathon, ON. She has driven 312 km and has 858 km still to drive. How much farther must she drive in order to be halfway from Waterloo to Marathon?", "answer": "273 \\mathrm{~km}", "image": null, "query": "Be<PERSON> is driving from Waterloo, ON to Marathon, ON. She has driven 312 km and has 858 km still to drive. How much farther must she drive in order to be halfway from Waterloo to Marathon?"}, {"pid": "67", "question": "<PERSON> draws three cards from a standard 52-card deck with replacement. Ace through 10 are worth 1 to 10 points respectively, and the face cards <PERSON>, Queen, and <PERSON> are each worth 10 points. The probability that the sum of the point values of the cards drawn is a multiple of 10 can be written as $\\frac{m}{n}$, where $m, n$ are positive integers and $\\operatorname{gcd}(m, n)=1$. Find $100 m+n$.", "answer": "26597", "image": null, "query": "<PERSON> draws three cards from a standard 52-card deck with replacement. Ace through 10 are worth 1 to 10 points respectively, and the face cards <PERSON>, Queen, and <PERSON> are each worth 10 points. The probability that the sum of the point values of the cards drawn is a multiple of 10 can be written as $\\frac{m}{n}$, where $m, n$ are positive integers and $\\operatorname{gcd}(m, n)=1$. Find $100 m+n$."}, {"pid": "68", "question": "To celebrate 2019, <PERSON><PERSON> gets four sandwiches shaped in the digits 2, 0, 1, and 9 at lunch. However, the four digits get reordered (but not flipped or rotated) on his plate and he notices that they form a 4-digit multiple of 7. What is the greatest possible number that could have been formed?", "answer": "1092", "image": null, "query": "To celebrate 2019, <PERSON><PERSON> gets four sandwiches shaped in the digits 2, 0, 1, and 9 at lunch. However, the four digits get reordered (but not flipped or rotated) on his plate and he notices that they form a 4-digit multiple of 7. What is the greatest possible number that could have been formed?"}, {"pid": "69", "question": "Find $(x+1)\\left(x^{2}+1\\right)\\left(x^{4}+1\\right)\\left(x^{8}+1\\right) \\cdots$, where $|x|<1$.", "answer": "\\frac{1}{1-x}", "image": null, "query": "Find $(x+1)\\left(x^{2}+1\\right)\\left(x^{4}+1\\right)\\left(x^{8}+1\\right) \\cdots$, where $|x|<1$."}, {"pid": "70", "question": "For how many positive integers $n \\leq 100$ is it true that $10 n$ has exactly three times as many positive divisors as $n$ has?", "answer": "28", "image": null, "query": "For how many positive integers $n \\leq 100$ is it true that $10 n$ has exactly three times as many positive divisors as $n$ has?"}, {"pid": "71", "question": "The writer <PERSON> has $n \\ge1$ co-authors who write books with him. Each book has a list of authors including <PERSON> himself. No two books have the same set of authors. At a party with all his co-author, each co-author writes on a note how many books they remember having written with <PERSON>. Inspecting the numbers on the notes, they discover that the numbers written down are the first $n$ Fibonacci numbers (defined by $F_1 = F_2 = 1$ and $F_{k+2}= F_{k+1} + F_k$). For which $n$ is it possible that none of the co-authors had a lapse of memory?", "answer": "$n\\le6$", "image": null, "query": "The writer <PERSON> has $n \\ge1$ co-authors who write books with him. Each book has a list of authors including <PERSON> himself. No two books have the same set of authors. At a party with all his co-author, each co-author writes on a note how many books they remember having written with <PERSON>. Inspecting the numbers on the notes, they discover that the numbers written down are the first $n$ Fibonacci numbers (defined by $F_1 = F_2 = 1$ and $F_{k+2}= F_{k+1} + F_k$). For which $n$ is it possible that none of the co-authors had a lapse of memory?"}, {"pid": "72", "question": "Let $n\\ge 3$ be a fixed integer. There are $m\\ge n+1$ beads on a circular necklace. You wish to paint the beads using $n$ colors, such that among any $n+1$ consecutive beads every color appears at least once. Find the largest value of $m$ for which this task is $\\emph{not}$ possible.\n\n[i]<PERSON>, USA[/i]", "answer": "$\\boxed{n^2-n-1}$", "image": null, "query": "Let $n\\ge 3$ be a fixed integer. There are $m\\ge n+1$ beads on a circular necklace. You wish to paint the beads using $n$ colors, such that among any $n+1$ consecutive beads every color appears at least once. Find the largest value of $m$ for which this task is $\\emph{not}$ possible.\n\n[i]<PERSON>, USA[/i]"}, {"pid": "73", "question": "Trapezoid $A B C D$, with bases $A B$ and $C D$, has side lengths $A B=28, B C=13, C D=14$, and $D A=15$. Let diagonals $A C$ and $B D$ intersect at $P$, and let $E$ and $F$ be the midpoints of $A P$ and $B P$, respectively. Find the area of quadrilateral $C D E F$.", "answer": "112", "image": null, "query": "Trapezoid $A B C D$, with bases $A B$ and $C D$, has side lengths $A B=28, B C=13, C D=14$, and $D A=15$. Let diagonals $A C$ and $B D$ intersect at $P$, and let $E$ and $F$ be the midpoints of $A P$ and $B P$, respectively. Find the area of quadrilateral $C D E F$."}, {"pid": "74", "question": "If $\\cos 60^{\\circ} = \\cos 45^{\\circ} \\cos \\theta$ with $0^{\\circ} \\leq \\theta \\leq 90^{\\circ}$, what is the value of $\\theta$?", "answer": "45^{\\circ}", "image": null, "query": "If $\\cos 60^{\\circ} = \\cos 45^{\\circ} \\cos \\theta$ with $0^{\\circ} \\leq \\theta \\leq 90^{\\circ}$, what is the value of $\\theta$?"}, {"pid": "75", "question": "For a nonnegative integer $n$ and a strictly increasing sequence of real numbers $t_0,t_1,\\dots,t_n$, let $f(t)$ be the corresponding real-valued function defined for $t \\geq t_0$ by the following properties: \\begin{enumerate} \\item[(a)] $f(t)$ is continuous for $t \\geq t_0$, and is twice differentiable for all $t>t_0$ other than $t_1,\\dots,t_n$; \\item[(b)] $f(t_0) = 1/2$; \\item[(c)] $\\lim_{t \\to t_k^+} f'(t) = 0$ for $0 \\leq k \\leq n$; \\item[(d)] For $0 \\leq k \\leq n-1$, we have $f''(t) = k+1$ when $t_k < t< t_{k+1}$, and $f''(t) = n+1$ when $t>t_n$. \\end{enumerate} Considering all choices of $n$ and $t_0,t_1,\\dots,t_n$ such that $t_k \\geq t_{k-1}+1$ for $1 \\leq k \\leq n$, what is the least possible value of $T$ for which $f(t_0+T) = 2023$?", "answer": "29", "image": null, "query": "For a nonnegative integer $n$ and a strictly increasing sequence of real numbers $t_0,t_1,\\dots,t_n$, let $f(t)$ be the corresponding real-valued function defined for $t \\geq t_0$ by the following properties: \\begin{enumerate} \\item[(a)] $f(t)$ is continuous for $t \\geq t_0$, and is twice differentiable for all $t>t_0$ other than $t_1,\\dots,t_n$; \\item[(b)] $f(t_0) = 1/2$; \\item[(c)] $\\lim_{t \\to t_k^+} f'(t) = 0$ for $0 \\leq k \\leq n$; \\item[(d)] For $0 \\leq k \\leq n-1$, we have $f''(t) = k+1$ when $t_k < t< t_{k+1}$, and $f''(t) = n+1$ when $t>t_n$. \\end{enumerate} Considering all choices of $n$ and $t_0,t_1,\\dots,t_n$ such that $t_k \\geq t_{k-1}+1$ for $1 \\leq k \\leq n$, what is the least possible value of $T$ for which $f(t_0+T) = 2023$?"}, {"pid": "76", "question": "If <PERSON> does not sing on Saturday, then she has a $70 \\%$ chance of singing on Sunday; however, to rest her voice, she never sings on both days. If <PERSON> has a $50 \\%$ chance of singing on Sunday, find the probability that she sings on Saturday.", "answer": "\\frac{2}{7}", "image": null, "query": "If <PERSON> does not sing on Saturday, then she has a $70 \\%$ chance of singing on Sunday; however, to rest her voice, she never sings on both days. If <PERSON> has a $50 \\%$ chance of singing on Sunday, find the probability that she sings on Saturday."}, {"pid": "77", "question": "Let $f(x)$ be a degree 2006 polynomial with complex roots $c_{1}, c_{2}, \\ldots, c_{2006}$, such that the set $$\\left\\{\\left|c_{1}\\right|,\\left|c_{2}\\right|, \\ldots,\\left|c_{2006}\\right|\\right\\}$$ consists of exactly 1006 distinct values. What is the minimum number of real roots of $f(x)$ ?", "answer": "The minimum number of real roots of \\( f(x) \\) is \\( 6 \\).", "image": null, "query": "Let $f(x)$ be a degree 2006 polynomial with complex roots $c_{1}, c_{2}, \\ldots, c_{2006}$, such that the set $$\\left\\{\\left|c_{1}\\right|,\\left|c_{2}\\right|, \\ldots,\\left|c_{2006}\\right|\\right\\}$$ consists of exactly 1006 distinct values. What is the minimum number of real roots of $f(x)$ ?"}, {"pid": "78", "question": "Let $f(n)$ and $g(n)$ be polynomials of degree 2014 such that $f(n)+(-1)^{n} g(n)=2^{n}$ for $n=1,2, \\ldots, 4030$. Find the coefficient of $x^{2014}$ in $g(x)$.", "answer": "\\frac{3^{2014}}{2^{2014} \\cdot 2014!}", "image": null, "query": "Let $f(n)$ and $g(n)$ be polynomials of degree 2014 such that $f(n)+(-1)^{n} g(n)=2^{n}$ for $n=1,2, \\ldots, 4030$. Find the coefficient of $x^{2014}$ in $g(x)$."}, {"pid": "79", "question": "Determine the greatest positive integer $ n$ such that in three-dimensional space, there exist n points $ P_{1},P_{2},\\cdots,P_{n},$ among $ n$ points no three points are collinear, and for arbitary $ 1\\leq i < j < k\\leq n$, $ P_{i}P_{j}P_{k}$ isn't obtuse triangle.", "answer": "8", "image": null, "query": "Determine the greatest positive integer $ n$ such that in three-dimensional space, there exist n points $ P_{1},P_{2},\\cdots,P_{n},$ among $ n$ points no three points are collinear, and for arbitary $ 1\\leq i < j < k\\leq n$, $ P_{i}P_{j}P_{k}$ isn't obtuse triangle."}, {"pid": "80", "question": "$r$ and $s$ are integers such that $3 r \\geq 2 s-3 \\text { and } 4 s \\geq r+12$. What is the smallest possible value of $r / s$ ?", "answer": "1/2", "image": null, "query": "$r$ and $s$ are integers such that $3 r \\geq 2 s-3 \\text { and } 4 s \\geq r+12$. What is the smallest possible value of $r / s$ ?"}, {"pid": "81", "question": "The digits $1,2,3,4,5,6$ are randomly chosen (without replacement) to form the three-digit numbers $M=\\overline{A B C}$ and $N=\\overline{D E F}$. For example, we could have $M=413$ and $N=256$. Find the expected value of $M \\cdot N$.", "answer": "143745", "image": null, "query": "The digits $1,2,3,4,5,6$ are randomly chosen (without replacement) to form the three-digit numbers $M=\\overline{A B C}$ and $N=\\overline{D E F}$. For example, we could have $M=413$ and $N=256$. Find the expected value of $M \\cdot N$."}, {"pid": "82", "question": "Find all triples $(a, b, c)$ of positive integers such that $a^3 + b^3 + c^3 = (abc)^2$.", "answer": "(3, 2, 1), (3, 1, 2), (2, 3, 1), (2, 1, 3), (1, 3, 2), (1, 2, 3)", "image": null, "query": "Find all triples $(a, b, c)$ of positive integers such that $a^3 + b^3 + c^3 = (abc)^2$."}, {"pid": "83", "question": "Suppose that a polynomial of the form $p(x)=x^{2010} \\pm x^{2009} \\pm \\cdots \\pm x \\pm 1$ has no real roots. What is the maximum possible number of coefficients of -1 in $p$?", "answer": "1005", "image": null, "query": "Suppose that a polynomial of the form $p(x)=x^{2010} \\pm x^{2009} \\pm \\cdots \\pm x \\pm 1$ has no real roots. What is the maximum possible number of coefficients of -1 in $p$?"}, {"pid": "84", "question": "Suppose that $x$ and $y$ are complex numbers such that $x+y=1$ and that $x^{20}+y^{20}=20$. Find the sum of all possible values of $x^{2}+y^{2}$.", "answer": "-90", "image": null, "query": "Suppose that $x$ and $y$ are complex numbers such that $x+y=1$ and that $x^{20}+y^{20}=20$. Find the sum of all possible values of $x^{2}+y^{2}$."}, {"pid": "85", "question": "Find all real numbers $x$ satisfying $$x^{9}+\\frac{9}{8} x^{6}+\\frac{27}{64} x^{3}-x+\\frac{219}{512}=0$$", "answer": "$\\frac{1}{2}, \\frac{-1 \\pm \\sqrt{13}}{4}$", "image": null, "query": "Find all real numbers $x$ satisfying $$x^{9}+\\frac{9}{8} x^{6}+\\frac{27}{64} x^{3}-x+\\frac{219}{512}=0$$"}, {"pid": "86", "question": "What is the smallest positive integer $n$ which cannot be written in any of the following forms? - $n=1+2+\\cdots+k$ for a positive integer $k$. - $n=p^{k}$ for a prime number $p$ and integer $k$ - $n=p+1$ for a prime number $p$. - $n=p q$ for some distinct prime numbers $p$ and $q$", "answer": "40", "image": null, "query": "What is the smallest positive integer $n$ which cannot be written in any of the following forms? - $n=1+2+\\cdots+k$ for a positive integer $k$. - $n=p^{k}$ for a prime number $p$ and integer $k$ - $n=p+1$ for a prime number $p$. - $n=p q$ for some distinct prime numbers $p$ and $q$"}, {"pid": "87", "question": "Let $p>2$ be a prime number. $\\mathbb{F}_{p}[x]$ is defined as the set of all polynomials in $x$ with coefficients in $\\mathbb{F}_{p}$ (the integers modulo $p$ with usual addition and subtraction), so that two polynomials are equal if and only if the coefficients of $x^{k}$ are equal in $\\mathbb{F}_{p}$ for each nonnegative integer $k$. For example, $(x+2)(2 x+3)=2 x^{2}+2 x+1$ in $\\mathbb{F}_{5}[x]$ because the corresponding coefficients are equal modulo 5 . Let $f, g \\in \\mathbb{F}_{p}[x]$. The pair $(f, g)$ is called compositional if $$f(g(x)) \\equiv x^{p^{2}}-x$$ in $\\mathbb{F}_{p}[x]$. Find, with proof, the number of compositional pairs (in terms of $p$ ).", "answer": "4 p(p-1)", "image": null, "query": "Let $p>2$ be a prime number. $\\mathbb{F}_{p}[x]$ is defined as the set of all polynomials in $x$ with coefficients in $\\mathbb{F}_{p}$ (the integers modulo $p$ with usual addition and subtraction), so that two polynomials are equal if and only if the coefficients of $x^{k}$ are equal in $\\mathbb{F}_{p}$ for each nonnegative integer $k$. For example, $(x+2)(2 x+3)=2 x^{2}+2 x+1$ in $\\mathbb{F}_{5}[x]$ because the corresponding coefficients are equal modulo 5 . Let $f, g \\in \\mathbb{F}_{p}[x]$. The pair $(f, g)$ is called compositional if $$f(g(x)) \\equiv x^{p^{2}}-x$$ in $\\mathbb{F}_{p}[x]$. Find, with proof, the number of compositional pairs (in terms of $p$ )."}, {"pid": "88", "question": "Let $\\omega$ be a fixed circle with radius 1, and let $B C$ be a fixed chord of $\\omega$ such that $B C=1$. The locus of the incenter of $A B C$ as $A$ varies along the circumference of $\\omega$ bounds a region $\\mathcal{R}$ in the plane. Find the area of $\\mathcal{R}$.", "answer": "\\pi\\left(\\frac{3-\\sqrt{3}}{3}\\right)-1", "image": null, "query": "Let $\\omega$ be a fixed circle with radius 1, and let $B C$ be a fixed chord of $\\omega$ such that $B C=1$. The locus of the incenter of $A B C$ as $A$ varies along the circumference of $\\omega$ bounds a region $\\mathcal{R}$ in the plane. Find the area of $\\mathcal{R}$."}, {"pid": "89", "question": "Let $A B C D$ be a quadrilateral, and let $E, F, G, H$ be the respective midpoints of $A B, B C, C D, D A$. If $E G=12$ and $F H=15$, what is the maximum possible area of $A B C D$?", "answer": "180", "image": null, "query": "Let $A B C D$ be a quadrilateral, and let $E, F, G, H$ be the respective midpoints of $A B, B C, C D, D A$. If $E G=12$ and $F H=15$, what is the maximum possible area of $A B C D$?"}, {"pid": "90", "question": "I don't like this solution, but I couldn't find a better one this late at night (or this early in the morning; it's 4:15 AM here :)).\n\nLet $S=KA\\cap \\Omega$, and let $T$ be the antipode of $K$ on $\\Omega$. Let $X,Y$ be the touch points between $\\Omega$ and $CA,AB$ respectively. \n\nThe line $AD$ is parallel to $KT$ and is cut into two equal parts by $KS,KN,KD$, so $(KT,KN;KS,KD)=-1$. This means that the quadrilateral $KTSN$ is harmonic, so the tangents to $\\Omega$ through $K,S$ meet on $NT$. On the other hand, the tangents to $\\Omega$ through the points $X,Y$ meet on $KS$, so $KXSY$ is also harmonic, meaning that the tangents to $\\Omega$ through $K,S$ meet on $XY$.\n\nFrom these it follows that $BC,XY,TN$ are concurrent. If $P=XY\\cap BC$, it's well-known that $(B,C;K,P)=-1$, and since $\\angle KNP=\\angle KNT=\\frac{\\pi}2$, it means that $N$ lies on an Apollonius circle, so $NK$ is the bisector of $\\angle BNC$.\n\nFrom here the conclusion follows, because if $B'=NB\\cap \\Omega,\\ C'=NC\\cap \\Omega$, we get $B'C'\\|BC$, so there's a homothety of center $N$ which maps $\\Omega$ to the circumcircle of $BNC$.", "answer": "", "image": null, "query": "I don't like this solution, but I couldn't find a better one this late at night (or this early in the morning; it's 4:15 AM here :)).\n\nLet $S=KA\\cap \\Omega$, and let $T$ be the antipode of $K$ on $\\Omega$. Let $X,Y$ be the touch points between $\\Omega$ and $CA,AB$ respectively. \n\nThe line $AD$ is parallel to $KT$ and is cut into two equal parts by $KS,KN,KD$, so $(KT,KN;KS,KD)=-1$. This means that the quadrilateral $KTSN$ is harmonic, so the tangents to $\\Omega$ through $K,S$ meet on $NT$. On the other hand, the tangents to $\\Omega$ through the points $X,Y$ meet on $KS$, so $KXSY$ is also harmonic, meaning that the tangents to $\\Omega$ through $K,S$ meet on $XY$.\n\nFrom these it follows that $BC,XY,TN$ are concurrent. If $P=XY\\cap BC$, it's well-known that $(B,C;K,P)=-1$, and since $\\angle KNP=\\angle KNT=\\frac{\\pi}2$, it means that $N$ lies on an Apollonius circle, so $NK$ is the bisector of $\\angle BNC$.\n\nFrom here the conclusion follows, because if $B'=NB\\cap \\Omega,\\ C'=NC\\cap \\Omega$, we get $B'C'\\|BC$, so there's a homothety of center $N$ which maps $\\Omega$ to the circumcircle of $BNC$."}, {"pid": "91", "question": "Among citizens of Cambridge there exist 8 different types of blood antigens. In a crowded lecture hall are 256 students, each of whom has a blood type corresponding to a distinct subset of the antigens; the remaining of the antigens are foreign to them. <PERSON><PERSON><PERSON> the Mosquito flies around the lecture hall, picks a subset of the students uniformly at random, and bites the chosen students in a random order. After biting a student, <PERSON><PERSON><PERSON> stores a bit of any antigens that student had. A student bitten while <PERSON><PERSON><PERSON> had $k$ blood antigen foreign to him/her will suffer for $k$ hours. What is the expected total suffering of all 256 students, in hours?", "answer": "\\frac{2^{135}-2^{128}+1}{2^{119} \\cdot 129}", "image": null, "query": "Among citizens of Cambridge there exist 8 different types of blood antigens. In a crowded lecture hall are 256 students, each of whom has a blood type corresponding to a distinct subset of the antigens; the remaining of the antigens are foreign to them. <PERSON><PERSON><PERSON> the Mosquito flies around the lecture hall, picks a subset of the students uniformly at random, and bites the chosen students in a random order. After biting a student, <PERSON><PERSON><PERSON> stores a bit of any antigens that student had. A student bitten while <PERSON><PERSON><PERSON> had $k$ blood antigen foreign to him/her will suffer for $k$ hours. What is the expected total suffering of all 256 students, in hours?"}, {"pid": "92", "question": "The country of HMMTLand has 8 cities. Its government decides to construct several two-way roads between pairs of distinct cities. After they finish construction, it turns out that each city can reach exactly 3 other cities via a single road, and from any pair of distinct cities, either exactly 0 or 2 other cities can be reached from both cities by a single road. Compute the number of ways HMMTLand could have constructed the roads.", "answer": "875", "image": null, "query": "The country of HMMTLand has 8 cities. Its government decides to construct several two-way roads between pairs of distinct cities. After they finish construction, it turns out that each city can reach exactly 3 other cities via a single road, and from any pair of distinct cities, either exactly 0 or 2 other cities can be reached from both cities by a single road. Compute the number of ways HMMTLand could have constructed the roads."}, {"pid": "93", "question": "Let Q be the product of the sizes of all the non-empty subsets of \\{1,2, \\ldots, 2012\\}$, and let $M=$ \\log _{2}\\left(\\log _{2}(Q)\\right)$. Give lower and upper bounds $L$ and $U$ for $M$. If $0<L \\leq M \\leq U$, then your score will be \\min \\left(23,\\left\\lfloor\\frac{23}{3(U-L)}\\right\\rfloor\\right)$. Otherwise, your score will be 0 .", "answer": "2015.318180 \\ldots", "image": null, "query": "Let Q be the product of the sizes of all the non-empty subsets of \\{1,2, \\ldots, 2012\\}$, and let $M=$ \\log _{2}\\left(\\log _{2}(Q)\\right)$. Give lower and upper bounds $L$ and $U$ for $M$. If $0<L \\leq M \\leq U$, then your score will be \\min \\left(23,\\left\\lfloor\\frac{23}{3(U-L)}\\right\\rfloor\\right)$. Otherwise, your score will be 0 ."}, {"pid": "94", "question": "$x, y$ are positive real numbers such that $x+y^{2}=x y$. What is the smallest possible value of $x$?", "answer": "4", "image": null, "query": "$x, y$ are positive real numbers such that $x+y^{2}=x y$. What is the smallest possible value of $x$?"}, {"pid": "95", "question": "Find all the positive perfect cubes that are not divisible by $10$ such that the number obtained by erasing the last three digits is also a perfect cube.", "answer": "1331 \\text{ and } 1728", "image": null, "query": "Find all the positive perfect cubes that are not divisible by $10$ such that the number obtained by erasing the last three digits is also a perfect cube."}, {"pid": "96", "question": "Find the probability that both students in any given pair did not get lost if the teacher leaves when the students from each pair are either both present or both not present.", "answer": "\\frac{81^{10}}{82^{10}", "image": null, "query": "Find the probability that both students in any given pair did not get lost if the teacher leaves when the students from each pair are either both present or both not present."}, {"pid": "97", "question": "Let $G$ be a simple graph with 100 vertices such that for each vertice $u$, there exists a vertice $v \\in N \\left ( u \\right )$ and $ N \\left ( u \\right ) \\cap  N \\left ( v \\right ) = \\o $. Try to find the maximal possible number of edges in $G$. The $ N \\left ( . \\right )$  refers to the neighborhood.", "answer": "3822", "image": null, "query": "Let $G$ be a simple graph with 100 vertices such that for each vertice $u$, there exists a vertice $v \\in N \\left ( u \\right )$ and $ N \\left ( u \\right ) \\cap  N \\left ( v \\right ) = \\o $. Try to find the maximal possible number of edges in $G$. The $ N \\left ( . \\right )$  refers to the neighborhood."}, {"pid": "98", "question": "Solve the system of equations $p+3q+r=3$, $p+2q+3r=3$, $p+q+r=2$ for the ordered triple $(p, q, r)$.", "answer": "\\left(\\frac{5}{4}, \\frac{1}{2}, \\frac{1}{4}\\right)", "image": null, "query": "Solve the system of equations $p+3q+r=3$, $p+2q+3r=3$, $p+q+r=2$ for the ordered triple $(p, q, r)$."}, {"pid": "99", "question": "What is the sum of the positive divisors of 1184?", "answer": "2394", "image": null, "query": "What is the sum of the positive divisors of 1184?"}, {"pid": "100", "question": "Find all integers $n$ and $m$, $n > m > 2$, and such that a regular $n$-sided polygon can be inscribed in a regular $m$-sided polygon so that all the vertices of the $n$-gon lie on the sides of the $m$-gon.", "answer": "(m, n) = (m, 2m), (3, 4)", "image": null, "query": "Find all integers $n$ and $m$, $n > m > 2$, and such that a regular $n$-sided polygon can be inscribed in a regular $m$-sided polygon so that all the vertices of the $n$-gon lie on the sides of the $m$-gon."}, {"pid": "101", "question": "Let $n$ be the answer to this problem. We define the digit sum of a date as the sum of its 4 digits when expressed in mmdd format (e.g. the digit sum of 13 May is $0+5+1+3=9$). Find the number of dates in the year 2021 with digit sum equal to the positive integer $n$.", "answer": "15", "image": null, "query": "Let $n$ be the answer to this problem. We define the digit sum of a date as the sum of its 4 digits when expressed in mmdd format (e.g. the digit sum of 13 May is $0+5+1+3=9$). Find the number of dates in the year 2021 with digit sum equal to the positive integer $n$."}, {"pid": "102", "question": "At the Lacsap Hospital, <PERSON> is a doctor and <PERSON> is a nurse. Not including <PERSON>, there are five doctors and three nurses at the hospital. Not including <PERSON>, there are $d$ doctors and $n$ nurses at the hospital. What is the product of $d$ and $n$?", "answer": "12", "image": null, "query": "At the Lacsap Hospital, <PERSON> is a doctor and <PERSON> is a nurse. Not including <PERSON>, there are five doctors and three nurses at the hospital. Not including <PERSON>, there are $d$ doctors and $n$ nurses at the hospital. What is the product of $d$ and $n$?"}, {"pid": "103", "question": "Pick a random integer between 0 and 4095, inclusive. Write it in base 2 (without any leading zeroes). What is the expected number of consecutive digits that are not the same (that is, the expected number of occurrences of either 01 or 10 in the base 2 representation)?", "answer": "\\frac{20481}{4096}", "image": null, "query": "Pick a random integer between 0 and 4095, inclusive. Write it in base 2 (without any leading zeroes). What is the expected number of consecutive digits that are not the same (that is, the expected number of occurrences of either 01 or 10 in the base 2 representation)?"}, {"pid": "104", "question": "The incenter of the triangle $ ABC$ is $ K.$ The midpoint of $ AB$ is $ C_1$ and that of $ AC$ is $ B_1.$ The lines $ C_1K$ and $ AC$ meet at $ B_2,$ the lines $ B_1K$ and $ AB$ at $ C_2.$ If the areas of the triangles $ AB_2C_2$ and $ ABC$ are equal, what is the measure of angle $ \\angle CAB?$", "answer": "$\\angle CAB=60^{\\circ}$", "image": null, "query": "The incenter of the triangle $ ABC$ is $ K.$ The midpoint of $ AB$ is $ C_1$ and that of $ AC$ is $ B_1.$ The lines $ C_1K$ and $ AC$ meet at $ B_2,$ the lines $ B_1K$ and $ AB$ at $ C_2.$ If the areas of the triangles $ AB_2C_2$ and $ ABC$ are equal, what is the measure of angle $ \\angle CAB?$"}, {"pid": "105", "question": "For any two different real numbers $x$ and $y$, we define $D(x,y)$ to be the unique integer $d$ satisfying $2^d\\le |x-y| < 2^{d+1}$. Given a set of reals $\\mathcal F$, and an element $x\\in \\mathcal F$, we say that the [i]scales[/i] of $x$ in $\\mathcal F$ are the values of $D(x,y)$ for $y\\in\\mathcal F$ with $x\\neq y$. Let $k$ be a given positive integer. \n \nSuppose that each member $x$ of $\\mathcal F$ has at most $k$ different scales in $\\mathcal F$ (note that these scales may depend on $x$). What is the maximum possible size of $\\mathcal F$?", "answer": "2^k", "image": null, "query": "For any two different real numbers $x$ and $y$, we define $D(x,y)$ to be the unique integer $d$ satisfying $2^d\\le |x-y| < 2^{d+1}$. Given a set of reals $\\mathcal F$, and an element $x\\in \\mathcal F$, we say that the [i]scales[/i] of $x$ in $\\mathcal F$ are the values of $D(x,y)$ for $y\\in\\mathcal F$ with $x\\neq y$. Let $k$ be a given positive integer. \n \nSuppose that each member $x$ of $\\mathcal F$ has at most $k$ different scales in $\\mathcal F$ (note that these scales may depend on $x$). What is the maximum possible size of $\\mathcal F$?"}, {"pid": "106", "question": "$A B C D$ is a cyclic quadrilateral with sides $A B=10, B C=8, C D=25$, and $D A=12$. A circle $\\omega$ is tangent to segments $D A, A B$, and $B C$. Find the radius of $\\omega$.", "answer": "\\sqrt{\\frac{1209}{7}} \\text{ OR } \\frac{\\sqrt{8463}}{7}", "image": null, "query": "$A B C D$ is a cyclic quadrilateral with sides $A B=10, B C=8, C D=25$, and $D A=12$. A circle $\\omega$ is tangent to segments $D A, A B$, and $B C$. Find the radius of $\\omega$."}, {"pid": "107", "question": "For a sequence $a_1<a_2<\\cdots<a_n$ of integers, a pair $(a_i,a_j)$ with $1\\leq i<j\\leq n$ is called [i]interesting[/i] if there exists a pair $(a_k,a_l)$ of integers with $1\\leq k<l\\leq n$ such that $$\\frac{a_l-a_k}{a_j-a_i}=2.$$ For each $n\\geq 3$, find the largest possible number of interesting pairs in a sequence of length $n$.", "answer": "\\binom{n}{2} - (n - 2)", "image": null, "query": "For a sequence $a_1<a_2<\\cdots<a_n$ of integers, a pair $(a_i,a_j)$ with $1\\leq i<j\\leq n$ is called [i]interesting[/i] if there exists a pair $(a_k,a_l)$ of integers with $1\\leq k<l\\leq n$ such that $$\\frac{a_l-a_k}{a_j-a_i}=2.$$ For each $n\\geq 3$, find the largest possible number of interesting pairs in a sequence of length $n$."}, {"pid": "108", "question": "How many times does the letter \"e\" occur in all problem statements in this year's HMMT February competition?", "answer": "1661", "image": null, "query": "How many times does the letter \"e\" occur in all problem statements in this year's HMMT February competition?"}, {"pid": "109", "question": "Three distinct vertices are chosen at random from the vertices of a given regular polygon of $(2n+1)$ sides. If all such choices are equally likely, what is the probability that the center of the given polygon lies in the interior of the triangle determined by the three chosen random points?", "answer": "\\[\n\\boxed{\\frac{n+1}{4n-2}}\n\\]", "image": null, "query": "Three distinct vertices are chosen at random from the vertices of a given regular polygon of $(2n+1)$ sides. If all such choices are equally likely, what is the probability that the center of the given polygon lies in the interior of the triangle determined by the three chosen random points?"}, {"pid": "110", "question": "How many functions $f:\\{1,2,3,4,5\\} \\rightarrow\\{1,2,3,4,5\\}$ satisfy $f(f(x))=f(x)$ for all $x \\in\\{1,2,3,4,5\\}$?", "answer": "196", "image": null, "query": "How many functions $f:\\{1,2,3,4,5\\} \\rightarrow\\{1,2,3,4,5\\}$ satisfy $f(f(x))=f(x)$ for all $x \\in\\{1,2,3,4,5\\}$?"}, {"pid": "111", "question": "<PERSON> is eating 2401 grains of rice for lunch. She eats the rice in a very peculiar manner: every step, if she has only one grain of rice remaining, she eats it. Otherwise, she finds the smallest positive integer $d>1$ for which she can group the rice into equal groups of size $d$ with none left over. She then groups the rice into groups of size $d$, eats one grain from each group, and puts the rice back into a single pile. How many steps does it take her to finish all her rice?", "answer": "17", "image": null, "query": "<PERSON> is eating 2401 grains of rice for lunch. She eats the rice in a very peculiar manner: every step, if she has only one grain of rice remaining, she eats it. Otherwise, she finds the smallest positive integer $d>1$ for which she can group the rice into equal groups of size $d$ with none left over. She then groups the rice into groups of size $d$, eats one grain from each group, and puts the rice back into a single pile. How many steps does it take her to finish all her rice?"}, {"pid": "112", "question": "If $\\left(a+\\frac{1}{a}\\right)^{2}=3$, find $\\left(a+\\frac{1}{a}\\right)^{3}$ in terms of $a$.", "answer": "0", "image": null, "query": "If $\\left(a+\\frac{1}{a}\\right)^{2}=3$, find $\\left(a+\\frac{1}{a}\\right)^{3}$ in terms of $a$."}, {"pid": "113", "question": "<PERSON> picks his favorite point $(x, y)$ in the first quadrant on the unit circle $x^{2}+y^{2}=1$, such that a ray from the origin through $(x, y)$ is $\\theta$ radians counterclockwise from the positive $x$-axis. He then computes $\\cos ^{-1}\\left(\\frac{4 x+3 y}{5}\\right)$ and is surprised to get $\\theta$. What is $\\tan (\\theta)$?", "answer": "\\frac{1}{3}", "image": null, "query": "<PERSON> picks his favorite point $(x, y)$ in the first quadrant on the unit circle $x^{2}+y^{2}=1$, such that a ray from the origin through $(x, y)$ is $\\theta$ radians counterclockwise from the positive $x$-axis. He then computes $\\cos ^{-1}\\left(\\frac{4 x+3 y}{5}\\right)$ and is surprised to get $\\theta$. What is $\\tan (\\theta)$?"}, {"pid": "114", "question": "<PERSON><PERSON> and <PERSON> alternate turns tossing a fair coin. <PERSON><PERSON> goes first and each player takes three turns. The first player to toss a tail wins. If neither <PERSON><PERSON> nor <PERSON> tosses a tail, then neither wins. What is the probability that <PERSON><PERSON> wins?", "answer": "\\frac{21}{32}", "image": null, "query": "<PERSON><PERSON> and <PERSON> alternate turns tossing a fair coin. <PERSON><PERSON> goes first and each player takes three turns. The first player to toss a tail wins. If neither <PERSON><PERSON> nor <PERSON> tosses a tail, then neither wins. What is the probability that <PERSON><PERSON> wins?"}, {"pid": "115", "question": "Is there a strictly increasing function $f: \\mathbb{R} \\to \\mathbb{R}$ such that $f'(x) = f(f(x))$ for all $x$?", "answer": "No", "image": null, "query": "Is there a strictly increasing function $f: \\mathbb{R} \\to \\mathbb{R}$ such that $f'(x) = f(f(x))$ for all $x$?"}, {"pid": "116", "question": "The sequence $\\{a_n\\}_{n\\geq 0}$ of real numbers satisfies the relation:\n\\[ a_{m+n} + a_{m-n} - m + n -1 = \\frac12 (a_{2m} + a_{2n})  \\]\nfor all non-negative integers $m$ and $n$, $m \\ge n$. If $a_1 = 3$ find $a_{2004}$.", "answer": "4018021", "image": null, "query": "The sequence $\\{a_n\\}_{n\\geq 0}$ of real numbers satisfies the relation:\n\\[ a_{m+n} + a_{m-n} - m + n -1 = \\frac12 (a_{2m} + a_{2n})  \\]\nfor all non-negative integers $m$ and $n$, $m \\ge n$. If $a_1 = 3$ find $a_{2004}$."}, {"pid": "117", "question": "Find the largest positive integer $k{}$ for which there exists a convex polyhedron $\\mathcal{P}$ with 2022 edges, which satisfies the following properties:\n[list]\n[*]The degrees of the vertices of $\\mathcal{P}$ don’t differ by more than one, and\n[*]It is possible to colour the edges of $\\mathcal{P}$ with $k{}$ colours such that for every colour $c{}$, and every pair of vertices $(v_1, v_2)$ of $\\mathcal{P}$, there is a monochromatic path between $v_1$ and $v_2$ in the colour $c{}$.\n[/list]\n[i]<PERSON>, Macedonia[/i]", "answer": "2", "image": null, "query": "Find the largest positive integer $k{}$ for which there exists a convex polyhedron $\\mathcal{P}$ with 2022 edges, which satisfies the following properties:\n[list]\n[*]The degrees of the vertices of $\\mathcal{P}$ don’t differ by more than one, and\n[*]It is possible to colour the edges of $\\mathcal{P}$ with $k{}$ colours such that for every colour $c{}$, and every pair of vertices $(v_1, v_2)$ of $\\mathcal{P}$, there is a monochromatic path between $v_1$ and $v_2$ in the colour $c{}$.\n[/list]\n[i]<PERSON>, Macedonia[/i]"}, {"pid": "118", "question": "<PERSON><PERSON><PERSON><PERSON> is given a $2013 \\times 2013$ array of integers each between 1 and 2013, inclusive. He is allowed two operations: 1. Choose a row, and subtract 1 from each entry. 2. Chooses a column, and add 1 to each entry. He would like to get an array where all integers are divisible by 2013. On how many arrays is this possible?", "answer": "2013^{4025}", "image": null, "query": "<PERSON><PERSON><PERSON><PERSON> is given a $2013 \\times 2013$ array of integers each between 1 and 2013, inclusive. He is allowed two operations: 1. Choose a row, and subtract 1 from each entry. 2. Chooses a column, and add 1 to each entry. He would like to get an array where all integers are divisible by 2013. On how many arrays is this possible?"}, {"pid": "119", "question": "Find all triples of positive integers $(x,y,z)$ that satisfy the equation\n\\begin{align*} 2(x+y+z+2xyz)^2=(2xy+2yz+2zx+1)^2+2023  \\end{align*}", "answer": "The only solutions are \\((2, 3, 3)\\) and its permutations.", "image": null, "query": "Find all triples of positive integers $(x,y,z)$ that satisfy the equation\n\\begin{align*} 2(x+y+z+2xyz)^2=(2xy+2yz+2zx+1)^2+2023  \\end{align*}"}, {"pid": "120", "question": "Our next object up for bid is an arithmetic progression of primes. For example, the primes 3,5, and 7 form an arithmetic progression of length 3. What is the largest possible length of an arithmetic progression formed of positive primes less than 1,000,000? Be prepared to justify your answer.", "answer": "12", "image": null, "query": "Our next object up for bid is an arithmetic progression of primes. For example, the primes 3,5, and 7 form an arithmetic progression of length 3. What is the largest possible length of an arithmetic progression formed of positive primes less than 1,000,000? Be prepared to justify your answer."}, {"pid": "121", "question": "A cube has edge length 4 m. One end of a rope of length 5 m is anchored to the centre of the top face of the cube. What is the integer formed by the rightmost two digits of the integer closest to 100 times the area of the surface of the cube that can be reached by the other end of the rope?", "answer": "81", "image": null, "query": "A cube has edge length 4 m. One end of a rope of length 5 m is anchored to the centre of the top face of the cube. What is the integer formed by the rightmost two digits of the integer closest to 100 times the area of the surface of the cube that can be reached by the other end of the rope?"}, {"pid": "122", "question": "In quadrilateral $ABCD$, there exists a point $E$ on segment $AD$ such that $\\frac{AE}{ED}=\\frac{1}{9}$ and $\\angle BEC$ is a right angle. Additionally, the area of triangle $CED$ is 27 times more than the area of triangle $AEB$. If $\\angle EBC=\\angle EAB, \\angle ECB=\\angle EDC$, and $BC=6$, compute the value of $AD^{2}$.", "answer": "320", "image": null, "query": "In quadrilateral $ABCD$, there exists a point $E$ on segment $AD$ such that $\\frac{AE}{ED}=\\frac{1}{9}$ and $\\angle BEC$ is a right angle. Additionally, the area of triangle $CED$ is 27 times more than the area of triangle $AEB$. If $\\angle EBC=\\angle EAB, \\angle ECB=\\angle EDC$, and $BC=6$, compute the value of $AD^{2}$."}, {"pid": "123", "question": "Twenty-seven players are randomly split into three teams of nine. Given that <PERSON> is on a different team from <PERSON><PERSON> and <PERSON><PERSON> is on a different team from <PERSON>, what is the probability that <PERSON> and <PERSON> are on the same team?", "answer": "\\frac{8}{17}", "image": null, "query": "Twenty-seven players are randomly split into three teams of nine. Given that <PERSON> is on a different team from <PERSON><PERSON> and <PERSON><PERSON> is on a different team from <PERSON>, what is the probability that <PERSON> and <PERSON> are on the same team?"}, {"pid": "124", "question": "In the Year 0 of Cambridge there is one squirrel and one rabbit. Both animals multiply in numbers quickly. In particular, if there are $m$ squirrels and $n$ rabbits in Year $k$, then there will be $2 m+2019$ squirrels and $4 n-2$ rabbits in Year $k+1$. What is the first year in which there will be strictly more rabbits than squirrels?", "answer": "13", "image": null, "query": "In the Year 0 of Cambridge there is one squirrel and one rabbit. Both animals multiply in numbers quickly. In particular, if there are $m$ squirrels and $n$ rabbits in Year $k$, then there will be $2 m+2019$ squirrels and $4 n-2$ rabbits in Year $k+1$. What is the first year in which there will be strictly more rabbits than squirrels?"}, {"pid": "125", "question": "How many solutions in nonnegative integers $(a, b, c)$ are there to the equation $2^{a}+2^{b}=c!\\quad ?$", "answer": "5", "image": null, "query": "How many solutions in nonnegative integers $(a, b, c)$ are there to the equation $2^{a}+2^{b}=c!\\quad ?$"}, {"pid": "126", "question": "Let $a, b, c, n$ be positive real numbers such that $\\frac{a+b}{a}=3, \\frac{b+c}{b}=4$, and $\\frac{c+a}{c}=n$. Find $n$.", "answer": "\\frac{7}{6}", "image": null, "query": "Let $a, b, c, n$ be positive real numbers such that $\\frac{a+b}{a}=3, \\frac{b+c}{b}=4$, and $\\frac{c+a}{c}=n$. Find $n$."}, {"pid": "127", "question": "For positive integers $a$ and $N$, let $r(a, N) \\in\\{0,1, \\ldots, N-1\\}$ denote the remainder of $a$ when divided by $N$. Determine the number of positive integers $n \\leq 1000000$ for which $r(n, 1000)>r(n, 1001)$.", "answer": "499500", "image": null, "query": "For positive integers $a$ and $N$, let $r(a, N) \\in\\{0,1, \\ldots, N-1\\}$ denote the remainder of $a$ when divided by $N$. Determine the number of positive integers $n \\leq 1000000$ for which $r(n, 1000)>r(n, 1001)$."}, {"pid": "128", "question": "Find all prime numbers $p$ such that $y^{2}=x^{3}+4x$ has exactly $p$ solutions in integers modulo $p$. In other words, determine all prime numbers $p$ with the following property: there exist exactly $p$ ordered pairs of integers $(x, y)$ such that $x, y \\in\\{0,1, \\ldots, p-1\\}$ and $p \\text{ divides } y^{2}-x^{3}-4x$.", "answer": "p=2 \\text{ and } p \\equiv 3(\\bmod 4)", "image": null, "query": "Find all prime numbers $p$ such that $y^{2}=x^{3}+4x$ has exactly $p$ solutions in integers modulo $p$. In other words, determine all prime numbers $p$ with the following property: there exist exactly $p$ ordered pairs of integers $(x, y)$ such that $x, y \\in\\{0,1, \\ldots, p-1\\}$ and $p \\text{ divides } y^{2}-x^{3}-4x$."}, {"pid": "129", "question": "Two fair octahedral dice, each with the numbers 1 through 8 on their faces, are rolled. Let $N$ be the remainder when the product of the numbers showing on the two dice is divided by 8. Find the expected value of $N$.", "answer": "\\frac{11}{4}", "image": null, "query": "Two fair octahedral dice, each with the numbers 1 through 8 on their faces, are rolled. Let $N$ be the remainder when the product of the numbers showing on the two dice is divided by 8. Find the expected value of $N$."}, {"pid": "130", "question": "Suppose that $x$ and $y$ are positive numbers with $xy=\\frac{1}{9}$, $x(y+1)=\\frac{7}{9}$, and $y(x+1)=\\frac{5}{18}$. What is the value of $(x+1)(y+1)$?", "answer": "\\frac{35}{18}", "image": null, "query": "Suppose that $x$ and $y$ are positive numbers with $xy=\\frac{1}{9}$, $x(y+1)=\\frac{7}{9}$, and $y(x+1)=\\frac{5}{18}$. What is the value of $(x+1)(y+1)$?"}, {"pid": "131", "question": "Let $ABC$ be a triangle with $AB=5, BC=4$ and $AC=3$. Let $\\mathcal{P}$ and $\\mathcal{Q}$ be squares inside $ABC$ with disjoint interiors such that they both have one side lying on $AB$. Also, the two squares each have an edge lying on a common line perpendicular to $AB$, and $\\mathcal{P}$ has one vertex on $AC$ and $\\mathcal{Q}$ has one vertex on $BC$. Determine the minimum value of the sum of the areas of the two squares.", "answer": "\\frac{144}{49}", "image": null, "query": "Let $ABC$ be a triangle with $AB=5, BC=4$ and $AC=3$. Let $\\mathcal{P}$ and $\\mathcal{Q}$ be squares inside $ABC$ with disjoint interiors such that they both have one side lying on $AB$. Also, the two squares each have an edge lying on a common line perpendicular to $AB$, and $\\mathcal{P}$ has one vertex on $AC$ and $\\mathcal{Q}$ has one vertex on $BC$. Determine the minimum value of the sum of the areas of the two squares."}, {"pid": "132", "question": "The rectangular flag shown is divided into seven stripes of equal height. The height of the flag is $h$ and the length of the flag is twice its height. The total area of the four shaded regions is $1400 \\mathrm{~cm}^{2}$. What is the height of the flag?", "answer": "35 \\mathrm{~cm}", "image": null, "query": "The rectangular flag shown is divided into seven stripes of equal height. The height of the flag is $h$ and the length of the flag is twice its height. The total area of the four shaded regions is $1400 \\mathrm{~cm}^{2}$. What is the height of the flag?"}, {"pid": "133", "question": "Fifteen freshmen are sitting in a circle around a table, but the course assistant (who remains standing) has made only six copies of today's handout. No freshman should get more than one handout, and any freshman who does not get one should be able to read a neighbor's. If the freshmen are distinguishable but the handouts are not, how many ways are there to distribute the six handouts subject to the above conditions?", "answer": "125", "image": null, "query": "Fifteen freshmen are sitting in a circle around a table, but the course assistant (who remains standing) has made only six copies of today's handout. No freshman should get more than one handout, and any freshman who does not get one should be able to read a neighbor's. If the freshmen are distinguishable but the handouts are not, how many ways are there to distribute the six handouts subject to the above conditions?"}, {"pid": "134", "question": "Find the number of eight-digit positive integers that are multiples of 9 and have all distinct digits.", "answer": "181440", "image": null, "query": "Find the number of eight-digit positive integers that are multiples of 9 and have all distinct digits."}, {"pid": "135", "question": "A square in the $xy$-plane has area $A$, and three of its vertices have $x$-coordinates 2, 0, and 18 in some order. Find the sum of all possible values of $A$.", "answer": "1168", "image": null, "query": "A square in the $xy$-plane has area $A$, and three of its vertices have $x$-coordinates 2, 0, and 18 in some order. Find the sum of all possible values of $A$."}, {"pid": "136", "question": "We are given $n$ coins of different weights and $n$ balances, $n>2$. On each turn one can choose one balance, put one coin on the right pan and one on the left pan, and then delete these coins out of the balance. It's known that one balance is wrong (but it's not known ehich exactly), and it shows an arbitrary result on every turn. What is the smallest number of turns required to find the heaviest coin?\n\n[hide=Thanks]Thanks to the user Vlados021 for translating the problem.[/hide]", "answer": "2n - 1", "image": null, "query": "We are given $n$ coins of different weights and $n$ balances, $n>2$. On each turn one can choose one balance, put one coin on the right pan and one on the left pan, and then delete these coins out of the balance. It's known that one balance is wrong (but it's not known ehich exactly), and it shows an arbitrary result on every turn. What is the smallest number of turns required to find the heaviest coin?\n\n[hide=Thanks]Thanks to the user Vlados021 for translating the problem.[/hide]"}, {"pid": "137", "question": "<PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. <PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. <PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. What is the probability that the product of their three integers is not a power of 2?", "answer": "\\frac{98}{125}", "image": null, "query": "<PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. <PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. <PERSON> chooses an integer at random from the set $\\{2,4,6,8,10\\}$. What is the probability that the product of their three integers is not a power of 2?"}, {"pid": "138", "question": "Find all polynomials $P$ with real coefficients such that \\[\\frac{P(x)}{yz}+\\frac{P(y)}{zx}+\\frac{P(z)}{xy}=P(x-y)+P(y-z)+P(z-x)\\] holds for all nonzero real numbers $x,y,z$ satisfying $2xyz=x+y+z$ .", "answer": "\\[ P(x) = c(x^2 + 3) \\text{ for any constant } c. \\]", "image": null, "query": "Find all polynomials $P$ with real coefficients such that \\[\\frac{P(x)}{yz}+\\frac{P(y)}{zx}+\\frac{P(z)}{xy}=P(x-y)+P(y-z)+P(z-x)\\] holds for all nonzero real numbers $x,y,z$ satisfying $2xyz=x+y+z$ ."}, {"pid": "139", "question": "How many positive integers less than 1998 are relatively prime to 1547 ? (Two integers are relatively prime if they have no common factors besides 1.)", "answer": "1487", "image": null, "query": "How many positive integers less than 1998 are relatively prime to 1547 ? (Two integers are relatively prime if they have no common factors besides 1.)"}, {"pid": "140", "question": "Two circles with equal radii intersect as shown. The area of the shaded region equals the sum of the areas of the two unshaded regions. If the area of the shaded region is $216\\pi$, what is the circumference of each circle?", "answer": "36\\pi", "image": null, "query": "Two circles with equal radii intersect as shown. The area of the shaded region equals the sum of the areas of the two unshaded regions. If the area of the shaded region is $216\\pi$, what is the circumference of each circle?"}, {"pid": "141", "question": "What fraction of the entire wall is painted red if <PERSON> paints half of her section red and <PERSON> paints one third of her section red?", "answer": "\\frac{5}{12}", "image": null, "query": "What fraction of the entire wall is painted red if <PERSON> paints half of her section red and <PERSON> paints one third of her section red?"}, {"pid": "142", "question": "Let $X$ be the number of sequences of integers $a_{1}, a_{2}, \\ldots, a_{2047}$ that satisfy all of the following properties: - Each $a_{i}$ is either 0 or a power of 2 . - $a_{i}=a_{2 i}+a_{2 i+1}$ for $1 \\leq i \\leq 1023$ - $a_{1}=1024$. Find the remainder when $X$ is divided by 100 .", "answer": "15", "image": null, "query": "Let $X$ be the number of sequences of integers $a_{1}, a_{2}, \\ldots, a_{2047}$ that satisfy all of the following properties: - Each $a_{i}$ is either 0 or a power of 2 . - $a_{i}=a_{2 i}+a_{2 i+1}$ for $1 \\leq i \\leq 1023$ - $a_{1}=1024$. Find the remainder when $X$ is divided by 100 ."}, {"pid": "143", "question": "Which of the following expressions is equal to an odd integer for every integer $n$?", "answer": "2017+2n", "image": null, "query": "Which of the following expressions is equal to an odd integer for every integer $n$?"}, {"pid": "144", "question": "Find all functions $f$ from the set of real numbers into the set of real numbers which satisfy for all $x$, $y$ the identity \\[ f\\left(xf(x+y)\\right) = f\\left(yf(x)\\right) +x^2\\]\n\n[i]", "answer": "f(x) = x \\text{ for all } x \\in \\mathbb{R}f(x) = -x \\text{ for all } x \\in \\mathbb{R}", "image": null, "query": "Find all functions $f$ from the set of real numbers into the set of real numbers which satisfy for all $x$, $y$ the identity \\[ f\\left(xf(x+y)\\right) = f\\left(yf(x)\\right) +x^2\\]\n\n[i]"}, {"pid": "145", "question": "For any real number $\\alpha$, define $$\\operatorname{sign}(\\alpha)= \\begin{cases}+1 & \\text { if } \\alpha>0 \\\\ 0 & \\text { if } \\alpha=0 \\\\ -1 & \\text { if } \\alpha<0\\end{cases}$$ How many triples $(x, y, z) \\in \\mathbb{R}^{3}$ satisfy the following system of equations $$\\begin{aligned} & x=2018-2019 \\cdot \\operatorname{sign}(y+z) \\\\ & y=2018-2019 \\cdot \\operatorname{sign}(z+x) \\\\ & z=2018-2019 \\cdot \\operatorname{sign}(x+y) \\end{aligned}$$", "answer": "3", "image": null, "query": "For any real number $\\alpha$, define $$\\operatorname{sign}(\\alpha)= \\begin{cases}+1 & \\text { if } \\alpha>0 \\\\ 0 & \\text { if } \\alpha=0 \\\\ -1 & \\text { if } \\alpha<0\\end{cases}$$ How many triples $(x, y, z) \\in \\mathbb{R}^{3}$ satisfy the following system of equations $$\\begin{aligned} & x=2018-2019 \\cdot \\operatorname{sign}(y+z) \\\\ & y=2018-2019 \\cdot \\operatorname{sign}(z+x) \\\\ & z=2018-2019 \\cdot \\operatorname{sign}(x+y) \\end{aligned}$$"}, {"pid": "146", "question": "Suppose that $f$ is a function from $\\mathbb{R}$ to $\\mathbb{R}$ such that \\[ f(x) + f\\left( 1 - \\frac{1}{x} \\right) = \\arctan x \\] for all real $x \\neq 0$. (As usual, $y = \\arctan x$ means $-\\pi/2 < y < \\pi/2$ and $\\tan y = x$.) Find \\[ \\int_0^1 f(x)\\,dx. \\]", "answer": "\\frac{3\\pi}{8}", "image": null, "query": "Suppose that $f$ is a function from $\\mathbb{R}$ to $\\mathbb{R}$ such that \\[ f(x) + f\\left( 1 - \\frac{1}{x} \\right) = \\arctan x \\] for all real $x \\neq 0$. (As usual, $y = \\arctan x$ means $-\\pi/2 < y < \\pi/2$ and $\\tan y = x$.) Find \\[ \\int_0^1 f(x)\\,dx. \\]"}, {"pid": "147", "question": "A convex figure $F$ is such that any equilateral triangle with side $1$ has a parallel translation that takes all its vertices to the boundary of $F$. Is $F$ necessarily a circle?", "answer": "\\text{No}", "image": null, "query": "A convex figure $F$ is such that any equilateral triangle with side $1$ has a parallel translation that takes all its vertices to the boundary of $F$. Is $F$ necessarily a circle?"}, {"pid": "148", "question": "Define the sequence $\\{x_{i}\\}_{i \\geq 0}$ by $x_{0}=2009$ and $x_{n}=-\\frac{2009}{n} \\sum_{k=0}^{n-1} x_{k}$ for all $n \\geq 1$. Compute the value of $\\sum_{n=0}^{2009} 2^{n} x_{n}$", "answer": "2009", "image": null, "query": "Define the sequence $\\{x_{i}\\}_{i \\geq 0}$ by $x_{0}=2009$ and $x_{n}=-\\frac{2009}{n} \\sum_{k=0}^{n-1} x_{k}$ for all $n \\geq 1$. Compute the value of $\\sum_{n=0}^{2009} 2^{n} x_{n}$"}, {"pid": "149", "question": "What is the minimum total number of boxes that <PERSON><PERSON> could have bought if each treat bag contains exactly 1 chocolate, 1 mint, and 1 caramel, and chocolates come in boxes of 50, mints in boxes of 40, and caramels in boxes of 25?", "answer": "17", "image": null, "query": "What is the minimum total number of boxes that <PERSON><PERSON> could have bought if each treat bag contains exactly 1 chocolate, 1 mint, and 1 caramel, and chocolates come in boxes of 50, mints in boxes of 40, and caramels in boxes of 25?"}, {"pid": "150", "question": "An ordered pair $(a, b)$ of positive integers is called spicy if $\\operatorname{gcd}(a+b, ab+1)=1$. Compute the probability that both $(99, n)$ and $(101, n)$ are spicy when $n$ is chosen from $\\{1,2, \\ldots, 2024\\}$ uniformly at random.", "answer": "\\frac{96}{595}", "image": null, "query": "An ordered pair $(a, b)$ of positive integers is called spicy if $\\operatorname{gcd}(a+b, ab+1)=1$. Compute the probability that both $(99, n)$ and $(101, n)$ are spicy when $n$ is chosen from $\\{1,2, \\ldots, 2024\\}$ uniformly at random."}, {"pid": "151", "question": "<PERSON> and <PERSON> play a game in which they take turns choosing integers from $1$ to $n$. Before any integers are chosen, <PERSON> selects a goal of \"odd\" or \"even\". On the first turn, <PERSON> chooses one of the $n$ integers. On the second turn, <PERSON> chooses one of the remaining integers. They continue alternately choosing one of the integers that has not yet been chosen, until the $n$th turn, which is forced and ends the game. <PERSON> wins if the parity of $\\{k\\colon \\mbox{the number $k$ was chosen on the $k$th turn}\\}$ matches his goal. For which values of $n$ does <PERSON> have a winning strategy?", "answer": "For all $n$, <PERSON> has a winning strategy.", "image": null, "query": "<PERSON> and <PERSON> play a game in which they take turns choosing integers from $1$ to $n$. Before any integers are chosen, <PERSON> selects a goal of \"odd\" or \"even\". On the first turn, <PERSON> chooses one of the $n$ integers. On the second turn, <PERSON> chooses one of the remaining integers. They continue alternately choosing one of the integers that has not yet been chosen, until the $n$th turn, which is forced and ends the game. <PERSON> wins if the parity of $\\{k\\colon \\mbox{the number $k$ was chosen on the $k$th turn}\\}$ matches his goal. For which values of $n$ does <PERSON> have a winning strategy?"}, {"pid": "152", "question": "We denote by $\\mathbb{R}^\\plus{}$ the set of all positive real numbers.\n\nFind all functions $f: \\mathbb R^ \\plus{} \\rightarrow\\mathbb R^ \\plus{}$ which have the property:\n\\[f(x)f(y)\\equal{}2f(x\\plus{}yf(x))\\]\nfor all positive real numbers $x$ and $y$.\n\n[i]", "answer": "f(x) = 2", "image": null, "query": "We denote by $\\mathbb{R}^\\plus{}$ the set of all positive real numbers.\n\nFind all functions $f: \\mathbb R^ \\plus{} \\rightarrow\\mathbb R^ \\plus{}$ which have the property:\n\\[f(x)f(y)\\equal{}2f(x\\plus{}yf(x))\\]\nfor all positive real numbers $x$ and $y$.\n\n[i]"}, {"pid": "153", "question": "<PERSON><PERSON> is in a tournament in which no game can end in a tie. She continues to play games until she loses 2 games, at which point she is eliminated and plays no more games. The probability of <PERSON><PERSON> winning the first game is $\frac{1}{2}$. After she wins a game, the probability of <PERSON><PERSON> winning the next game is $\frac{3}{4}$. After she loses a game, the probability of <PERSON><PERSON> winning the next game is $\frac{1}{3}$. What is the probability that <PERSON><PERSON> wins 3 games before being eliminated from the tournament?", "answer": "23", "image": null, "query": "<PERSON><PERSON> is in a tournament in which no game can end in a tie. She continues to play games until she loses 2 games, at which point she is eliminated and plays no more games. The probability of <PERSON><PERSON> winning the first game is $\frac{1}{2}$. After she wins a game, the probability of <PERSON><PERSON> winning the next game is $\frac{3}{4}$. After she loses a game, the probability of <PERSON><PERSON> winning the next game is $\frac{1}{3}$. What is the probability that <PERSON><PERSON> wins 3 games before being eliminated from the tournament?"}, {"pid": "154", "question": "Kermit the frog enjoys hopping around the infinite square grid in his backyard. It takes him 1 Joule of energy to hop one step north or one step south, and 1 Joule of energy to hop one step east or one step west. He wakes up one morning on the grid with 100 Joules of energy, and hops till he falls asleep with 0 energy. How many different places could he have gone to sleep?", "answer": "10201", "image": null, "query": "Kermit the frog enjoys hopping around the infinite square grid in his backyard. It takes him 1 Joule of energy to hop one step north or one step south, and 1 Joule of energy to hop one step east or one step west. He wakes up one morning on the grid with 100 Joules of energy, and hops till he falls asleep with 0 energy. How many different places could he have gone to sleep?"}, {"pid": "155", "question": "What is the side length of the larger square if a small square is drawn inside a larger square, and the area of the shaded region and the area of the unshaded region are each $18 \\mathrm{~cm}^{2}$?", "answer": "6 \\mathrm{~cm}", "image": null, "query": "What is the side length of the larger square if a small square is drawn inside a larger square, and the area of the shaded region and the area of the unshaded region are each $18 \\mathrm{~cm}^{2}$?"}, {"pid": "156", "question": "Six soccer teams are competing in a tournament in Waterloo. Every team is to play three games, each against a different team. How many different schedules are possible?", "answer": "70", "image": null, "query": "Six soccer teams are competing in a tournament in Waterloo. Every team is to play three games, each against a different team. How many different schedules are possible?"}, {"pid": "157", "question": "<PERSON> starts with a number $n$, then repeatedly flips a fair coin. If it lands heads he subtracts 1 from his number and if it lands tails he subtracts 2 . Let $E_{n}$ be the expected number of flips <PERSON> does before his number is zero or negative. Find the pair $(a, b)$ such that $$ \\lim _{n \\rightarrow \\infty}\\left(E_{n}-a n-b\\right)=0 $$", "answer": "\\left(\\frac{2}{3}, \\frac{2}{9}\\right)", "image": null, "query": "<PERSON> starts with a number $n$, then repeatedly flips a fair coin. If it lands heads he subtracts 1 from his number and if it lands tails he subtracts 2 . Let $E_{n}$ be the expected number of flips <PERSON> does before his number is zero or negative. Find the pair $(a, b)$ such that $$ \\lim _{n \\rightarrow \\infty}\\left(E_{n}-a n-b\\right)=0 $$"}, {"pid": "158", "question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is walking on an $8 \\times 8$ chessboard that wraps around at its edges (so squares on the left edge of the chessboard are adjacent to squares on the right edge, and similarly for the top and bottom edges). Each femtosecond, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moves in one of the four diagonal directions uniformly at random. After 2012 femtoseconds, what is the probability that <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is at his original location?", "answer": "\\left(\\frac{1+2^{1005}}{2^{1007}}\\right)^{2}", "image": null, "query": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is walking on an $8 \\times 8$ chessboard that wraps around at its edges (so squares on the left edge of the chessboard are adjacent to squares on the right edge, and similarly for the top and bottom edges). Each femtosecond, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> moves in one of the four diagonal directions uniformly at random. After 2012 femtoseconds, what is the probability that <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is at his original location?"}, {"pid": "159", "question": "In triangle $BCD$, $\\angle CBD=\\angle CDB$ because $BC=CD$. If $\\angle BCD=80+50+30=160$, find $\\angle CBD=\\angle CDB$.", "answer": "10", "image": null, "query": "In triangle $BCD$, $\\angle CBD=\\angle CDB$ because $BC=CD$. If $\\angle BCD=80+50+30=160$, find $\\angle CBD=\\angle CDB$."}, {"pid": "160", "question": "We say that a subset of $\\mathbb{R}^{n}$ is $k$-almost contained by a hyperplane if there are less than $k$ points in that set which do not belong to the hyperplane. We call a finite set of points $k$-generic if there is no hyperplane that $k$-almost contains the set. For each pair of positive integers $k$ and $n$, find the minimal number $d(k, n)$ such that every finite $k$-generic set in $\\mathbb{R}^{n}$ contains a $k$-generic subset with at most $d(k, n)$ elements.", "answer": "d(k, n) = k \\cdot n \\text{ if } k, n > 1 \\text{, otherwise } d(k, n) = k + n", "image": null, "query": "We say that a subset of $\\mathbb{R}^{n}$ is $k$-almost contained by a hyperplane if there are less than $k$ points in that set which do not belong to the hyperplane. We call a finite set of points $k$-generic if there is no hyperplane that $k$-almost contains the set. For each pair of positive integers $k$ and $n$, find the minimal number $d(k, n)$ such that every finite $k$-generic set in $\\mathbb{R}^{n}$ contains a $k$-generic subset with at most $d(k, n)$ elements."}, {"pid": "161", "question": "In a game, there are three indistinguishable boxes; one box contains two red balls, one contains two blue balls, and the last contains one ball of each color. To play, <PERSON> first predicts whether he will draw two balls of the same color or two of different colors. Then, he picks a box, draws a ball at random, looks at the color, and replaces the ball in the same box. Finally, he repeats this; however, the boxes are not shuffled between draws, so he can determine whether he wants to draw again from the same box. <PERSON> wins if he predicts correctly; if he plays optimally, what is the probability that he will win?", "answer": "\\frac{5}{6}", "image": null, "query": "In a game, there are three indistinguishable boxes; one box contains two red balls, one contains two blue balls, and the last contains one ball of each color. To play, <PERSON> first predicts whether he will draw two balls of the same color or two of different colors. Then, he picks a box, draws a ball at random, looks at the color, and replaces the ball in the same box. Finally, he repeats this; however, the boxes are not shuffled between draws, so he can determine whether he wants to draw again from the same box. <PERSON> wins if he predicts correctly; if he plays optimally, what is the probability that he will win?"}, {"pid": "162", "question": "When 100 is divided by a positive integer $x$, the remainder is 10. When 1000 is divided by $x$, what is the remainder?", "answer": "10", "image": null, "query": "When 100 is divided by a positive integer $x$, the remainder is 10. When 1000 is divided by $x$, what is the remainder?"}, {"pid": "163", "question": "We call a two-variable polynomial $P(x, y)$ [i]secretly one-variable,[/i] if there exist polynomials $Q(x)$ and $R(x, y)$ such that $\\deg(Q) \\ge 2$ and $P(x, y) = Q(R(x, y))$ (e.g. $x^2 + 1$ and $x^2y^2 +1$ are [i]secretly one-variable[/i], but $xy + 1$ is not).\n\nProve or disprove the following statement: If $P(x, y)$ is a polynomial such that both $P(x, y)$ and $P(x, y) + 1$ can be written as the product of two non-constant polynomials, then $P$ is [i]secretly one-variable[/i].", "answer": "\\text{True}", "image": null, "query": "We call a two-variable polynomial $P(x, y)$ [i]secretly one-variable,[/i] if there exist polynomials $Q(x)$ and $R(x, y)$ such that $\\deg(Q) \\ge 2$ and $P(x, y) = Q(R(x, y))$ (e.g. $x^2 + 1$ and $x^2y^2 +1$ are [i]secretly one-variable[/i], but $xy + 1$ is not).\n\nProve or disprove the following statement: If $P(x, y)$ is a polynomial such that both $P(x, y)$ and $P(x, y) + 1$ can be written as the product of two non-constant polynomials, then $P$ is [i]secretly one-variable[/i]."}, {"pid": "164", "question": "Let $k \\geq 14$ be an integer, and let $p_{k}$ be the largest prime number which is strictly less than $k$. You may assume that $p_{k} \\geq 3 k / 4$. Let $n$ be a composite integer. Prove: (a) if $n=2 p_{k}$, then $n$ does not divide $(n-k)$ !; (b) if $n>2 p_{k}$, then $n$ divides $(n-k)$ ! .", "answer": "(a) $2 p_{k} \\nmid (n-k)!$\n\n(b) $n \\mid (n-k)!$", "image": null, "query": "Let $k \\geq 14$ be an integer, and let $p_{k}$ be the largest prime number which is strictly less than $k$. You may assume that $p_{k} \\geq 3 k / 4$. Let $n$ be a composite integer. Prove: (a) if $n=2 p_{k}$, then $n$ does not divide $(n-k)$ !; (b) if $n>2 p_{k}$, then $n$ divides $(n-k)$ ! ."}, {"pid": "165", "question": "Let $A B C D$ be a cyclic quadrilateral, and let segments $A C$ and $B D$ intersect at $E$. Let $W$ and $Y$ be the feet of the altitudes from $E$ to sides $D A$ and $B C$, respectively, and let $X$ and $Z$ be the midpoints of sides $A B$ and $C D$, respectively. Given that the area of $A E D$ is 9, the area of $B E C$ is 25, and $\\angle E B C-\\angle E C B=30^{\\circ}$, then compute the area of $W X Y Z$.", "answer": "17+\\frac{15}{2} \\sqrt{3}", "image": null, "query": "Let $A B C D$ be a cyclic quadrilateral, and let segments $A C$ and $B D$ intersect at $E$. Let $W$ and $Y$ be the feet of the altitudes from $E$ to sides $D A$ and $B C$, respectively, and let $X$ and $Z$ be the midpoints of sides $A B$ and $C D$, respectively. Given that the area of $A E D$ is 9, the area of $B E C$ is 25, and $\\angle E B C-\\angle E C B=30^{\\circ}$, then compute the area of $W X Y Z$."}, {"pid": "166", "question": "The list $p, q, r, s$ consists of four consecutive integers listed in increasing order. If $p + s = 109$, what is the value of $q + r$?", "answer": "109", "image": null, "query": "The list $p, q, r, s$ consists of four consecutive integers listed in increasing order. If $p + s = 109$, what is the value of $q + r$?"}, {"pid": "167", "question": "How many 8-digit numbers begin with 1 , end with 3 , and have the property that each successive digit is either one more or two more than the previous digit, considering 0 to be one more than 9 ?", "answer": "21", "image": null, "query": "How many 8-digit numbers begin with 1 , end with 3 , and have the property that each successive digit is either one more or two more than the previous digit, considering 0 to be one more than 9 ?"}, {"pid": "168", "question": "Compute the number of ways to tile a $3 \\times 5$ rectangle with one $1 \\times 1$ tile, one $1 \\times 2$ tile, one $1 \\times 3$ tile, one $1 \\times 4$ tile, and one $1 \\times 5$ tile. (The tiles can be rotated, and tilings that differ by rotation or reflection are considered distinct.)", "answer": "40", "image": null, "query": "Compute the number of ways to tile a $3 \\times 5$ rectangle with one $1 \\times 1$ tile, one $1 \\times 2$ tile, one $1 \\times 3$ tile, one $1 \\times 4$ tile, and one $1 \\times 5$ tile. (The tiles can be rotated, and tilings that differ by rotation or reflection are considered distinct.)"}, {"pid": "169", "question": "Every pair of communities in a county are linked directly by one mode of transportation; bus, train, or airplane. All three methods of transportation are used in the county with no community being serviced by all three modes and no three communities being linked pairwise by the same mode. Determine the largest number of communities in this county.", "answer": "4", "image": null, "query": "Every pair of communities in a county are linked directly by one mode of transportation; bus, train, or airplane. All three methods of transportation are used in the county with no community being serviced by all three modes and no three communities being linked pairwise by the same mode. Determine the largest number of communities in this county."}, {"pid": "170", "question": "What is the sum of all of the possibilities for <PERSON>'s number if <PERSON> thinks of a 5-digit number, <PERSON>'s friend <PERSON> tries to guess his number, <PERSON> writes the number of matching digits beside each of <PERSON>'s guesses, and a digit is considered \"matching\" when it is the correct digit in the correct position?", "answer": "526758", "image": null, "query": "What is the sum of all of the possibilities for <PERSON>'s number if <PERSON> thinks of a 5-digit number, <PERSON>'s friend <PERSON> tries to guess his number, <PERSON> writes the number of matching digits beside each of <PERSON>'s guesses, and a digit is considered \"matching\" when it is the correct digit in the correct position?"}, {"pid": "171", "question": "For a finite set $A$ of positive integers, a partition of $A$ into two disjoint nonempty subsets $A_1$ and $A_2$ is $\\textit{good}$ if the least common multiple of the elements in $A_1$ is equal to the greatest common divisor of the elements in $A_2$. Determine the minimum value of $n$ such that there exists a set of $n$ positive integers with exactly $2015$ good partitions.", "answer": "3024", "image": null, "query": "For a finite set $A$ of positive integers, a partition of $A$ into two disjoint nonempty subsets $A_1$ and $A_2$ is $\\textit{good}$ if the least common multiple of the elements in $A_1$ is equal to the greatest common divisor of the elements in $A_2$. Determine the minimum value of $n$ such that there exists a set of $n$ positive integers with exactly $2015$ good partitions."}, {"pid": "172", "question": "A new website registered $2000$ people. Each of them invited $1000$ other registered people to be their friends. Two people are considered to be friends if and only if they have invited each other. What is the minimum number of pairs of friends on this website?", "answer": "1000", "image": null, "query": "A new website registered $2000$ people. Each of them invited $1000$ other registered people to be their friends. Two people are considered to be friends if and only if they have invited each other. What is the minimum number of pairs of friends on this website?"}, {"pid": "173", "question": "Find the number of arrangements of 4 beads (2 red, 2 green, 2 blue) in a circle such that the two red beads are not adjacent.", "answer": "11", "image": null, "query": "Find the number of arrangements of 4 beads (2 red, 2 green, 2 blue) in a circle such that the two red beads are not adjacent."}, {"pid": "174", "question": "Find the rightmost non-zero digit of the expansion of (20)(13!).", "answer": "6", "image": null, "query": "Find the rightmost non-zero digit of the expansion of (20)(13!)."}, {"pid": "175", "question": "Compute the number of ordered quintuples of nonnegative integers $(a_{1}, a_{2}, a_{3}, a_{4}, a_{5})$ such that $0 \\leq a_{1}, a_{2}, a_{3}, a_{4}, a_{5} \\leq 7$ and 5 divides $2^{a_{1}}+2^{a_{2}}+2^{a_{3}}+2^{a_{4}}+2^{a_{5}}$.", "answer": "6528", "image": null, "query": "Compute the number of ordered quintuples of nonnegative integers $(a_{1}, a_{2}, a_{3}, a_{4}, a_{5})$ such that $0 \\leq a_{1}, a_{2}, a_{3}, a_{4}, a_{5} \\leq 7$ and 5 divides $2^{a_{1}}+2^{a_{2}}+2^{a_{3}}+2^{a_{4}}+2^{a_{5}}$."}, {"pid": "176", "question": "Solve in positive real numbers: $n+ \\lfloor \\sqrt{n} \\rfloor+\\lfloor \\sqrt[3]{n} \\rfloor=2014$", "answer": "1958", "image": null, "query": "Solve in positive real numbers: $n+ \\lfloor \\sqrt{n} \\rfloor+\\lfloor \\sqrt[3]{n} \\rfloor=2014$"}, {"pid": "177", "question": "Suppose $f(x)$ is a rational function such that $3f\\left(\\frac{1}{x}\\right) + \\frac{2f(x)}{x} = x^{2}$ for $x \\neq 0$. Find $f(-2)$.", "answer": "\\frac{67}{20}", "image": null, "query": "Suppose $f(x)$ is a rational function such that $3f\\left(\\frac{1}{x}\\right) + \\frac{2f(x)}{x} = x^{2}$ for $x \\neq 0$. Find $f(-2)$."}, {"pid": "178", "question": "Let $A B C D$ be a rectangle, and let $E$ and $F$ be points on segment $A B$ such that $A E=E F=F B$. If $C E$ intersects the line $A D$ at $P$, and $P F$ intersects $B C$ at $Q$, determine the ratio of $B Q$ to $C Q$.", "answer": "\\frac{1}{3}", "image": null, "query": "Let $A B C D$ be a rectangle, and let $E$ and $F$ be points on segment $A B$ such that $A E=E F=F B$. If $C E$ intersects the line $A D$ at $P$, and $P F$ intersects $B C$ at $Q$, determine the ratio of $B Q$ to $C Q$."}, {"pid": "179", "question": "Does there exist a set $M$ in usual Euclidean space such that for every plane $\\lambda$ the intersection $M \\cap \\lambda$ is finite and nonempty ?\n\n[i]\n[hide=\"Remark\"]I'm not sure I'm posting this in a right Forum.[/hide]", "answer": "\\text{yes}", "image": null, "query": "Does there exist a set $M$ in usual Euclidean space such that for every plane $\\lambda$ the intersection $M \\cap \\lambda$ is finite and nonempty ?\n\n[i]\n[hide=\"Remark\"]I'm not sure I'm posting this in a right Forum.[/hide]"}, {"pid": "180", "question": "Find all functions $f:\\mathbb{R} \\to \\mathbb{R}$ satisfying the equation \\[\n\tf(x^2+y^2+2f(xy)) = (f(x+y))^2.\n\\] for all $x,y \\in \\mathbb{R}$.", "answer": "$f(x) = x,f(x) = 0 \\text{ and all functions of the form } f(x) =\\left\\{\\begin{matrix}\n  1,&x \\notin X, \\\\\n  -1,&x \\in X,\n\\end{matrix}\\right. \\text{ where } X \\subset (-\\infty , \\frac{-2}{3} ) $", "image": null, "query": "Find all functions $f:\\mathbb{R} \\to \\mathbb{R}$ satisfying the equation \\[\n\tf(x^2+y^2+2f(xy)) = (f(x+y))^2.\n\\] for all $x,y \\in \\mathbb{R}$."}, {"pid": "181", "question": "<PERSON><PERSON><PERSON> runs 16 km in 1.5 hours. He runs the first 10 km at an average speed of $12 \\mathrm{~km} / \\mathrm{h}$. What is his average speed for the last 6 km?", "answer": "9 \\mathrm{~km} / \\mathrm{h}", "image": null, "query": "<PERSON><PERSON><PERSON> runs 16 km in 1.5 hours. He runs the first 10 km at an average speed of $12 \\mathrm{~km} / \\mathrm{h}$. What is his average speed for the last 6 km?"}, {"pid": "182", "question": "How many ways are there to insert +'s between the digits of 111111111111111 (fifteen 1's) so that the result will be a multiple of 30?", "answer": "2002", "image": null, "query": "How many ways are there to insert +'s between the digits of 111111111111111 (fifteen 1's) so that the result will be a multiple of 30?"}, {"pid": "183", "question": "Is there a number $n$ such that one can write $n$ as the sum of $2017$ perfect squares and (with at least) $2017$ distinct ways?", "answer": "\\text{Yes}", "image": null, "query": "Is there a number $n$ such that one can write $n$ as the sum of $2017$ perfect squares and (with at least) $2017$ distinct ways?"}, {"pid": "184", "question": "For any integer $k$, write $f_{k}(x)=\\left(1+x^{2}\\right)^{-1-k}$. When $k \\geq 1$, find constants $c_{1}, c_{2}$ such that the function $y=\\left(S f_{k}\\right)(x)$ solves a second order differential equation $x y^{\\prime \\prime}+c_{1} y^{\\prime}+c_{2} x y=0$.", "answer": "c_{1}=-2 k, c_{2}=-4 \\pi^{2}", "image": null, "query": "For any integer $k$, write $f_{k}(x)=\\left(1+x^{2}\\right)^{-1-k}$. When $k \\geq 1$, find constants $c_{1}, c_{2}$ such that the function $y=\\left(S f_{k}\\right)(x)$ solves a second order differential equation $x y^{\\prime \\prime}+c_{1} y^{\\prime}+c_{2} x y=0$."}, {"pid": "185", "question": "For how many values of $n$ with $3 \\leq n \\leq 12$ can a Fano table be created?", "answer": "3", "image": null, "query": "For how many values of $n$ with $3 \\leq n \\leq 12$ can a Fano table be created?"}, {"pid": "186", "question": "A point is equidistant from the coordinate axes if the vertical distance from the point to the $x$-axis is equal to the horizontal distance from the point to the $y$-axis. The point of intersection of the vertical line $x = a$ with the line with equation $3x + 8y = 24$ is equidistant from the coordinate axes. What is the sum of all possible values of $a$?", "answer": "-\\frac{144}{55}", "image": null, "query": "A point is equidistant from the coordinate axes if the vertical distance from the point to the $x$-axis is equal to the horizontal distance from the point to the $y$-axis. The point of intersection of the vertical line $x = a$ with the line with equation $3x + 8y = 24$ is equidistant from the coordinate axes. What is the sum of all possible values of $a$?"}, {"pid": "187", "question": "Compute the sum of all two-digit positive integers $x$ such that for all three-digit (base 10) positive integers \\underline{a} \\underline{b} \\underline{c}, if \\underline{a} \\underline{b} \\underline{c} is a multiple of $x$, then the three-digit (base 10) number \\underline{b} \\underline{c} \\underline{a} is also a multiple of $x$.", "answer": "64", "image": null, "query": "Compute the sum of all two-digit positive integers $x$ such that for all three-digit (base 10) positive integers \\underline{a} \\underline{b} \\underline{c}, if \\underline{a} \\underline{b} \\underline{c} is a multiple of $x$, then the three-digit (base 10) number \\underline{b} \\underline{c} \\underline{a} is also a multiple of $x$."}, {"pid": "188", "question": "Determine the value of the sum $$\\frac{3}{1^{2} \\cdot 2^{2}}+\\frac{5}{2^{2} \\cdot 3^{2}}+\\frac{7}{3^{2} \\cdot 4^{2}}+\\cdots+\\frac{29}{14^{2} \\cdot 15^{2}}$$", "answer": "\\frac{224}{225}", "image": null, "query": "Determine the value of the sum $$\\frac{3}{1^{2} \\cdot 2^{2}}+\\frac{5}{2^{2} \\cdot 3^{2}}+\\frac{7}{3^{2} \\cdot 4^{2}}+\\cdots+\\frac{29}{14^{2} \\cdot 15^{2}}$$"}, {"pid": "189", "question": "Find the value of $1006 \\sin \\frac{\\pi}{1006}$. Approximating directly by $\\pi=3.1415 \\ldots$ is worth only 3 points.", "answer": "3.1415875473", "image": null, "query": "Find the value of $1006 \\sin \\frac{\\pi}{1006}$. Approximating directly by $\\pi=3.1415 \\ldots$ is worth only 3 points."}, {"pid": "190", "question": "Let $N$ be the number of ways in which the letters in \"HMMTH<PERSON><PERSON><PERSON>THMMTHMMTHMMT\" (\"HMMT\" repeated six times) can be rearranged so that each letter is adjacent to another copy of the same letter. For example, \"MMMMMMTTTTTTHHHHHHHHHHHH\" satisfies this property, but \"HMMMMM<PERSON><PERSON>TTHHHHHHHHHHHM\" does not. Estimate $N$. An estimate of $E$ will earn $\\left\\lfloor 20 \\min \\left(\\frac{N}{E}, \\frac{E}{N}\\right)^{4}\\right\\rfloor$ points.", "answer": "78556", "image": null, "query": "Let $N$ be the number of ways in which the letters in \"HMMTH<PERSON><PERSON><PERSON>THMMTHMMTHMMT\" (\"HMMT\" repeated six times) can be rearranged so that each letter is adjacent to another copy of the same letter. For example, \"MMMMMMTTTTTTHHHHHHHHHHHH\" satisfies this property, but \"HMMMMM<PERSON><PERSON>TTHHHHHHHHHHHM\" does not. Estimate $N$. An estimate of $E$ will earn $\\left\\lfloor 20 \\min \\left(\\frac{N}{E}, \\frac{E}{N}\\right)^{4}\\right\\rfloor$ points."}, {"pid": "191", "question": "Given an angle \\(\\theta\\), consider the polynomial \\(P(x)=\\sin(\\theta)x^{2}+\\cos(\\theta)x+\\tan(\\theta)x+1\\). Given that \\(P\\) only has one real root, find all possible values of \\(\\sin(\\theta)\\).", "answer": "0, \\frac{\\sqrt{5}-1}{2}", "image": null, "query": "Given an angle \\(\\theta\\), consider the polynomial \\(P(x)=\\sin(\\theta)x^{2}+\\cos(\\theta)x+\\tan(\\theta)x+1\\). Given that \\(P\\) only has one real root, find all possible values of \\(\\sin(\\theta)\\)."}, {"pid": "192", "question": "Let $S$ be the smallest subset of the integers with the property that $0 \\in S$ and for any $x \\in S$, we have $3 x \\in S$ and $3 x+1 \\in S$. Determine the number of non-negative integers in $S$ less than 2008.", "answer": "128", "image": null, "query": "Let $S$ be the smallest subset of the integers with the property that $0 \\in S$ and for any $x \\in S$, we have $3 x \\in S$ and $3 x+1 \\in S$. Determine the number of non-negative integers in $S$ less than 2008."}, {"pid": "193", "question": "Determine all integers $n\\geqslant 2$ with the following property: every $n$ pairwise distinct integers whose sum is not divisible by $n$ can be arranged in some order $a_1,a_2,\\ldots, a_n$ so that $n$ divides $1\\cdot a_1+2\\cdot a_2+\\cdots+n\\cdot a_n.$\n\n[i]<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>[/i]", "answer": "$\\text{ Odd numbers and powers of 2 }$", "image": null, "query": "Determine all integers $n\\geqslant 2$ with the following property: every $n$ pairwise distinct integers whose sum is not divisible by $n$ can be arranged in some order $a_1,a_2,\\ldots, a_n$ so that $n$ divides $1\\cdot a_1+2\\cdot a_2+\\cdots+n\\cdot a_n.$\n\n[i]<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>[/i]"}, {"pid": "194", "question": "<PERSON><PERSON> chooses 5 of the vertices of a unit cube. What is the maximum possible volume of the figure whose vertices are the 5 chosen points?", "answer": "\\frac{1}{2}", "image": null, "query": "<PERSON><PERSON> chooses 5 of the vertices of a unit cube. What is the maximum possible volume of the figure whose vertices are the 5 chosen points?"}, {"pid": "195", "question": "Does there exist a sequence $ F(1), F(2), F(3), \\ldots$ of non-negative integers that simultaneously satisfies the following three conditions?\n\n[b](a)[/b] Each of the integers $ 0, 1, 2, \\ldots$ occurs in the sequence.\n[b](b)[/b] Each positive integer occurs in the sequence infinitely often.\n[b](c)[/b] For any $ n \\geq 2,$\n\\[ F(F(n^{163})) \\equal{} F(F(n)) \\plus{} F(F(361)).\n\\]", "answer": "\\text{Yes}", "image": null, "query": "Does there exist a sequence $ F(1), F(2), F(3), \\ldots$ of non-negative integers that simultaneously satisfies the following three conditions?\n\n[b](a)[/b] Each of the integers $ 0, 1, 2, \\ldots$ occurs in the sequence.\n[b](b)[/b] Each positive integer occurs in the sequence infinitely often.\n[b](c)[/b] For any $ n \\geq 2,$\n\\[ F(F(n^{163})) \\equal{} F(F(n)) \\plus{} F(F(361)).\n\\]"}, {"pid": "196", "question": "A snake of length $k$ is an animal which occupies an ordered $k$-tuple $\\left(s_{1}, \\ldots, s_{k}\\right)$ of cells in a $n \\times n$ grid of square unit cells. These cells must be pairwise distinct, and $s_{i}$ and $s_{i+1}$ must share a side for $i=1, \\ldots, k-1$. If the snake is currently occupying $\\left(s_{1}, \\ldots, s_{k}\\right)$ and $s$ is an unoccupied cell sharing a side with $s_{1}$, the snake can move to occupy $\\left(s, s_{1}, \\ldots, s_{k-1}\\right)$ instead. Initially, a snake of length 4 is in the grid $\\{1,2, \\ldots, 30\\}^{2}$ occupying the positions $(1,1),(1,2),(1,3),(1,4)$ with $(1,1)$ as its head. The snake repeatedly makes a move uniformly at random among moves it can legally make. Estimate $N$, the expected number of moves the snake makes before it has no legal moves remaining.", "answer": "4571.8706930", "image": null, "query": "A snake of length $k$ is an animal which occupies an ordered $k$-tuple $\\left(s_{1}, \\ldots, s_{k}\\right)$ of cells in a $n \\times n$ grid of square unit cells. These cells must be pairwise distinct, and $s_{i}$ and $s_{i+1}$ must share a side for $i=1, \\ldots, k-1$. If the snake is currently occupying $\\left(s_{1}, \\ldots, s_{k}\\right)$ and $s$ is an unoccupied cell sharing a side with $s_{1}$, the snake can move to occupy $\\left(s, s_{1}, \\ldots, s_{k-1}\\right)$ instead. Initially, a snake of length 4 is in the grid $\\{1,2, \\ldots, 30\\}^{2}$ occupying the positions $(1,1),(1,2),(1,3),(1,4)$ with $(1,1)$ as its head. The snake repeatedly makes a move uniformly at random among moves it can legally make. Estimate $N$, the expected number of moves the snake makes before it has no legal moves remaining."}, {"pid": "197", "question": "For $0 \\leq p \\leq 1/2$, let $X_1, X_2, \\dots$ be independent random variables such that \\[ X_i = \\begin{cases} 1 & \\mbox{with probability $p$,} \\\\ -1 & \\mbox{with probability $p$,} \\\\ 0 & \\mbox{with probability $1-2p$,} \\end{cases} \\] for all $i \\geq 1$. Given a positive integer $n$ and integers $b, a_1, \\dots, a_n$, let $P(b, a_1, \\dots, a_n)$ denote the probability that $a_1 X_1 + \\cdots + a_n X_n = b$. For which values of $p$ is it the case that \\[ P(0, a_1, \\dots, a_n) \\geq P(b, a_1, \\dots, a_n) \\] for all positive integers $n$ and all integers $b, a_1, \\dots, a_n$?", "answer": "p \\leq 1/4", "image": null, "query": "For $0 \\leq p \\leq 1/2$, let $X_1, X_2, \\dots$ be independent random variables such that \\[ X_i = \\begin{cases} 1 & \\mbox{with probability $p$,} \\\\ -1 & \\mbox{with probability $p$,} \\\\ 0 & \\mbox{with probability $1-2p$,} \\end{cases} \\] for all $i \\geq 1$. Given a positive integer $n$ and integers $b, a_1, \\dots, a_n$, let $P(b, a_1, \\dots, a_n)$ denote the probability that $a_1 X_1 + \\cdots + a_n X_n = b$. For which values of $p$ is it the case that \\[ P(0, a_1, \\dots, a_n) \\geq P(b, a_1, \\dots, a_n) \\] for all positive integers $n$ and all integers $b, a_1, \\dots, a_n$?"}, {"pid": "198", "question": "Compute the positive real number $x$ satisfying $x^{\\left(2 x^{6}\\right)}=3$", "answer": "\\sqrt[6]{3}", "image": null, "query": "Compute the positive real number $x$ satisfying $x^{\\left(2 x^{6}\\right)}=3$"}, {"pid": "199", "question": "One needs to \fffll the cells of an $n\\times n$ table ($n > 1$) with distinct integers from $1$ to $n^2$ so that every two consecutive integers are placed in cells that share a side, while every two integers with the same remainder if divided by $n$ are placed in distinct rows and distinct columns. For which $n$ is this possible?\n\n(<PERSON><PERSON><PERSON>)", "answer": "\\text{even } n", "image": null, "query": "One needs to \fffll the cells of an $n\\times n$ table ($n > 1$) with distinct integers from $1$ to $n^2$ so that every two consecutive integers are placed in cells that share a side, while every two integers with the same remainder if divided by $n$ are placed in distinct rows and distinct columns. For which $n$ is this possible?\n\n(<PERSON><PERSON><PERSON>)"}, {"pid": "200", "question": "Find all real solutions to $x^{4}+(2-x)^{4}=34$.", "answer": "1 \\pm \\sqrt{2}", "image": null, "query": "Find all real solutions to $x^{4}+(2-x)^{4}=34$."}, {"pid": "201", "question": "A light pulse starts at a corner of a reflective square. It bounces around inside the square, reflecting off of the square's perimeter $n$ times before ending in a different corner. The path of the light pulse, when traced, divides the square into exactly 2021 regions. Compute the smallest possible value of $n$.", "answer": "129", "image": null, "query": "A light pulse starts at a corner of a reflective square. It bounces around inside the square, reflecting off of the square's perimeter $n$ times before ending in a different corner. The path of the light pulse, when traced, divides the square into exactly 2021 regions. Compute the smallest possible value of $n$."}, {"pid": "202", "question": "A physicist encounters $2015$ atoms called usamons. Each usamon either has one electron or zero electrons, and the physicist can't tell the difference.  The physicist's only tool is a diode. The physicist may connect the diode from any usamon $A$ to any other usamon $B$. (This connection is directed.) When she does so, if usamon $A$ has an electron and usamon $B$ does not, then the electron jumps from $A$ to $B$. In any other case, nothing happens. In addition, the physicist cannot tell whether an electron jumps during any given step.  The physicist's goal is to isolate two usamons that she is  sure are currently in the same state. Is there any series of diode usage that makes this possible?", "answer": "\\text{No}", "image": null, "query": "A physicist encounters $2015$ atoms called usamons. Each usamon either has one electron or zero electrons, and the physicist can't tell the difference.  The physicist's only tool is a diode. The physicist may connect the diode from any usamon $A$ to any other usamon $B$. (This connection is directed.) When she does so, if usamon $A$ has an electron and usamon $B$ does not, then the electron jumps from $A$ to $B$. In any other case, nothing happens. In addition, the physicist cannot tell whether an electron jumps during any given step.  The physicist's goal is to isolate two usamons that she is  sure are currently in the same state. Is there any series of diode usage that makes this possible?"}, {"pid": "203", "question": "There are $2n$ students in a school $(n \\in \\mathbb{N}, n \\geq 2)$. Each week $n$ students go on a trip. After several trips the following condition was fulfilled: every two students were together on at least one trip. What is the minimum number of trips needed for this to happen?", "answer": "6", "image": null, "query": "There are $2n$ students in a school $(n \\in \\mathbb{N}, n \\geq 2)$. Each week $n$ students go on a trip. After several trips the following condition was fulfilled: every two students were together on at least one trip. What is the minimum number of trips needed for this to happen?"}, {"pid": "204", "question": "A sequence of positive integers $a_{1}, a_{2}, a_{3}, \\ldots$ satisfies $$a_{n+1}=n\\left\\lfloor\\frac{a_{n}}{n}\\right\\rfloor+1$$ for all positive integers $n$. If $a_{30}=30$, how many possible values can $a_{1}$ take? (For a real number $x$, $\\lfloor x\\rfloor$ denotes the largest integer that is not greater than $x$.)", "answer": "274", "image": null, "query": "A sequence of positive integers $a_{1}, a_{2}, a_{3}, \\ldots$ satisfies $$a_{n+1}=n\\left\\lfloor\\frac{a_{n}}{n}\\right\\rfloor+1$$ for all positive integers $n$. If $a_{30}=30$, how many possible values can $a_{1}$ take? (For a real number $x$, $\\lfloor x\\rfloor$ denotes the largest integer that is not greater than $x$.)"}, {"pid": "205", "question": "<PERSON><PERSON> and <PERSON><PERSON> play a game on an $8\\times 8$ board, making moves in turn. During his turn, <PERSON><PERSON> must put one cross in any empty cell (i.e., in a cell in which a cross has not yet been drawn and which has not yet been covered with a domino). <PERSON><PERSON> must cover two adjacent cells with a domino (which are not yet covered with other dominoes), in which there are an even number of crosses in total (0 or 2).  The one who can't make a move loses. Which of does the player have a winning strategy, if\n[list=a]\n[*]<PERSON><PERSON> makes the first move?\n[*]<PERSON><PERSON> makes the first move?\n[/list]\n[i]", "answer": "\\text{<PERSON><PERSON> wins if <PERSON><PERSON> starts, and <PERSON><PERSON> wins if <PERSON><PERSON> starts.}", "image": null, "query": "<PERSON><PERSON> and <PERSON><PERSON> play a game on an $8\\times 8$ board, making moves in turn. During his turn, <PERSON><PERSON> must put one cross in any empty cell (i.e., in a cell in which a cross has not yet been drawn and which has not yet been covered with a domino). <PERSON><PERSON> must cover two adjacent cells with a domino (which are not yet covered with other dominoes), in which there are an even number of crosses in total (0 or 2).  The one who can't make a move loses. Which of does the player have a winning strategy, if\n[list=a]\n[*]<PERSON><PERSON> makes the first move?\n[*]<PERSON><PERSON> makes the first move?\n[/list]\n[i]"}, {"pid": "206", "question": "Let $n$ be a positive integer. Let there be $P_{n}$ ways for <PERSON> Penny to make exactly $n$ dollars out of quarters, dimes, nickels, and pennies. Also, let there be $B_{n}$ ways for <PERSON> Bill to make exactly $n$ dollars out of one dollar bills, quarters, dimes, and nickels. As $n$ goes to infinity, the sequence of fractions \\frac{P_{n}}{B_{n}}$ approaches a real number $c$. Find $c$.", "answer": "20", "image": null, "query": "Let $n$ be a positive integer. Let there be $P_{n}$ ways for <PERSON> Penny to make exactly $n$ dollars out of quarters, dimes, nickels, and pennies. Also, let there be $B_{n}$ ways for <PERSON> Bill to make exactly $n$ dollars out of one dollar bills, quarters, dimes, and nickels. As $n$ goes to infinity, the sequence of fractions \\frac{P_{n}}{B_{n}}$ approaches a real number $c$. Find $c$."}, {"pid": "207", "question": "Let $a, b$, and $c$ be the 3 roots of $x^{3}-x+1=0$. Find $\\frac{1}{a+1}+\\frac{1}{b+1}+\\frac{1}{c+1}$.", "answer": "-2", "image": null, "query": "Let $a, b$, and $c$ be the 3 roots of $x^{3}-x+1=0$. Find $\\frac{1}{a+1}+\\frac{1}{b+1}+\\frac{1}{c+1}$."}, {"pid": "208", "question": "Find all positive integers, such that there exist positive integers $a, b, c$, satisfying $\\gcd(a, b, c)=1$ and $n=\\gcd(ab+c, ac-b)=a+b+c$.", "answer": "\\text{All positive integers } n \\text{ with prime factors } 1 \\pmod{4}.", "image": null, "query": "Find all positive integers, such that there exist positive integers $a, b, c$, satisfying $\\gcd(a, b, c)=1$ and $n=\\gcd(ab+c, ac-b)=a+b+c$."}, {"pid": "209", "question": "The average age of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> is 22 years. Given that <PERSON><PERSON> is 23 and <PERSON> is 24, what is <PERSON><PERSON><PERSON>'s age?", "answer": "19", "image": null, "query": "The average age of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> is 22 years. Given that <PERSON><PERSON> is 23 and <PERSON> is 24, what is <PERSON><PERSON><PERSON>'s age?"}, {"pid": "210", "question": "In a rhombus $P Q R S$ with $P Q=Q R=R S=S P=S Q=6$ and $P T=R T=14$, what is the length of $S T$?", "answer": "10", "image": null, "query": "In a rhombus $P Q R S$ with $P Q=Q R=R S=S P=S Q=6$ and $P T=R T=14$, what is the length of $S T$?"}, {"pid": "211", "question": "Let $n$ be a positive integer.  There are $\\tfrac{n(n+1)}{2}$ marks, each with a black side and a white side, arranged into an equilateral triangle, with the biggest row containing $n$ marks.  Initially, each mark has the black side up.  An operation is to choose a line parallel to the sides of the triangle, and flipping all the marks on that line.  A configuration is called admissible if it can be obtained from the initial configuration by performing a finite number of operations.  For each admissible configuration $C$ , let $f(C)$ denote the smallest number of operations required to obtain $C$ from the initial configuration.  Find the maximum value of $f(C)$ , where $C$ varies over all admissible configurations.", "answer": "The problem does not have a provided solution, so the final answer cannot be extracted.", "image": null, "query": "Let $n$ be a positive integer.  There are $\\tfrac{n(n+1)}{2}$ marks, each with a black side and a white side, arranged into an equilateral triangle, with the biggest row containing $n$ marks.  Initially, each mark has the black side up.  An operation is to choose a line parallel to the sides of the triangle, and flipping all the marks on that line.  A configuration is called admissible if it can be obtained from the initial configuration by performing a finite number of operations.  For each admissible configuration $C$ , let $f(C)$ denote the smallest number of operations required to obtain $C$ from the initial configuration.  Find the maximum value of $f(C)$ , where $C$ varies over all admissible configurations."}, {"pid": "212", "question": "Let $A,B,C,D$ denote four points in space such that at most one of the distances $AB,AC,AD,BC,BD,CD$ is greater than $1$ . Determine the maximum value of the sum of the six distances.", "answer": "\\[ 5 + \\sqrt{3} \\]", "image": null, "query": "Let $A,B,C,D$ denote four points in space such that at most one of the distances $AB,AC,AD,BC,BD,CD$ is greater than $1$ . Determine the maximum value of the sum of the six distances."}, {"pid": "213", "question": "<PERSON> and <PERSON> are driving on a circular track with radius 25 km. <PERSON> leaves the starting line first, going clockwise at 80 km/h. Fifteen minutes later, <PERSON> leaves the same starting line, going counterclockwise at 100 km/h. For how many hours will <PERSON> have been driving when they pass each other for the fourth time?", "answer": "\\\\frac{10\\\\pi-1}{9}", "image": null, "query": "<PERSON> and <PERSON> are driving on a circular track with radius 25 km. <PERSON> leaves the starting line first, going clockwise at 80 km/h. Fifteen minutes later, <PERSON> leaves the same starting line, going counterclockwise at 100 km/h. For how many hours will <PERSON> have been driving when they pass each other for the fourth time?"}, {"pid": "214", "question": "For every $a \\in \\mathbb N$ denote by $M(a)$ the number of elements of the set\n\\[ \\{ b \\in \\mathbb N | a + b \\text{  is a divisor of } ab \\}.\\]\nFind $\\max_{a\\leq 1983} M(a).$", "answer": "121", "image": null, "query": "For every $a \\in \\mathbb N$ denote by $M(a)$ the number of elements of the set\n\\[ \\{ b \\in \\mathbb N | a + b \\text{  is a divisor of } ab \\}.\\]\nFind $\\max_{a\\leq 1983} M(a).$"}, {"pid": "215", "question": "For integral $m$, let $p(m)$ be the greatest prime divisor of $m.$ By convention, we set $p(\\pm 1) = 1$ and $p(0) = \\infty.$ Find all polynomials $f$ with integer coefficients such that the sequence\n\\[ \\{p \\left( f \\left( n^2 \\right) \\right) - 2n \\}_{n \\geq 0}  \\] is bounded above. (In particular, this requires $f \\left (n^2 \\right ) \\neq 0$ for $n \\geq 0.$)", "answer": "f(x) = T \\cdot \\prod_{i=1}^{m} (4x - a_i)", "image": null, "query": "For integral $m$, let $p(m)$ be the greatest prime divisor of $m.$ By convention, we set $p(\\pm 1) = 1$ and $p(0) = \\infty.$ Find all polynomials $f$ with integer coefficients such that the sequence\n\\[ \\{p \\left( f \\left( n^2 \\right) \\right) - 2n \\}_{n \\geq 0}  \\] is bounded above. (In particular, this requires $f \\left (n^2 \\right ) \\neq 0$ for $n \\geq 0.$)"}, {"pid": "216", "question": "Find all functions $f:\\mathbb{R}\\rightarrow\\mathbb{R}$ that satisfy \\[f(x^2-y)+2yf(x)=f(f(x))+f(y)\\] for all $x,y\\in\\mathbb{R}$ .", "answer": "\\[ f(x) = -x^2, \\quad f(x) = 0, \\quad f(x) = x^2 \\]", "image": null, "query": "Find all functions $f:\\mathbb{R}\\rightarrow\\mathbb{R}$ that satisfy \\[f(x^2-y)+2yf(x)=f(f(x))+f(y)\\] for all $x,y\\in\\mathbb{R}$ ."}, {"pid": "217", "question": "A binary tree is a tree in which each node has exactly two descendants. Suppose that each node of the tree is coloured black with probability \\(p\\), and white otherwise, independently of all other nodes. For any path \\(\\pi\\) containing \\(n\\) nodes beginning at the root of the tree, let \\(B(\\pi)\\) be the number of black nodes in \\(\\pi\\), and let \\(X_{n}(k)\\) be the number of such paths \\(\\pi\\) for which \\(B(\\pi) \\geq k\\). (1) Show that there exists \\(\\beta_{c}\\) such that \\(\\lim _{n \\rightarrow \\infty} \\mathbb{E}\\left(X_{n}(\\beta n)\\right)= \\begin{cases}0, & \\text { if } \\beta>\\beta_{c} \\\\ \\infty, & \\text { if } \\beta<\\beta_{c}\\end{cases}\\) How to determine the value of \\(\\beta_{c}\\) ? (2) For \\(\\beta \\neq \\beta_{c}\\), find the limit \\(\\lim _{n \\rightarrow \\infty} \\mathbb{P}\\left(X_{n}(\\beta n) \\geq 1\\right)\\).", "answer": "Existence of \\(\\beta_{c}\\) and limits as described in the solution.", "image": null, "query": "A binary tree is a tree in which each node has exactly two descendants. Suppose that each node of the tree is coloured black with probability \\(p\\), and white otherwise, independently of all other nodes. For any path \\(\\pi\\) containing \\(n\\) nodes beginning at the root of the tree, let \\(B(\\pi)\\) be the number of black nodes in \\(\\pi\\), and let \\(X_{n}(k)\\) be the number of such paths \\(\\pi\\) for which \\(B(\\pi) \\geq k\\). (1) Show that there exists \\(\\beta_{c}\\) such that \\(\\lim _{n \\rightarrow \\infty} \\mathbb{E}\\left(X_{n}(\\beta n)\\right)= \\begin{cases}0, & \\text { if } \\beta>\\beta_{c} \\\\ \\infty, & \\text { if } \\beta<\\beta_{c}\\end{cases}\\) How to determine the value of \\(\\beta_{c}\\) ? (2) For \\(\\beta \\neq \\beta_{c}\\), find the limit \\(\\lim _{n \\rightarrow \\infty} \\mathbb{P}\\left(X_{n}(\\beta n) \\geq 1\\right)\\)."}, {"pid": "218", "question": "Let $n \\geq 2$ be an integer. Find all real numbers $a$ such that there exist real numbers $x_{1}$, $\\ldots, x_{n}$ satisfying $$x_{1}\\left(1-x_{2}\\right)=x_{2}\\left(1-x_{3}\\right)=\\ldots=x_{n-1}\\left(1-x_{n}\\right)=x_{n}\\left(1-x_{1}\\right)=a$$", "answer": "(-\\infty, \\frac{1}{4}] \\cup \\{\\frac{1}{4 \\cos^{2} \\frac{k\\pi}{n}}; k \\in \\mathbb{N}, 1 \\leq k < \\frac{n}{2}\\}", "image": null, "query": "Let $n \\geq 2$ be an integer. Find all real numbers $a$ such that there exist real numbers $x_{1}$, $\\ldots, x_{n}$ satisfying $$x_{1}\\left(1-x_{2}\\right)=x_{2}\\left(1-x_{3}\\right)=\\ldots=x_{n-1}\\left(1-x_{n}\\right)=x_{n}\\left(1-x_{1}\\right)=a$$"}, {"pid": "219", "question": "A regular dodecagon $P_{1} P_{2} \\cdots P_{12}$ is inscribed in a unit circle with center $O$. Let $X$ be the intersection of $P_{1} P_{5}$ and $O P_{2}$, and let $Y$ be the intersection of $P_{1} P_{5}$ and $O P_{4}$. Let $A$ be the area of the region bounded by $X Y, X P_{2}, Y P_{4}$, and minor arc $\\widehat{P_{2} P_{4}}$. Compute $\\lfloor 120 A\\rfloor$.", "answer": "45", "image": null, "query": "A regular dodecagon $P_{1} P_{2} \\cdots P_{12}$ is inscribed in a unit circle with center $O$. Let $X$ be the intersection of $P_{1} P_{5}$ and $O P_{2}$, and let $Y$ be the intersection of $P_{1} P_{5}$ and $O P_{4}$. Let $A$ be the area of the region bounded by $X Y, X P_{2}, Y P_{4}$, and minor arc $\\widehat{P_{2} P_{4}}$. Compute $\\lfloor 120 A\\rfloor$."}, {"pid": "220", "question": "In circle $\\omega$, two perpendicular chords intersect at a point $P$. The two chords have midpoints $M_{1}$ and $M_{2}$ respectively, such that $P M_{1}=15$ and $P M_{2}=20$. Line $M_{1} M_{2}$ intersects $\\omega$ at points $A$ and $B$, with $M_{1}$ between $A$ and $M_{2}$. Compute the largest possible value of $B M_{2}-A M_{1}$.", "answer": "7", "image": null, "query": "In circle $\\omega$, two perpendicular chords intersect at a point $P$. The two chords have midpoints $M_{1}$ and $M_{2}$ respectively, such that $P M_{1}=15$ and $P M_{2}=20$. Line $M_{1} M_{2}$ intersects $\\omega$ at points $A$ and $B$, with $M_{1}$ between $A$ and $M_{2}$. Compute the largest possible value of $B M_{2}-A M_{1}$."}, {"pid": "221", "question": "Let $m$ be a fixed positive integer. The infinite sequence $\\{a_n\\}_{n\\geq 1}$ is defined in the following way: $a_1$ is a positive integer, and for every integer $n\\geq 1$ we have\n$$a_{n+1} = \\begin{cases}a_n^2+2^m & \\text{if } a_n< 2^m \\\\ a_n/2 &\\text{if } a_n\\geq 2^m\\end{cases}$$\nFor each $m$, determine all possible values of $a_1$ such that every term in the sequence is an integer.", "answer": "m = 2 \\text{ and } a_1 \\text{ is a power of two}", "image": null, "query": "Let $m$ be a fixed positive integer. The infinite sequence $\\{a_n\\}_{n\\geq 1}$ is defined in the following way: $a_1$ is a positive integer, and for every integer $n\\geq 1$ we have\n$$a_{n+1} = \\begin{cases}a_n^2+2^m & \\text{if } a_n< 2^m \\\\ a_n/2 &\\text{if } a_n\\geq 2^m\\end{cases}$$\nFor each $m$, determine all possible values of $a_1$ such that every term in the sequence is an integer."}, {"pid": "222", "question": "<PERSON> spends $\\$ 1.20$ per litre on gasoline. He uses an average of 1 L of gasoline to drive 12.5 km. How much will <PERSON> spend on gasoline to drive 50 km?", "answer": "\\$ 4.80", "image": null, "query": "<PERSON> spends $\\$ 1.20$ per litre on gasoline. He uses an average of 1 L of gasoline to drive 12.5 km. How much will <PERSON> spend on gasoline to drive 50 km?"}, {"pid": "223", "question": "Over all real numbers $x$ and $y$, find the minimum possible value of $$ (x y)^{2}+(x+7)^{2}+(2 y+7)^{2} $$", "answer": "45", "image": null, "query": "Over all real numbers $x$ and $y$, find the minimum possible value of $$ (x y)^{2}+(x+7)^{2}+(2 y+7)^{2} $$"}, {"pid": "224", "question": "<PERSON>, <PERSON>, and <PERSON> play a game in which each randomly picks and then replaces a card from a standard 52 card deck, until a spades card is drawn. What is the probability that <PERSON> draws the spade? (<PERSON>, <PERSON>, and <PERSON> draw in that order, and the game repeats if no spade is drawn.)", "answer": "\\frac{12}{37}", "image": null, "query": "<PERSON>, <PERSON>, and <PERSON> play a game in which each randomly picks and then replaces a card from a standard 52 card deck, until a spades card is drawn. What is the probability that <PERSON> draws the spade? (<PERSON>, <PERSON>, and <PERSON> draw in that order, and the game repeats if no spade is drawn.)"}, {"pid": "225", "question": "Let $A B C D$ be a convex quadrilateral so that all of its sides and diagonals have integer lengths. Given that $\\angle A B C=\\angle A D C=90^{\\circ}, A B=B D$, and $C D=41$, find the length of $B C$.", "answer": "580", "image": null, "query": "Let $A B C D$ be a convex quadrilateral so that all of its sides and diagonals have integer lengths. Given that $\\angle A B C=\\angle A D C=90^{\\circ}, A B=B D$, and $C D=41$, find the length of $B C$."}, {"pid": "226", "question": "You are given an unlimited supply of red, blue, and yellow cards to form a hand. Each card has a point value and your score is the sum of the point values of those cards. The point values are as follows: the value of each red card is 1 , the value of each blue card is equal to twice the number of red cards, and the value of each yellow card is equal to three times the number of blue cards. What is the maximum score you can get with fifteen cards?", "answer": "168", "image": null, "query": "You are given an unlimited supply of red, blue, and yellow cards to form a hand. Each card has a point value and your score is the sum of the point values of those cards. The point values are as follows: the value of each red card is 1 , the value of each blue card is equal to twice the number of red cards, and the value of each yellow card is equal to three times the number of blue cards. What is the maximum score you can get with fifteen cards?"}, {"pid": "227", "question": "A sequence of 11 positive real numbers, $a_{1}, a_{2}, a_{3}, \\ldots, a_{11}$, satisfies $a_{1}=4$ and $a_{11}=1024$ and $a_{n}+a_{n-1}=\\frac{5}{2} \\sqrt{a_{n} \\cdot a_{n-1}}$ for every integer $n$ with $2 \\leq n \\leq 11$. For example when $n=7, a_{7}+a_{6}=\\frac{5}{2} \\sqrt{a_{7} \\cdot a_{6}}$. There are $S$ such sequences. What are the rightmost two digits of $S$?", "answer": "20", "image": null, "query": "A sequence of 11 positive real numbers, $a_{1}, a_{2}, a_{3}, \\ldots, a_{11}$, satisfies $a_{1}=4$ and $a_{11}=1024$ and $a_{n}+a_{n-1}=\\frac{5}{2} \\sqrt{a_{n} \\cdot a_{n-1}}$ for every integer $n$ with $2 \\leq n \\leq 11$. For example when $n=7, a_{7}+a_{6}=\\frac{5}{2} \\sqrt{a_{7} \\cdot a_{6}}$. There are $S$ such sequences. What are the rightmost two digits of $S$?"}, {"pid": "228", "question": "Let $f: \\mathbb{N} \\rightarrow \\mathbb{N}$ be a function satisfying the following conditions:\n(1) $f(1)=1$;\n(2) $\\forall n\\in \\mathbb{N}$, $3f(n) f(2n+1) =f(2n) ( 1+3f(n) )$;\n(3) $\\forall n\\in \\mathbb{N}$, $f(2n) < 6 f(n)$.\nFind all solutions of equation $f(k) +f(l)=293$, where $k<l$.\n($\\mathbb{N}$ denotes the set of all natural numbers).", "answer": "(5, 47), (7, 45), (13, 39), (15, 37)", "image": null, "query": "Let $f: \\mathbb{N} \\rightarrow \\mathbb{N}$ be a function satisfying the following conditions:\n(1) $f(1)=1$;\n(2) $\\forall n\\in \\mathbb{N}$, $3f(n) f(2n+1) =f(2n) ( 1+3f(n) )$;\n(3) $\\forall n\\in \\mathbb{N}$, $f(2n) < 6 f(n)$.\nFind all solutions of equation $f(k) +f(l)=293$, where $k<l$.\n($\\mathbb{N}$ denotes the set of all natural numbers)."}, {"pid": "229", "question": "We are given $2021$ points on a plane, no three of which are collinear. Among any $5$ of these points, at least $4$ lie on the same circle. Is it necessarily true that at least $2020$ of the points lie on the same circle?", "answer": "\\text{Yes}", "image": null, "query": "We are given $2021$ points on a plane, no three of which are collinear. Among any $5$ of these points, at least $4$ lie on the same circle. Is it necessarily true that at least $2020$ of the points lie on the same circle?"}, {"pid": "230", "question": "Evaluate $1201201_{-4}$.", "answer": "2017", "image": null, "query": "Evaluate $1201201_{-4}$."}, {"pid": "231", "question": "Problem\n<PERSON> is piling $m\\geq 1$ indistinguishable stones on the squares of an $n\\times n$ grid. Each square can have an arbitrarily high pile of stones. After he finished piling his stones in some manner, he can then perform stone moves, defined as follows. Consider any four grid squares, which are corners of a rectangle, i.e. in positions $(i, k), (i, l), (j, k), (j, l)$ for some $1\\leq i, j, k, l\\leq n$ , such that $i<j$ and $k<l$ . A stone move consists of either removing one stone from each of $(i, k)$ and $(j, l)$ and moving them to $(i, l)$ and $(j, k)$ respectively,j or removing one stone from each of $(i, l)$ and $(j, k)$ and moving them to $(i, k)$ and $(j, l)$ respectively.\nTwo ways of piling the stones are equivalent if they can be obtained from one another by a sequence of stone moves.\nHow many different non-equivalent ways can <PERSON> pile the stones on the grid?", "answer": "\\[\n\\binom{n+m-1}{m}^{2}\n\\]", "image": null, "query": "Problem\n<PERSON> is piling $m\\geq 1$ indistinguishable stones on the squares of an $n\\times n$ grid. Each square can have an arbitrarily high pile of stones. After he finished piling his stones in some manner, he can then perform stone moves, defined as follows. Consider any four grid squares, which are corners of a rectangle, i.e. in positions $(i, k), (i, l), (j, k), (j, l)$ for some $1\\leq i, j, k, l\\leq n$ , such that $i<j$ and $k<l$ . A stone move consists of either removing one stone from each of $(i, k)$ and $(j, l)$ and moving them to $(i, l)$ and $(j, k)$ respectively,j or removing one stone from each of $(i, l)$ and $(j, k)$ and moving them to $(i, k)$ and $(j, l)$ respectively.\nTwo ways of piling the stones are equivalent if they can be obtained from one another by a sequence of stone moves.\nHow many different non-equivalent ways can <PERSON> pile the stones on the grid?"}, {"pid": "232", "question": "Let $A$ be a given set with $n$ elements. Let $k<n$ be a given positive integer. Find the maximum value of $m$ for which it is possible to choose sets $B_i$ and $C_i$ for $i=1,2,\\ldots,m$ satisfying the following conditions:\n[list=1]\n[*]$B_i\\subset A,$ $|B_i|=k,$\n[*]$C_i\\subset B_i$ (there is no additional condition for the number of elements in $C_i$), and\n[*]$B_i\\cap C_j\\neq B_j\\cap C_i$ for all $i\\neq j.$\n[/list]", "answer": "{2^k}", "image": null, "query": "Let $A$ be a given set with $n$ elements. Let $k<n$ be a given positive integer. Find the maximum value of $m$ for which it is possible to choose sets $B_i$ and $C_i$ for $i=1,2,\\ldots,m$ satisfying the following conditions:\n[list=1]\n[*]$B_i\\subset A,$ $|B_i|=k,$\n[*]$C_i\\subset B_i$ (there is no additional condition for the number of elements in $C_i$), and\n[*]$B_i\\cap C_j\\neq B_j\\cap C_i$ for all $i\\neq j.$\n[/list]"}, {"pid": "233", "question": "On a board the following six vectors are written: \\((1,0,0), \\quad(-1,0,0), \\quad(0,1,0), \\quad(0,-1,0), \\quad(0,0,1), \\quad(0,0,-1)\\). Given two vectors \\(v\\) and \\(w\\) on the board, a move consists of erasing \\(v\\) and \\(w\\) and replacing them with \\(\\frac{1}{\\sqrt{2}}(v+w)\\) and \\(\\frac{1}{\\sqrt{2}}(v-w)\\). After some number of moves, the sum of the six vectors on the board is \\(u\\). Find, with proof, the maximum possible length of \\(u\\).", "answer": "2 \\sqrt{3}", "image": null, "query": "On a board the following six vectors are written: \\((1,0,0), \\quad(-1,0,0), \\quad(0,1,0), \\quad(0,-1,0), \\quad(0,0,1), \\quad(0,0,-1)\\). Given two vectors \\(v\\) and \\(w\\) on the board, a move consists of erasing \\(v\\) and \\(w\\) and replacing them with \\(\\frac{1}{\\sqrt{2}}(v+w)\\) and \\(\\frac{1}{\\sqrt{2}}(v-w)\\). After some number of moves, the sum of the six vectors on the board is \\(u\\). Find, with proof, the maximum possible length of \\(u\\)."}, {"pid": "234", "question": "Suppose that $x, y, z$ are real numbers such that $x=y+z+2$, $y=z+x+1$, and $z=x+y+4$. Compute $x+y+z$.", "answer": "-7", "image": null, "query": "Suppose that $x, y, z$ are real numbers such that $x=y+z+2$, $y=z+x+1$, and $z=x+y+4$. Compute $x+y+z$."}, {"pid": "235", "question": "The integers \\(1,2,3,4,5,6,7,8,9,10\\) are written on a blackboard. Each day, a teacher chooses one of the integers uniformly at random and decreases it by 1. Let \\(X\\) be the expected value of the number of days which elapse before there are no longer positive integers on the board. Estimate \\(X\\). An estimate of \\(E\\) earns \\(\\left\\lfloor 20 \\cdot 2^{-|X-E| / 8}\\right\\rfloor\\) points.", "answer": "120.75280458176904", "image": null, "query": "The integers \\(1,2,3,4,5,6,7,8,9,10\\) are written on a blackboard. Each day, a teacher chooses one of the integers uniformly at random and decreases it by 1. Let \\(X\\) be the expected value of the number of days which elapse before there are no longer positive integers on the board. Estimate \\(X\\). An estimate of \\(E\\) earns \\(\\left\\lfloor 20 \\cdot 2^{-|X-E| / 8}\\right\\rfloor\\) points."}, {"pid": "236", "question": "Let $A_{1}, A_{2}, A_{3}$ be three points in the plane, and for convenience, let $A_{4}=A_{1}, A_{5}=A_{2}$. For $n=1,2$, and 3, suppose that $B_{n}$ is the midpoint of $A_{n} A_{n+1}$, and suppose that $C_{n}$ is the midpoint of $A_{n} B_{n}$. Suppose that $A_{n} C_{n+1}$ and $B_{n} A_{n+2}$ meet at $D_{n}$, and that $A_{n} B_{n+1}$ and $C_{n} A_{n+2}$ meet at $E_{n}$. Calculate the ratio of the area of triangle $D_{1} D_{2} D_{3}$ to the area of triangle $E_{1} E_{2} E_{3}$.", "answer": "\\frac{25}{49}", "image": null, "query": "Let $A_{1}, A_{2}, A_{3}$ be three points in the plane, and for convenience, let $A_{4}=A_{1}, A_{5}=A_{2}$. For $n=1,2$, and 3, suppose that $B_{n}$ is the midpoint of $A_{n} A_{n+1}$, and suppose that $C_{n}$ is the midpoint of $A_{n} B_{n}$. Suppose that $A_{n} C_{n+1}$ and $B_{n} A_{n+2}$ meet at $D_{n}$, and that $A_{n} B_{n+1}$ and $C_{n} A_{n+2}$ meet at $E_{n}$. Calculate the ratio of the area of triangle $D_{1} D_{2} D_{3}$ to the area of triangle $E_{1} E_{2} E_{3}$."}, {"pid": "237", "question": "Let $A B C$ be a triangle with $A B=6, A C=7, B C=8$. Let $I$ be the incenter of $A B C$. Points $Z$ and $Y$ lie on the interior of segments $A B$ and $A C$ respectively such that $Y Z$ is tangent to the incircle. Given point $P$ such that $$\\angle Z P C=\\angle Y P B=90^{\\circ}$$ find the length of $I P$.", "answer": "\\frac{\\sqrt{30}}{2}", "image": null, "query": "Let $A B C$ be a triangle with $A B=6, A C=7, B C=8$. Let $I$ be the incenter of $A B C$. Points $Z$ and $Y$ lie on the interior of segments $A B$ and $A C$ respectively such that $Y Z$ is tangent to the incircle. Given point $P$ such that $$\\angle Z P C=\\angle Y P B=90^{\\circ}$$ find the length of $I P$."}, {"pid": "238", "question": "Let $ n > 1$ be an integer. Find all sequences $ a_1, a_2, \\ldots a_{n^2 \\plus{} n}$ satisfying the following conditions:\n\\[ \\text{ (a) } a_i \\in \\left\\{0,1\\right\\} \\text{ for all } 1 \\leq i \\leq n^2 \\plus{} n;\n\\]\n\n\\[ \\text{ (b) } a_{i \\plus{} 1} \\plus{} a_{i \\plus{} 2} \\plus{} \\ldots \\plus{} a_{i \\plus{} n} < a_{i \\plus{} n \\plus{} 1} \\plus{} a_{i \\plus{} n \\plus{} 2} \\plus{} \\ldots \\plus{} a_{i \\plus{} 2n} \\text{ for all } 0 \\leq i \\leq n^2 \\minus{} n.\n\\]\n[i]Author: <PERSON><PERSON>, Serbia[/i]", "answer": "\\[\na_{u+vn} = \n\\begin{cases} \n0, & u+v \\le n, \\\\ \n1, & u+v \\ge n+1 \n\\end{cases} \n\\quad \\text{for all } 1 \\le u \\le n \\text{ and } 0 \\le v \\le n.\n\\]\n\\[\n\\text{The terms can be arranged into blocks of length } n \\text{ as}\n\\]\n\\[\n\\underbrace{(0 \\cdots 0)}_{n} \\underbrace{(0 \\cdots 0 \\ 1)}_{n-1} \\underbrace{(0 \\cdots 0 \\ 1 \\ 1)}_{n-2} \\cdots \\underbrace{(0 \\cdots 0 \\ 1 \\cdots 1)}_{n-v} \\underbrace{(0 \\ 1 \\cdots 1)}_{v} \\cdots \\underbrace{(0 \\ 1 \\cdots 1)}_{n-1} \\underbrace{(1 \\cdots 1)}_{n}.\n\\]", "image": null, "query": "Let $ n > 1$ be an integer. Find all sequences $ a_1, a_2, \\ldots a_{n^2 \\plus{} n}$ satisfying the following conditions:\n\\[ \\text{ (a) } a_i \\in \\left\\{0,1\\right\\} \\text{ for all } 1 \\leq i \\leq n^2 \\plus{} n;\n\\]\n\n\\[ \\text{ (b) } a_{i \\plus{} 1} \\plus{} a_{i \\plus{} 2} \\plus{} \\ldots \\plus{} a_{i \\plus{} n} < a_{i \\plus{} n \\plus{} 1} \\plus{} a_{i \\plus{} n \\plus{} 2} \\plus{} \\ldots \\plus{} a_{i \\plus{} 2n} \\text{ for all } 0 \\leq i \\leq n^2 \\minus{} n.\n\\]\n[i]Author: <PERSON><PERSON>, Serbia[/i]"}, {"pid": "239", "question": "How many different collections of 9 letters are there? A letter can appear multiple times in a collection. Two collections are equal if each letter appears the same number of times in both collections.", "answer": "\\binom{34}{9}", "image": null, "query": "How many different collections of 9 letters are there? A letter can appear multiple times in a collection. Two collections are equal if each letter appears the same number of times in both collections."}, {"pid": "240", "question": "Let $\\left(x_{1}, y_{1}\\right), \\ldots,\\left(x_{k}, y_{k}\\right)$ be the distinct real solutions to the equation $$\\left(x^{2}+y^{2}\\right)^{6}=\\left(x^{2}-y^{2}\\right)^{4}=\\left(2 x^{3}-6 x y^{2}\\right)^{3}$$ Then $\\sum_{i=1}^{k}\\left(x_{i}+y_{i}\\right)$ can be expressed as $\\frac{a}{b}$, where $a$ and $b$ are relatively prime positive integers. Compute $100 a+b$.", "answer": "516", "image": null, "query": "Let $\\left(x_{1}, y_{1}\\right), \\ldots,\\left(x_{k}, y_{k}\\right)$ be the distinct real solutions to the equation $$\\left(x^{2}+y^{2}\\right)^{6}=\\left(x^{2}-y^{2}\\right)^{4}=\\left(2 x^{3}-6 x y^{2}\\right)^{3}$$ Then $\\sum_{i=1}^{k}\\left(x_{i}+y_{i}\\right)$ can be expressed as $\\frac{a}{b}$, where $a$ and $b$ are relatively prime positive integers. Compute $100 a+b$."}, {"pid": "241", "question": "Let $A B C D$ be a tetrahedron such that its circumscribed sphere of radius $R$ and its inscribed sphere of radius $r$ are concentric. Given that $A B=A C=1 \\leq B C$ and $R=4 r$, find $B C^{2}$.", "answer": "1+\\sqrt{\\frac{7}{15}}", "image": null, "query": "Let $A B C D$ be a tetrahedron such that its circumscribed sphere of radius $R$ and its inscribed sphere of radius $r$ are concentric. Given that $A B=A C=1 \\leq B C$ and $R=4 r$, find $B C^{2}$."}, {"pid": "242", "question": "Solve the system of simultaneous equations\n\\[\\sqrt x - \\frac 1y - 2w + 3z = 1,\\]\\[x + \\frac{1}{y^2} - 4w^2 - 9z^2 = 3,\\]\\[x \\sqrt x - \\frac{1}{y^3} - 8w^3 + 27z^3 = -5,\\]\\[x^2 + \\frac{1}{y^4} - 16w^4 - 81z^4 = 15.\\]", "answer": "\\[ (x, y, w, z) = \\boxed{\\left(1, \\frac{1}{2}, -\\frac{1}{2}, \\frac{1}{3}\\right)} \\]", "image": null, "query": "Solve the system of simultaneous equations\n\\[\\sqrt x - \\frac 1y - 2w + 3z = 1,\\]\\[x + \\frac{1}{y^2} - 4w^2 - 9z^2 = 3,\\]\\[x \\sqrt x - \\frac{1}{y^3} - 8w^3 + 27z^3 = -5,\\]\\[x^2 + \\frac{1}{y^4} - 16w^4 - 81z^4 = 15.\\]"}, {"pid": "243", "question": "A sequence consists of 2010 terms. Each term after the first is 1 larger than the previous term. The sum of the 2010 terms is 5307. What is the sum when every second term is added up, starting with the first term and ending with the second last term?", "answer": "2151", "image": null, "query": "A sequence consists of 2010 terms. Each term after the first is 1 larger than the previous term. The sum of the 2010 terms is 5307. What is the sum when every second term is added up, starting with the first term and ending with the second last term?"}, {"pid": "244", "question": "A sequence $\\left\\{a_{n}\\right\\}_{n \\geq 1}$ of positive reals is defined by the rule $a_{n+1} a_{n-1}^{5}=a_{n}^{4} a_{n-2}^{2}$ for integers $n>2$ together with the initial values $a_{1}=8$ and $a_{2}=64$ and $a_{3}=1024$. Compute $$\\sqrt{a_{1}+\\sqrt{a_{2}+\\sqrt{a_{3}+\\cdots}}}$$", "answer": "3\\sqrt{2}", "image": null, "query": "A sequence $\\left\\{a_{n}\\right\\}_{n \\geq 1}$ of positive reals is defined by the rule $a_{n+1} a_{n-1}^{5}=a_{n}^{4} a_{n-2}^{2}$ for integers $n>2$ together with the initial values $a_{1}=8$ and $a_{2}=64$ and $a_{3}=1024$. Compute $$\\sqrt{a_{1}+\\sqrt{a_{2}+\\sqrt{a_{3}+\\cdots}}}$$"}, {"pid": "245", "question": "Call an ordered pair $(a, b)$ of positive integers fantastic if and only if $a, b \\leq 10^{4}$ and $\\operatorname{gcd}(a \\cdot n!-1, a \\cdot(n+1)!+b)>1$ for infinitely many positive integers $n$. Find the sum of $a+b$ across all fantastic pairs $(a, b)$.", "answer": "\\[ 5183 \\]", "image": null, "query": "Call an ordered pair $(a, b)$ of positive integers fantastic if and only if $a, b \\leq 10^{4}$ and $\\operatorname{gcd}(a \\cdot n!-1, a \\cdot(n+1)!+b)>1$ for infinitely many positive integers $n$. Find the sum of $a+b$ across all fantastic pairs $(a, b)$."}, {"pid": "246", "question": "A positive integer is called primer if it has a prime number of distinct prime factors. A positive integer is called primest if it has a primer number of distinct primer factors. A positive integer is called prime-minister if it has a primest number of distinct primest factors. Let $N$ be the smallest prime-minister number. Estimate $N$.", "answer": "378000", "image": null, "query": "A positive integer is called primer if it has a prime number of distinct prime factors. A positive integer is called primest if it has a primer number of distinct primer factors. A positive integer is called prime-minister if it has a primest number of distinct primest factors. Let $N$ be the smallest prime-minister number. Estimate $N$."}, {"pid": "247", "question": "From the point $(x, y)$, a legal move is a move to $\\left(\\frac{x}{3}+u, \\frac{y}{3}+v\\right)$, where $u$ and $v$ are real numbers such that $u^{2}+v^{2} \\leq 1$. What is the area of the set of points that can be reached from $(0,0)$ in a finite number of legal moves?", "answer": "\\frac{9 \\pi}{4}", "image": null, "query": "From the point $(x, y)$, a legal move is a move to $\\left(\\frac{x}{3}+u, \\frac{y}{3}+v\\right)$, where $u$ and $v$ are real numbers such that $u^{2}+v^{2} \\leq 1$. What is the area of the set of points that can be reached from $(0,0)$ in a finite number of legal moves?"}, {"pid": "248", "question": "Given a pair $(a_0, b_0)$ of real numbers, we define two sequences $a_0, a_1, a_2,...$ and $b_0, b_1, b_2, ...$ of real numbers by $a_{n+1}= a_n + b_n$ and $b_{n+1}=a_nb_n$ for all $n = 0, 1, 2,...$. Find all pairs $(a_0, b_0)$ of real numbers such that $a_{2022}= a_0$ and $b_{2022}= b_0$.", "answer": "(a, 0) \\text{ for any real number } a.", "image": null, "query": "Given a pair $(a_0, b_0)$ of real numbers, we define two sequences $a_0, a_1, a_2,...$ and $b_0, b_1, b_2, ...$ of real numbers by $a_{n+1}= a_n + b_n$ and $b_{n+1}=a_nb_n$ for all $n = 0, 1, 2,...$. Find all pairs $(a_0, b_0)$ of real numbers such that $a_{2022}= a_0$ and $b_{2022}= b_0$."}, {"pid": "249", "question": "Express, as concisely as possible, the value of the product $$\\left(0^{3}-350\\right)\\left(1^{3}-349\\right)\\left(2^{3}-348\\right)\\left(3^{3}-347\\right) \\cdots\\left(349^{3}-1\\right)\\left(350^{3}-0\\right)$$", "answer": "0", "image": null, "query": "Express, as concisely as possible, the value of the product $$\\left(0^{3}-350\\right)\\left(1^{3}-349\\right)\\left(2^{3}-348\\right)\\left(3^{3}-347\\right) \\cdots\\left(349^{3}-1\\right)\\left(350^{3}-0\\right)$$"}, {"pid": "250", "question": "Find all integers $\\,a,b,c\\,$ with $\\,1<a<b<c\\,$ such that \\[ (a-1)(b-1)(c-1)  \\] is a divisor of $abc-1.$", "answer": "(2, 4, 8) \\text{ and } (3, 5, 15)", "image": null, "query": "Find all integers $\\,a,b,c\\,$ with $\\,1<a<b<c\\,$ such that \\[ (a-1)(b-1)(c-1)  \\] is a divisor of $abc-1.$"}, {"pid": "251", "question": "Let $r_{1}, \\ldots, r_{n}$ be the distinct real zeroes of the equation $x^{8}-14 x^{4}-8 x^{3}-x^{2}+1=0$. Evaluate $r_{1}^{2}+\\cdots+r_{n}^{2}$", "answer": "8", "image": null, "query": "Let $r_{1}, \\ldots, r_{n}$ be the distinct real zeroes of the equation $x^{8}-14 x^{4}-8 x^{3}-x^{2}+1=0$. Evaluate $r_{1}^{2}+\\cdots+r_{n}^{2}$"}, {"pid": "252", "question": "$A B C D$ is a parallelogram satisfying $A B=7, B C=2$, and $\\angle D A B=120^{\\circ}$. Parallelogram $E C F A$ is contained in $A B C D$ and is similar to it. Find the ratio of the area of $E C F A$ to the area of $A B C D$.", "answer": "\\frac{39}{67}", "image": null, "query": "$A B C D$ is a parallelogram satisfying $A B=7, B C=2$, and $\\angle D A B=120^{\\circ}$. Parallelogram $E C F A$ is contained in $A B C D$ and is similar to it. Find the ratio of the area of $E C F A$ to the area of $A B C D$."}, {"pid": "253", "question": "A random number selector can only select one of the nine integers 1, 2, ..., 9, and it makes these selections with equal probability. Determine the probability that after $n$ selections ( $n>1$ ), the product of the $n$ numbers selected will be divisible by 10.", "answer": "\\[ 1 - \\left( \\frac{8}{9} \\right)^n - \\left( \\frac{5}{9} \\right)^n + \\left( \\frac{4}{9} \\right)^n \\]", "image": null, "query": "A random number selector can only select one of the nine integers 1, 2, ..., 9, and it makes these selections with equal probability. Determine the probability that after $n$ selections ( $n>1$ ), the product of the $n$ numbers selected will be divisible by 10."}, {"pid": "254", "question": "What is the tens digit of the smallest six-digit positive integer that is divisible by each of $10,11,12,13,14$, and 15?", "answer": "2", "image": null, "query": "What is the tens digit of the smallest six-digit positive integer that is divisible by each of $10,11,12,13,14$, and 15?"}, {"pid": "255", "question": "Given an $m \\times n$ table consisting of $mn$ unit cells. <PERSON> and <PERSON> play the following game: <PERSON> goes first and the one who moves colors one of the empty cells with one of the given three colors. <PERSON> wins if there is a figure, such as the ones below, having three different colors. Otherwise <PERSON> is the winner. Determine the winner for all cases of $m$\nand $n$ where $m, n \\ge 3$.", "answer": "\\text{ when } m \\ge 5, n \\ge 4\\text{ <PERSON> has a winning strategy, otherwise <PERSON> has }", "image": null, "query": "Given an $m \\times n$ table consisting of $mn$ unit cells. <PERSON> and <PERSON> play the following game: <PERSON> goes first and the one who moves colors one of the empty cells with one of the given three colors. <PERSON> wins if there is a figure, such as the ones below, having three different colors. Otherwise <PERSON> is the winner. Determine the winner for all cases of $m$\nand $n$ where $m, n \\ge 3$."}, {"pid": "256", "question": "<PERSON> has three sisters. The average age of the three sisters is 27. The average age of <PERSON> and his three sisters is 28. What is <PERSON>'s age?", "answer": "31", "image": null, "query": "<PERSON> has three sisters. The average age of the three sisters is 27. The average age of <PERSON> and his three sisters is 28. What is <PERSON>'s age?"}, {"pid": "257", "question": "A sequence of real numbers $a_0, a_1, . . .$ is said to be good if the following three conditions hold.\n(i) The value of $a_0$ is a positive integer.\n(ii) For each non-negative integer $i$ we have $a_{i+1} = 2a_i + 1 $ or $a_{i+1} =\\frac{a_i}{a_i + 2} $\n(iii) There exists a positive integer $k$ such that $a_k = 2014$.\n\nFind the smallest positive integer $n$ such that there exists a good sequence $a_0, a_1, . . .$ of real numbers with the property that $a_n = 2014$.", "answer": "60", "image": null, "query": "A sequence of real numbers $a_0, a_1, . . .$ is said to be good if the following three conditions hold.\n(i) The value of $a_0$ is a positive integer.\n(ii) For each non-negative integer $i$ we have $a_{i+1} = 2a_i + 1 $ or $a_{i+1} =\\frac{a_i}{a_i + 2} $\n(iii) There exists a positive integer $k$ such that $a_k = 2014$.\n\nFind the smallest positive integer $n$ such that there exists a good sequence $a_0, a_1, . . .$ of real numbers with the property that $a_n = 2014$."}, {"pid": "258", "question": "Find all positive integers $a$ such that there exists a set $X$ of $6$ integers satisfying the following conditions: for every $k=1,2,\\ldots ,36$ there exist $x,y\\in X$ such that $ax+y-k$ is divisible by $37$.", "answer": "6, 31", "image": null, "query": "Find all positive integers $a$ such that there exists a set $X$ of $6$ integers satisfying the following conditions: for every $k=1,2,\\ldots ,36$ there exist $x,y\\in X$ such that $ax+y-k$ is divisible by $37$."}, {"pid": "259", "question": "A plane $P$ slices through a cube of volume 1 with a cross-section in the shape of a regular hexagon. This cube also has an inscribed sphere, whose intersection with $P$ is a circle. What is the area of the region inside the regular hexagon but outside the circle?", "answer": "(3 \\sqrt{3}-\\pi) / 4", "image": null, "query": "A plane $P$ slices through a cube of volume 1 with a cross-section in the shape of a regular hexagon. This cube also has an inscribed sphere, whose intersection with $P$ is a circle. What is the area of the region inside the regular hexagon but outside the circle?"}, {"pid": "260", "question": "<PERSON> has a $3 \\times 4$ grid of dots. She colors each dot red, blue, or dark gray. Compute the number of ways <PERSON> can color the grid such that there is no rectangle whose sides are parallel to the grid lines and whose vertices all have the same color.", "answer": "284688", "image": null, "query": "<PERSON> has a $3 \\times 4$ grid of dots. She colors each dot red, blue, or dark gray. Compute the number of ways <PERSON> can color the grid such that there is no rectangle whose sides are parallel to the grid lines and whose vertices all have the same color."}, {"pid": "261", "question": "<PERSON> is once again very bored in class. On a whim, she chooses three primes $p, q, r$ independently and uniformly at random from the set of primes of at most 30. She then calculates the roots of $p x^{2}+q x+r$. What is the probability that at least one of her roots is an integer?", "answer": "\\frac{3}{200}", "image": null, "query": "<PERSON> is once again very bored in class. On a whim, she chooses three primes $p, q, r$ independently and uniformly at random from the set of primes of at most 30. She then calculates the roots of $p x^{2}+q x+r$. What is the probability that at least one of her roots is an integer?"}, {"pid": "262", "question": "<PERSON><PERSON> the frog currently sits at $(0,0)$ in the coordinate plane. If <PERSON><PERSON> is at $(x, y)$, either he can walk to any of $(x, y+1),(x+1, y)$, or $(x+1, y+1)$, or he can jump to any of $(x, y+2),(x+2, y)$ or $(x+1, y+1)$. Walking and jumping from $(x, y)$ to $(x+1, y+1)$ are considered distinct actions. Compute the number of ways <PERSON><PERSON> can reach $(6,8)$.", "answer": "1831830", "image": null, "query": "<PERSON><PERSON> the frog currently sits at $(0,0)$ in the coordinate plane. If <PERSON><PERSON> is at $(x, y)$, either he can walk to any of $(x, y+1),(x+1, y)$, or $(x+1, y+1)$, or he can jump to any of $(x, y+2),(x+2, y)$ or $(x+1, y+1)$. Walking and jumping from $(x, y)$ to $(x+1, y+1)$ are considered distinct actions. Compute the number of ways <PERSON><PERSON> can reach $(6,8)$."}, {"pid": "263", "question": "A given finite number of lines in the plane, no two of which are parallel and no three of which are concurrent, divide the plane into finite and infinite regions. In each finite region we write $1$ or $-1$. In one operation, we can choose any triangle made of three of the lines (which may be cut by other lines in the collection) and multiply by $-1$ each of the numbers in the triangle. Determine if it is always possible to obtain $1$ in all the finite regions by successively applying this operation, regardless of the initial distribution of  $1$s and $-1$s.", "answer": "\\text{No}", "image": null, "query": "A given finite number of lines in the plane, no two of which are parallel and no three of which are concurrent, divide the plane into finite and infinite regions. In each finite region we write $1$ or $-1$. In one operation, we can choose any triangle made of three of the lines (which may be cut by other lines in the collection) and multiply by $-1$ each of the numbers in the triangle. Determine if it is always possible to obtain $1$ in all the finite regions by successively applying this operation, regardless of the initial distribution of  $1$s and $-1$s."}, {"pid": "264", "question": "The integers $1,2,4,5,6,9,10,11,13$ are to be placed in the circles and squares below with one number in each shape. Each integer must be used exactly once and the integer in each circle must be equal to the sum of the integers in the two neighbouring squares. If the integer $x$ is placed in the leftmost square and the integer $y$ is placed in the rightmost square, what is the largest possible value of $x+y$?", "answer": "20", "image": null, "query": "The integers $1,2,4,5,6,9,10,11,13$ are to be placed in the circles and squares below with one number in each shape. Each integer must be used exactly once and the integer in each circle must be equal to the sum of the integers in the two neighbouring squares. If the integer $x$ is placed in the leftmost square and the integer $y$ is placed in the rightmost square, what is the largest possible value of $x+y$?"}, {"pid": "265", "question": "Every positive integer greater than $1000$ is colored in red or blue, such that the product of any two distinct red numbers is blue. Is it possible to happen that no two blue numbers have difference $1$?", "answer": "\\text{No}", "image": null, "query": "Every positive integer greater than $1000$ is colored in red or blue, such that the product of any two distinct red numbers is blue. Is it possible to happen that no two blue numbers have difference $1$?"}, {"pid": "266", "question": "If the system of equations $$\\begin{aligned} & |x+y|=99 \\\\ & |x-y|=c \\end{aligned}$$ has exactly two real solutions $(x, y)$, find the value of $c$.", "answer": "0", "image": null, "query": "If the system of equations $$\\begin{aligned} & |x+y|=99 \\\\ & |x-y|=c \\end{aligned}$$ has exactly two real solutions $(x, y)$, find the value of $c$."}, {"pid": "267", "question": "Evaluate \\[ \\sum_{k=1}^\\infty \\frac{(-1)^{k-1}}{k} \\sum_{n=0}^\\infty \\frac{1}{k2^n + 1}. \\]", "answer": "1", "image": null, "query": "Evaluate \\[ \\sum_{k=1}^\\infty \\frac{(-1)^{k-1}}{k} \\sum_{n=0}^\\infty \\frac{1}{k2^n + 1}. \\]"}, {"pid": "268", "question": "For how many positive integers $k$ do the lines with equations $9x+4y=600$ and $kx-4y=24$ intersect at a point whose coordinates are positive integers?", "answer": "7", "image": null, "query": "For how many positive integers $k$ do the lines with equations $9x+4y=600$ and $kx-4y=24$ intersect at a point whose coordinates are positive integers?"}, {"pid": "269", "question": "Give the set of all positive integers $n$ such that $\\varphi(n)=2002^{2}-1$.", "answer": "\\varnothing", "image": null, "query": "Give the set of all positive integers $n$ such that $\\varphi(n)=2002^{2}-1$."}, {"pid": "270", "question": "<PERSON> is learning how to write the letter C. She has 6 differently-colored crayons, and wants to write Cc Cc Cc Cc Cc. In how many ways can she write the ten Cs, in such a way that each upper case C is a different color, each lower case C is a different color, and in each pair the upper case C and lower case C are different colors?", "answer": "222480", "image": null, "query": "<PERSON> is learning how to write the letter C. She has 6 differently-colored crayons, and wants to write Cc Cc Cc Cc Cc. In how many ways can she write the ten Cs, in such a way that each upper case C is a different color, each lower case C is a different color, and in each pair the upper case C and lower case C are different colors?"}, {"pid": "271", "question": "What is the last digit of $1^{1}+2^{2}+3^{3}+\\cdots+100^{100}$?", "answer": "0", "image": null, "query": "What is the last digit of $1^{1}+2^{2}+3^{3}+\\cdots+100^{100}$?"}, {"pid": "272", "question": "The Dyslexian alphabet consists of consonants and vowels. It so happens that a finite sequence of letters is a word in Dyslexian precisely if it alternates between consonants and vowels (it may begin with either). There are 4800 five-letter words in Dyslexian. How many letters are in the alphabet?", "answer": "12", "image": null, "query": "The Dyslexian alphabet consists of consonants and vowels. It so happens that a finite sequence of letters is a word in Dyslexian precisely if it alternates between consonants and vowels (it may begin with either). There are 4800 five-letter words in Dyslexian. How many letters are in the alphabet?"}, {"pid": "273", "question": "If $a, b, c>0$, what is the smallest possible value of $\\left\\lfloor\\frac{a+b}{c}\\right\\rfloor+\\left\\lfloor\\frac{b+c}{a}\\right\\rfloor+\\left\\lfloor\\frac{c+a}{b}\\right\\rfloor$? (Note that $\\lfloor x\\rfloor$ denotes the greatest integer less than or equal to $x$.)", "answer": "4", "image": null, "query": "If $a, b, c>0$, what is the smallest possible value of $\\left\\lfloor\\frac{a+b}{c}\\right\\rfloor+\\left\\lfloor\\frac{b+c}{a}\\right\\rfloor+\\left\\lfloor\\frac{c+a}{b}\\right\\rfloor$? (Note that $\\lfloor x\\rfloor$ denotes the greatest integer less than or equal to $x$.)"}, {"pid": "274", "question": "What is the remainder when $2^{2001}$ is divided by $2^{7}-1$ ?", "answer": "64", "image": null, "query": "What is the remainder when $2^{2001}$ is divided by $2^{7}-1$ ?"}, {"pid": "275", "question": "What is the area of rectangle \\( PQRS \\) if the perimeter of rectangle \\( TVWY \\) is 60?", "answer": "600", "image": null, "query": "What is the area of rectangle \\( PQRS \\) if the perimeter of rectangle \\( TVWY \\) is 60?"}, {"pid": "276", "question": "Let $P$ be a polynomial such that $P(x)=P(0)+P(1) x+P(2) x^{2}$ and $P(-1)=1$. Compute $P(3)$.", "answer": "5", "image": null, "query": "Let $P$ be a polynomial such that $P(x)=P(0)+P(1) x+P(2) x^{2}$ and $P(-1)=1$. Compute $P(3)$."}, {"pid": "277", "question": "We consider dissections of regular $n$-gons into $n - 2$ triangles by $n - 3$ diagonals which do not intersect inside the $n$-gon. A [i]bicoloured triangulation[/i] is such a dissection of an $n$-gon in which each triangle is coloured black or white and any two triangles which share an edge have different colours. We call a positive integer $n \\ge 4$ [i]triangulable[/i] if every regular $n$-gon has a bicoloured triangulation such that for each vertex $A$ of the $n$-gon the number of black triangles of which $A$ is a vertex is greater than the number of white triangles of which $A$ is a vertex.\n\nFind all triangulable numbers.", "answer": "3\\mid n", "image": null, "query": "We consider dissections of regular $n$-gons into $n - 2$ triangles by $n - 3$ diagonals which do not intersect inside the $n$-gon. A [i]bicoloured triangulation[/i] is such a dissection of an $n$-gon in which each triangle is coloured black or white and any two triangles which share an edge have different colours. We call a positive integer $n \\ge 4$ [i]triangulable[/i] if every regular $n$-gon has a bicoloured triangulation such that for each vertex $A$ of the $n$-gon the number of black triangles of which $A$ is a vertex is greater than the number of white triangles of which $A$ is a vertex.\n\nFind all triangulable numbers."}, {"pid": "278", "question": "Find all functions $f : \\mathbb{N}\\rightarrow{\\mathbb{N}}$ such that for all positive integers $m$ and $n$ the number $f(m)+n-m$ is divisible by $f(n)$.", "answer": "$\\boxed{f(n)=n+c},\\boxed{f(n)\\equiv 1},\\boxed{f(even)=1, f(odd)=2},\\boxed{f(odd)=1,f(even)=2}$", "image": null, "query": "Find all functions $f : \\mathbb{N}\\rightarrow{\\mathbb{N}}$ such that for all positive integers $m$ and $n$ the number $f(m)+n-m$ is divisible by $f(n)$."}, {"pid": "279", "question": "Consider a cube $A B C D E F G H$, where $A B C D$ and $E F G H$ are faces, and segments $A E, B F, C G, D H$ are edges of the cube. Let $P$ be the center of face $E F G H$, and let $O$ be the center of the cube. Given that $A G=1$, determine the area of triangle $A O P$.", "answer": "$\\frac{\\sqrt{2}}{24}$", "image": null, "query": "Consider a cube $A B C D E F G H$, where $A B C D$ and $E F G H$ are faces, and segments $A E, B F, C G, D H$ are edges of the cube. Let $P$ be the center of face $E F G H$, and let $O$ be the center of the cube. Given that $A G=1$, determine the area of triangle $A O P$."}, {"pid": "280", "question": "Find all functions $f:\\mathbb{R}\\rightarrow\\mathbb{R}$ such that $$f(x^2y)=f(xy)+yf(f(x)+y)$$ for all real numbers $x$ and $y$.", "answer": "f(x) = 0", "image": null, "query": "Find all functions $f:\\mathbb{R}\\rightarrow\\mathbb{R}$ such that $$f(x^2y)=f(xy)+yf(f(x)+y)$$ for all real numbers $x$ and $y$."}, {"pid": "281", "question": "How many sequences of ten binary digits are there in which neither two zeroes nor three ones ever appear in a row?", "answer": "28", "image": null, "query": "How many sequences of ten binary digits are there in which neither two zeroes nor three ones ever appear in a row?"}, {"pid": "282", "question": "Which graph is linear with a slope of 0?", "answer": "Graph Q", "image": null, "query": "Which graph is linear with a slope of 0?"}, {"pid": "283", "question": "Consider functions $f : [0, 1] \\rightarrow \\mathbb{R}$ which satisfy\n  (i) for all in ,    (ii) ,    (iii) whenever , , and are all in .\nFind, with proof, the smallest constant $c$ such that\n$f(x) \\le cx$\nfor every function $f$ satisfying (i)-(iii) and every $x$ in $[0, 1]$ .", "answer": "The smallest constant \\( c \\) such that \\( f(x) \\le cx \\) for every function \\( f \\) satisfying the given conditions is \\( c = 2 \\).", "image": null, "query": "Consider functions $f : [0, 1] \\rightarrow \\mathbb{R}$ which satisfy\n  (i) for all in ,    (ii) ,    (iii) whenever , , and are all in .\nFind, with proof, the smallest constant $c$ such that\n$f(x) \\le cx$\nfor every function $f$ satisfying (i)-(iii) and every $x$ in $[0, 1]$ ."}, {"pid": "284", "question": "Begining at a vertex, an ant crawls between the vertices of a regular octahedron. After reaching a vertex, it randomly picks a neighboring vertex (sharing an edge) and walks to that vertex along the adjoining edge (with all possibilities equally likely.) What is the probability that after walking along 2006 edges, the ant returns to the vertex where it began?", "answer": "\\frac{2^{2005}+1}{3 \\cdot 2^{2006}}", "image": null, "query": "Begining at a vertex, an ant crawls between the vertices of a regular octahedron. After reaching a vertex, it randomly picks a neighboring vertex (sharing an edge) and walks to that vertex along the adjoining edge (with all possibilities equally likely.) What is the probability that after walking along 2006 edges, the ant returns to the vertex where it began?"}, {"pid": "285", "question": "If $3 \times n=6 \times 2$, what is the value of $n$?", "answer": "4", "image": null, "query": "If $3 \times n=6 \times 2$, what is the value of $n$?"}, {"pid": "286", "question": "Find all pairs of positive integers $m, n$ such that $9^{|m-n|}+3^{|m-n|}+1$ is divisible by $m$ and $n$ simultaneously.", "answer": "(1, 1) \\text{ and } (3, 3)", "image": null, "query": "Find all pairs of positive integers $m, n$ such that $9^{|m-n|}+3^{|m-n|}+1$ is divisible by $m$ and $n$ simultaneously."}, {"pid": "287", "question": "If $x$ and $y$ are positive real numbers with $\\frac{1}{x+y}=\\frac{1}{x}-\\frac{1}{y}$, what is the value of $\\left(\\frac{x}{y}+\\frac{y}{x}\\right)^{2}$?", "answer": "5", "image": null, "query": "If $x$ and $y$ are positive real numbers with $\\frac{1}{x+y}=\\frac{1}{x}-\\frac{1}{y}$, what is the value of $\\left(\\frac{x}{y}+\\frac{y}{x}\\right)^{2}$?"}, {"pid": "288", "question": "For a given positive integer $ k$ denote the square of the sum of its digits by $ f_1(k)$ and let $ f_{n\\plus{}1}(k) \\equal{} f_1(f_n(k)).$ Determine the value of $ f_{1991}(2^{1990}).$", "answer": "256", "image": null, "query": "For a given positive integer $ k$ denote the square of the sum of its digits by $ f_1(k)$ and let $ f_{n\\plus{}1}(k) \\equal{} f_1(f_n(k)).$ Determine the value of $ f_{1991}(2^{1990}).$"}, {"pid": "289", "question": "A [i]snake of length $k$[/i] is an animal which occupies an ordered $k$-tuple $(s_1, \\dots, s_k)$ of cells in a $n \\times n$ grid of square unit cells. These cells must be pairwise distinct, and $s_i$ and $s_{i+1}$ must share a side for $i = 1, \\dots, k-1$. If the snake is currently occupying $(s_1, \\dots, s_k)$ and $s$ is an unoccupied cell sharing a side with $s_1$, the snake can [i]move[/i] to occupy $(s, s_1, \\dots, s_{k-1})$ instead. The snake has [i]turned around[/i] if it occupied $(s_1, s_2, \\dots, s_k)$ at the beginning, but after a finite number of moves occupies $(s_k, s_{k-1}, \\dots, s_1)$ instead.\n\nDetermine whether there exists an integer $n > 1$ such that: one can place some snake of length $0.9n^2$ in an $n \\times n$ grid which can turn around.\n\n[i]<PERSON>[/i]", "answer": "\\text{Yes}", "image": null, "query": "A [i]snake of length $k$[/i] is an animal which occupies an ordered $k$-tuple $(s_1, \\dots, s_k)$ of cells in a $n \\times n$ grid of square unit cells. These cells must be pairwise distinct, and $s_i$ and $s_{i+1}$ must share a side for $i = 1, \\dots, k-1$. If the snake is currently occupying $(s_1, \\dots, s_k)$ and $s$ is an unoccupied cell sharing a side with $s_1$, the snake can [i]move[/i] to occupy $(s, s_1, \\dots, s_{k-1})$ instead. The snake has [i]turned around[/i] if it occupied $(s_1, s_2, \\dots, s_k)$ at the beginning, but after a finite number of moves occupies $(s_k, s_{k-1}, \\dots, s_1)$ instead.\n\nDetermine whether there exists an integer $n > 1$ such that: one can place some snake of length $0.9n^2$ in an $n \\times n$ grid which can turn around.\n\n[i]<PERSON>[/i]"}, {"pid": "290", "question": "The equation $x^{2}+2 x=i$ has two complex solutions. Determine the product of their real parts.", "answer": "\\frac{1-\\sqrt{2}}{2}", "image": null, "query": "The equation $x^{2}+2 x=i$ has two complex solutions. Determine the product of their real parts."}, {"pid": "291", "question": "In this problem only, assume that $s_{1}=4$ and that exactly one board square, say square number $n$, is marked with an arrow. Determine all choices of $n$ that maximize the average distance in squares the first player will travel in his first two turns.", "answer": "n=4", "image": null, "query": "In this problem only, assume that $s_{1}=4$ and that exactly one board square, say square number $n$, is marked with an arrow. Determine all choices of $n$ that maximize the average distance in squares the first player will travel in his first two turns."}, {"pid": "292", "question": "Compute the value of \\(\\frac{\\cos 30.5^{\\circ}+\\cos 31.5^{\\circ}+\\ldots+\\cos 44.5^{\\circ}}{\\sin 30.5^{\\circ}+\\sin 31.5^{\\circ}+\\ldots+\\sin 44.5^{\\circ}}\\).", "answer": "(\\sqrt{2}-1)(\\sqrt{3}+\\sqrt{2})=2-\\sqrt{2}-\\sqrt{3}+\\sqrt{6}", "image": null, "query": "Compute the value of \\(\\frac{\\cos 30.5^{\\circ}+\\cos 31.5^{\\circ}+\\ldots+\\cos 44.5^{\\circ}}{\\sin 30.5^{\\circ}+\\sin 31.5^{\\circ}+\\ldots+\\sin 44.5^{\\circ}}\\)."}, {"pid": "293", "question": "Does there exist a field such that its multiplicative group is isomorphic to its additive group?", "answer": "There exist no such field.", "image": null, "query": "Does there exist a field such that its multiplicative group is isomorphic to its additive group?"}, {"pid": "294", "question": "A positive integer $n$ is a multiple of 7. The square root of $n$ is between 17 and 18. How many possible values of $n$ are there?", "answer": "5", "image": null, "query": "A positive integer $n$ is a multiple of 7. The square root of $n$ is between 17 and 18. How many possible values of $n$ are there?"}, {"pid": "295", "question": "<PERSON> has the number 1000 in her hands. When she puts the number $x$ in her left pocket, the number changes to $x+1$. When she puts the number $x$ in her right pocket, the number changes to $x^{-1}$. Each minute, she flips a fair coin. If it lands heads, she puts the number into her left pocket, and if it lands tails, she puts it into her right pocket. She then takes the new number out of her pocket. If the expected value of the number in <PERSON>'s hands after eight minutes is $E$, then compute $\\left\\lfloor\\frac{E}{10}\\right\\rfloor$.", "answer": "13", "image": null, "query": "<PERSON> has the number 1000 in her hands. When she puts the number $x$ in her left pocket, the number changes to $x+1$. When she puts the number $x$ in her right pocket, the number changes to $x^{-1}$. Each minute, she flips a fair coin. If it lands heads, she puts the number into her left pocket, and if it lands tails, she puts it into her right pocket. She then takes the new number out of her pocket. If the expected value of the number in <PERSON>'s hands after eight minutes is $E$, then compute $\\left\\lfloor\\frac{E}{10}\\right\\rfloor$."}, {"pid": "296", "question": "Find the smallest $n$ such that $n$! ends in 290 zeroes.", "answer": "1170", "image": null, "query": "Find the smallest $n$ such that $n$! ends in 290 zeroes."}, {"pid": "297", "question": "Let $z$ be a non-real complex number with $z^{23}=1$. Compute $$ \\sum_{k=0}^{22} \\frac{1}{1+z^{k}+z^{2 k}} $$", "answer": "46 / 3", "image": null, "query": "Let $z$ be a non-real complex number with $z^{23}=1$. Compute $$ \\sum_{k=0}^{22} \\frac{1}{1+z^{k}+z^{2 k}} $$"}, {"pid": "298", "question": "At the start of this month, <PERSON><PERSON> and <PERSON><PERSON> each had 100 coins. For <PERSON><PERSON>, this was $25 \\%$ more coins than she had at the start of last month. For <PERSON><PERSON>, this was $20 \\%$ fewer coins than he had at the start of last month. What was the total number of coins that they had at the start of last month?", "answer": "205", "image": null, "query": "At the start of this month, <PERSON><PERSON> and <PERSON><PERSON> each had 100 coins. For <PERSON><PERSON>, this was $25 \\%$ more coins than she had at the start of last month. For <PERSON><PERSON>, this was $20 \\%$ fewer coins than he had at the start of last month. What was the total number of coins that they had at the start of last month?"}, {"pid": "299", "question": "Let $A B C D$ be a square of side length 5, and let $E$ be the midpoint of side $A B$. Let $P$ and $Q$ be the feet of perpendiculars from $B$ and $D$ to $C E$, respectively, and let $R$ be the foot of the perpendicular from $A$ to $D Q$. The segments $C E, B P, D Q$, and $A R$ partition $A B C D$ into five regions. What is the median of the areas of these five regions?", "answer": "5", "image": null, "query": "Let $A B C D$ be a square of side length 5, and let $E$ be the midpoint of side $A B$. Let $P$ and $Q$ be the feet of perpendiculars from $B$ and $D$ to $C E$, respectively, and let $R$ be the foot of the perpendicular from $A$ to $D Q$. The segments $C E, B P, D Q$, and $A R$ partition $A B C D$ into five regions. What is the median of the areas of these five regions?"}]