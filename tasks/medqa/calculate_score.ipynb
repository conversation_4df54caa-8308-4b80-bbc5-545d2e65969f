{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import json\n", "from tqdm import tqdm\n", "from glob import glob\n", "from octotools.engine.openai import ChatOpenAI\n", "\n", "exp_code = 'YOUR_EXPERIMENT_CODE'\n", "\n", "local_llm_engine = ChatOpenAI(model_string=\"gpt-3.5-turbo\", is_multimodal=False, enable_cache=True)\n", "\n", "demo_prompt = \"\"\"\n", "You are looking at answers to multiple choice questions. You will be given a question and a response. Each question will have 4 choices (A, B, C, and D). Find the choice chosen by the response. YOU ARE NOT ANSWERING THE QUESTION YOURSELF. You are simply extracting the choice from the provided response. The response likely is organized in sections and the choice is generally located in the conclusion or final answer section. RESPOND ONLY WITH THE NUMBER CORRESPONDING TO THE CHOICE THAT THE ANSWER MADE. If the response did not choose an answer, respond with '-'.\n", "\"\"\"\n", "\n", "def create_test_prompt(demo_prompt, query, response):\n", "    demo_prompt = demo_prompt.strip()\n", "    test_prompt = f\"Question:\\n{query}\\n\\nResponse:\\n{response}\"\n", "    full_prompt = f\"{demo_prompt}\\n\\n{test_prompt}\\n\\nChoice:\"\n", "    return full_prompt\n", "\n", "def safe_equal(prediction, answer):\n", "    \"\"\"\n", "    Check if the prediction is equal to the answer, even if they are of different types\n", "    \"\"\"\n", "    try:\n", "        if prediction == answer:\n", "            return True\n", "        return False\n", "    except Exception as e:\n", "        print(e)\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_dir = f'../results/{exp_code}'\n", "outputs = glob(f'{results_dir}/*.json')\n", "\n", "use_col = 'direct_output'\n", "\n", "items = []\n", "for output in tqdm(outputs):\n", "    item_id = os.path.splitext(os.path.basename(output))[0]\n", "    with open(output, 'r') as f:\n", "        out = json.load(f)\n", "\n", "    output = out[use_col]\n", "    question = out.get('query', out.get('question', ''))\n", "    if len(output.replace('.', '').strip()) == 1:\n", "        extraction = output.replace('.', '').strip()\n", "    else:\n", "        full_prompt = create_test_prompt(demo_prompt, question, output)\n", "        extraction = local_llm_engine(full_prompt)\n", "\n", "    item = {\n", "        'id': item_id,\n", "        'question': question,\n", "        'correct': out['answer'],\n", "        'extracted_choice': extraction,\n", "        'response': output,\n", "        'use_col': use_col\n", "    }\n", "    items.append(item)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(items)\n", "\n", "def process_choice(x): \n", "    x = x.split('.')[0]\n", "    x = x.replace('.', '')\n", "    if x.isnumeric():\n", "        return chr(ord('A') + int(x) - 1)\n", "    return x\n", "\n", "df.apply(lambda row: safe_equal(process_choice(row['extracted_choice']), row['correct']), axis=1).mean()"]}], "metadata": {"kernelspec": {"display_name": "opentools-path", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}