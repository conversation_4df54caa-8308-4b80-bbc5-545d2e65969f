[{"pid": "0", "question": [1, 1, 1, 8], "answer": ["(1+1+1)×8"], "image": null, "query": "Using the numbers [1, 1, 1, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "1", "question": [1, 1, 1, 11], "answer": ["(11+1)×(1+1)"], "image": null, "query": "Using the numbers [1, 1, 1, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "2", "question": [1, 1, 1, 12], "answer": ["(1+1)×12×1"], "image": null, "query": "Using the numbers [1, 1, 1, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "3", "question": [1, 1, 1, 13], "answer": ["(13-1)×(1+1)"], "image": null, "query": "Using the numbers [1, 1, 1, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "4", "question": [1, 1, 2, 6], "answer": ["(1+1)×6×2", "(2+1+1)×6"], "image": null, "query": "Using the numbers [1, 1, 2, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "5", "question": [1, 1, 2, 7], "answer": ["(7+1)×(2+1)"], "image": null, "query": "Using the numbers [1, 1, 2, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "6", "question": [1, 1, 2, 8], "answer": ["(2+1)×8×1"], "image": null, "query": "Using the numbers [1, 1, 2, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "7", "question": [1, 1, 2, 9], "answer": ["(9-1)×(2+1)"], "image": null, "query": "Using the numbers [1, 1, 2, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "8", "question": [1, 1, 2, 10], "answer": ["(10+2)×(1+1)", "(10+1+1)×2"], "image": null, "query": "Using the numbers [1, 1, 2, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "9", "question": [1, 1, 2, 11], "answer": ["11×2+1+1", "(1+1)×11+2", "(11+1)×2×1"], "image": null, "query": "Using the numbers [1, 1, 2, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "10", "question": [1, 1, 2, 12], "answer": ["12×2+1-1", "12/(1-1/2)"], "image": null, "query": "Using the numbers [1, 1, 2, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "11", "question": [1, 1, 2, 13], "answer": ["13×2-1-1", "(1+1)×13-2", "(13-1)×2×1"], "image": null, "query": "Using the numbers [1, 1, 2, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "12", "question": [1, 1, 3, 4], "answer": ["(1+1)×4×3"], "image": null, "query": "Using the numbers [1, 1, 3, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "13", "question": [1, 1, 3, 5], "answer": ["(5+1)×(3+1)"], "image": null, "query": "Using the numbers [1, 1, 3, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "14", "question": [1, 1, 3, 6], "answer": ["(6+1+1)×3", "(3+1)×6×1"], "image": null, "query": "Using the numbers [1, 1, 3, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "15", "question": [1, 1, 3, 7], "answer": ["(7-1)×(3+1)", "(7+1)×3×1"], "image": null, "query": "Using the numbers [1, 1, 3, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "16", "question": [1, 1, 3, 8], "answer": ["8×3+1-1"], "image": null, "query": "Using the numbers [1, 1, 3, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "17", "question": [1, 1, 3, 9], "answer": ["(9+3)×(1+1)", "(9-1)×3×1"], "image": null, "query": "Using the numbers [1, 1, 3, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "18", "question": [1, 1, 3, 10], "answer": ["(10-1-1)×3"], "image": null, "query": "Using the numbers [1, 1, 3, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "19", "question": [1, 1, 3, 11], "answer": ["(11+1)×(3-1)"], "image": null, "query": "Using the numbers [1, 1, 3, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "20", "question": [1, 1, 3, 12], "answer": ["(3-1)×12×1"], "image": null, "query": "Using the numbers [1, 1, 3, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "21", "question": [1, 1, 3, 13], "answer": ["(13-1)×(3-1)"], "image": null, "query": "Using the numbers [1, 1, 3, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "22", "question": [1, 1, 4, 4], "answer": ["(4+1+1)×4"], "image": null, "query": "Using the numbers [1, 1, 4, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "23", "question": [1, 1, 4, 5], "answer": ["(4+1)×5-1", "(5+1)×4×1"], "image": null, "query": "Using the numbers [1, 1, 4, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "24", "question": [1, 1, 4, 6], "answer": ["6×4+1-1"], "image": null, "query": "Using the numbers [1, 1, 4, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "25", "question": [1, 1, 4, 7], "answer": ["(7-1)×4×1", "(7+1)×(4-1)"], "image": null, "query": "Using the numbers [1, 1, 4, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "26", "question": [1, 1, 4, 8], "answer": ["(8+4)×(1+1)", "(8-1-1)×4", "(4-1)×8×1"], "image": null, "query": "Using the numbers [1, 1, 4, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "27", "question": [1, 1, 4, 9], "answer": ["(9-1)×(4-1)"], "image": null, "query": "Using the numbers [1, 1, 4, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "28", "question": [1, 1, 4, 10], "answer": ["(1+1)×10+4"], "image": null, "query": "Using the numbers [1, 1, 4, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "29", "question": [1, 1, 4, 12], "answer": ["12×4/(1+1)", "(4-1-1)×12"], "image": null, "query": "Using the numbers [1, 1, 4, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "30", "question": [1, 1, 5, 5], "answer": ["(5+1)×(5-1)", "(5×5-1)×1"], "image": null, "query": "Using the numbers [1, 1, 5, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "31", "question": [1, 1, 5, 6], "answer": ["(5-1)×6×1", "(6-1)×5-1"], "image": null, "query": "Using the numbers [1, 1, 5, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "32", "question": [1, 1, 5, 7], "answer": ["(7+5)×(1+1)", "(7-1)×(5-1)"], "image": null, "query": "Using the numbers [1, 1, 5, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "33", "question": [1, 1, 5, 8], "answer": ["(5-1-1)×8"], "image": null, "query": "Using the numbers [1, 1, 5, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "34", "question": [1, 1, 6, 6], "answer": ["(6+6)×(1+1)", "(6-1-1)×6"], "image": null, "query": "Using the numbers [1, 1, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "35", "question": [1, 1, 6, 8], "answer": ["8×6/(1+1)"], "image": null, "query": "Using the numbers [1, 1, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "36", "question": [1, 1, 6, 9], "answer": ["(1+1)×9+6"], "image": null, "query": "Using the numbers [1, 1, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "37", "question": [1, 1, 6, 12], "answer": ["(1+1)×6+12"], "image": null, "query": "Using the numbers [1, 1, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "38", "question": [1, 1, 7, 10], "answer": ["(1+1)×7+10"], "image": null, "query": "Using the numbers [1, 1, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "39", "question": [1, 1, 8, 8], "answer": ["(1+1)×8+8"], "image": null, "query": "Using the numbers [1, 1, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "40", "question": [1, 1, 9, 13], "answer": ["13+9+1+1"], "image": null, "query": "Using the numbers [1, 1, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "41", "question": [1, 1, 10, 12], "answer": ["12+10+1+1"], "image": null, "query": "Using the numbers [1, 1, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "42", "question": [1, 1, 10, 13], "answer": ["(13+10+1)×1"], "image": null, "query": "Using the numbers [1, 1, 10, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "43", "question": [1, 1, 11, 11], "answer": ["11+11+1+1"], "image": null, "query": "Using the numbers [1, 1, 11, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "44", "question": [1, 1, 11, 12], "answer": ["(12+11+1)×1"], "image": null, "query": "Using the numbers [1, 1, 11, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "45", "question": [1, 1, 11, 13], "answer": ["13+11+1-1"], "image": null, "query": "Using the numbers [1, 1, 11, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "46", "question": [1, 1, 12, 12], "answer": ["12+12+1-1"], "image": null, "query": "Using the numbers [1, 1, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "47", "question": [1, 1, 12, 13], "answer": ["(13+12-1)×1"], "image": null, "query": "Using the numbers [1, 1, 12, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "48", "question": [1, 1, 13, 13], "answer": ["13+13-1-1"], "image": null, "query": "Using the numbers [1, 1, 13, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "49", "question": [1, 2, 2, 4], "answer": ["(2+1)×4×2"], "image": null, "query": "Using the numbers [1, 2, 2, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "50", "question": [1, 2, 2, 5], "answer": ["(5+1)×(2+2)", "(5+1)×2×2"], "image": null, "query": "Using the numbers [1, 2, 2, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "51", "question": [1, 2, 2, 6], "answer": ["(6+2)×(2+1)", "6×2×2×1", "(2+2)×6×1"], "image": null, "query": "Using the numbers [1, 2, 2, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "52", "question": [1, 2, 2, 7], "answer": ["(7-1)×(2+2)", "(7-1)×2×2"], "image": null, "query": "Using the numbers [1, 2, 2, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "53", "question": [1, 2, 2, 8], "answer": ["(2+2-1)×8", "(2×2-1)×8"], "image": null, "query": "Using the numbers [1, 2, 2, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "54", "question": [1, 2, 2, 9], "answer": ["(9+2+1)×2"], "image": null, "query": "Using the numbers [1, 2, 2, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "55", "question": [1, 2, 2, 10], "answer": ["(10-2)×(2+1)", "(10+2)×2×1", "(10+1)×2+2"], "image": null, "query": "Using the numbers [1, 2, 2, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "56", "question": [1, 2, 2, 11], "answer": ["(11+2-1)×2", "(11×2+2)×1"], "image": null, "query": "Using the numbers [1, 2, 2, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "57", "question": [1, 2, 2, 12], "answer": ["(2-1)×12×2", "(12+1)×2-2", "(12-1)×2+2", "(2/2+1)×12"], "image": null, "query": "Using the numbers [1, 2, 2, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "58", "question": [1, 2, 2, 13], "answer": ["(13+1-2)×2", "(13×2-2)×1"], "image": null, "query": "Using the numbers [1, 2, 2, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "59", "question": [1, 2, 3, 3], "answer": ["(3+1)×3×2"], "image": null, "query": "Using the numbers [1, 2, 3, 3], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "60", "question": [1, 2, 3, 4], "answer": ["(3+2+1)×4", "4×3×2×1", "(4+2)×(3+1)"], "image": null, "query": "Using the numbers [1, 2, 3, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "61", "question": [1, 2, 3, 5], "answer": ["(5+3)×(2+1)", "(5+2+1)×3", "(5-1)×3×2", "(3+2)×5-1"], "image": null, "query": "Using the numbers [1, 2, 3, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "62", "question": [1, 2, 3, 6], "answer": ["(3+2-1)×6", "(6+2)×3×1", "(3-1)×6×2"], "image": null, "query": "Using the numbers [1, 2, 3, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "63", "question": [1, 2, 3, 7], "answer": ["7×3+2+1", "(2+1)×7+3", "(7+2-1)×3"], "image": null, "query": "Using the numbers [1, 2, 3, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "64", "question": [1, 2, 3, 8], "answer": ["(2-1)×8×3", "(8-2)×(3+1)", "(8+3+1)×2", "8/(1-2/3)"], "image": null, "query": "Using the numbers [1, 2, 3, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "65", "question": [1, 2, 3, 9], "answer": ["9×3-2-1", "(2+1)×9-3", "(9+3)×2×1", "(9+1-2)×3"], "image": null, "query": "Using the numbers [1, 2, 3, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "66", "question": [1, 2, 3, 10], "answer": ["10×2+3+1", "(10-2)×3×1", "(10+3-1)×2", "(10+2)×(3-1)"], "image": null, "query": "Using the numbers [1, 2, 3, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "67", "question": [1, 2, 3, 11], "answer": ["(11-3)×(2+1)", "(11-2-1)×3", "11×2+3-1", "(3-1)×11+2"], "image": null, "query": "Using the numbers [1, 2, 3, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "68", "question": [1, 2, 3, 12], "answer": ["(3+1-2)×12", "(3+1)×12/2", "12/(3/2-1)"], "image": null, "query": "Using the numbers [1, 2, 3, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "69", "question": [1, 2, 3, 13], "answer": ["13×2+1-3", "(3-1)×13-2"], "image": null, "query": "Using the numbers [1, 2, 3, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "70", "question": [1, 2, 4, 4], "answer": ["(4+4)×(2+1)", "(4+2)×4×1", "(4-1)×4×2"], "image": null, "query": "Using the numbers [1, 2, 4, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "71", "question": [1, 2, 4, 5], "answer": ["(5+2-1)×4", "(4+2)×(5-1)"], "image": null, "query": "Using the numbers [1, 2, 4, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "72", "question": [1, 2, 4, 6], "answer": ["(2-1)×6×4", "(6+2)×(4-1)"], "image": null, "query": "Using the numbers [1, 2, 4, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "73", "question": [1, 2, 4, 7], "answer": ["(7+1-2)×4", "(7+4+1)×2"], "image": null, "query": "Using the numbers [1, 2, 4, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "74", "question": [1, 2, 4, 8], "answer": ["(8+4)×2×1", "(4+1-2)×8", "(8-2)×4×1", "(4/2+1)×8"], "image": null, "query": "Using the numbers [1, 2, 4, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "75", "question": [1, 2, 4, 9], "answer": ["(9-2-1)×4", "(9+4-1)×2", "(9+1)×2+4"], "image": null, "query": "Using the numbers [1, 2, 4, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "76", "question": [1, 2, 4, 10], "answer": ["(10-2)×(4-1)", "(10×2+4)×1", "(10/2+1)×4"], "image": null, "query": "Using the numbers [1, 2, 4, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "77", "question": [1, 2, 4, 11], "answer": ["(11+1)×(4-2)", "(11+1)×4/2", "(11-1)×2+4"], "image": null, "query": "Using the numbers [1, 2, 4, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "78", "question": [1, 2, 4, 12], "answer": ["(12-4)×(2+1)", "(2+1)×4+12", "12×4×1/2", "(4-2)×12×1", "12/(1-2/4)"], "image": null, "query": "Using the numbers [1, 2, 4, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "79", "question": [1, 2, 4, 13], "answer": ["(13+1)×2-4", "(13-1)×(4-2)", "(13-1)×4/2"], "image": null, "query": "Using the numbers [1, 2, 4, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "80", "question": [1, 2, 5, 5], "answer": ["5×5+1-2"], "image": null, "query": "Using the numbers [1, 2, 5, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "81", "question": [1, 2, 5, 6], "answer": ["(5+1-2)×6", "(5+1)×(6-2)", "(6+5+1)×2"], "image": null, "query": "Using the numbers [1, 2, 5, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "82", "question": [1, 2, 5, 7], "answer": ["(7+5)×2×1", "(7+1)×(5-2)", "(7-2)×5-1"], "image": null, "query": "Using the numbers [1, 2, 5, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "83", "question": [1, 2, 5, 8], "answer": ["(5+1)×8/2", "(8+5-1)×2", "(8-2)×(5-1)", "(5-2)×8×1"], "image": null, "query": "Using the numbers [1, 2, 5, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "84", "question": [1, 2, 5, 9], "answer": ["(2+1)×5+9", "9×2+5+1", "(9-1)×(5-2)"], "image": null, "query": "Using the numbers [1, 2, 5, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "85", "question": [1, 2, 5, 10], "answer": ["10×2+5-1", "10×5/2-1"], "image": null, "query": "Using the numbers [1, 2, 5, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "86", "question": [1, 2, 5, 12], "answer": ["(5-2-1)×12", "(5+1)×2+12", "(5-1)×12/2"], "image": null, "query": "Using the numbers [1, 2, 5, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "87", "question": [1, 2, 5, 13], "answer": ["(13-5)×(2+1)", "5×2+13+1"], "image": null, "query": "Using the numbers [1, 2, 5, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "88", "question": [1, 2, 6, 6], "answer": ["(2+1)×6+6", "(6+6)×2×1", "(6-2)×6×1", "(6/2+1)×6"], "image": null, "query": "Using the numbers [1, 2, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "89", "question": [1, 2, 6, 7], "answer": ["(7-2-1)×6", "(7+6-1)×2", "(7+1)×6/2", "(7-1)×(6-2)"], "image": null, "query": "Using the numbers [1, 2, 6, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "90", "question": [1, 2, 6, 8], "answer": ["(6-2-1)×8", "8×6×1/2", "(8+1)×2+6"], "image": null, "query": "Using the numbers [1, 2, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "91", "question": [1, 2, 6, 9], "answer": ["(9-1)×6/2", "(9×2+6)×1"], "image": null, "query": "Using the numbers [1, 2, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "92", "question": [1, 2, 6, 10], "answer": ["(2+1)×10-6", "(6+1)×2+10", "(10-1)×2+6", "(10/2-1)×6"], "image": null, "query": "Using the numbers [1, 2, 6, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "93", "question": [1, 2, 6, 11], "answer": ["6×2+11+1"], "image": null, "query": "Using the numbers [1, 2, 6, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "94", "question": [1, 2, 6, 12], "answer": ["12×6/(2+1)", "(6×2+12)×1", "(6/2-1)×12"], "image": null, "query": "Using the numbers [1, 2, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "95", "question": [1, 2, 6, 13], "answer": ["6×2+13-1"], "image": null, "query": "Using the numbers [1, 2, 6, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "96", "question": [1, 2, 7, 7], "answer": ["(7×7-1)/2"], "image": null, "query": "Using the numbers [1, 2, 7, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "97", "question": [1, 2, 7, 8], "answer": ["8×2+7+1", "(7+1)×2+8", "(7-1)×8/2"], "image": null, "query": "Using the numbers [1, 2, 7, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "98", "question": [1, 2, 7, 9], "answer": ["9×2+7-1", "7×2+9+1"], "image": null, "query": "Using the numbers [1, 2, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "99", "question": [1, 2, 7, 10], "answer": ["(7×2+10)×1"], "image": null, "query": "Using the numbers [1, 2, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "100", "question": [1, 2, 7, 11], "answer": ["7×2+11-1"], "image": null, "query": "Using the numbers [1, 2, 7, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "101", "question": [1, 2, 7, 12], "answer": ["(7-1)×2+12"], "image": null, "query": "Using the numbers [1, 2, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "102", "question": [1, 2, 8, 8], "answer": ["(8×2+8)×1", "(8/2-1)×8"], "image": null, "query": "Using the numbers [1, 2, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "103", "question": [1, 2, 8, 9], "answer": ["9×8/(2+1)", "8×2+9-1", "(9-1)×2+8"], "image": null, "query": "Using the numbers [1, 2, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "104", "question": [1, 2, 8, 10], "answer": ["(8-1)×2+10"], "image": null, "query": "Using the numbers [1, 2, 8, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "105", "question": [1, 2, 8, 13], "answer": ["13+8+2+1"], "image": null, "query": "Using the numbers [1, 2, 8, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "106", "question": [1, 2, 9, 11], "answer": ["(2+1)×11-9"], "image": null, "query": "Using the numbers [1, 2, 9, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "107", "question": [1, 2, 9, 12], "answer": ["12+9+2+1"], "image": null, "query": "Using the numbers [1, 2, 9, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "108", "question": [1, 2, 9, 13], "answer": ["(13+9+2)×1"], "image": null, "query": "Using the numbers [1, 2, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "109", "question": [1, 2, 10, 11], "answer": ["11+10+2+1"], "image": null, "query": "Using the numbers [1, 2, 10, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "110", "question": [1, 2, 10, 12], "answer": ["(12+10+2)×1"], "image": null, "query": "Using the numbers [1, 2, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "111", "question": [1, 2, 10, 13], "answer": ["13+10+2-1"], "image": null, "query": "Using the numbers [1, 2, 10, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "112", "question": [1, 2, 11, 11], "answer": ["(11+11+2)×1"], "image": null, "query": "Using the numbers [1, 2, 11, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "113", "question": [1, 2, 11, 12], "answer": ["12+11+2-1", "2/(1-11/12)"], "image": null, "query": "Using the numbers [1, 2, 11, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "114", "question": [1, 2, 11, 13], "answer": ["(13+11)×(2-1)"], "image": null, "query": "Using the numbers [1, 2, 11, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "115", "question": [1, 2, 12, 12], "answer": ["(2+1)×12-12", "(12+12)×(2-1)"], "image": null, "query": "Using the numbers [1, 2, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "116", "question": [1, 2, 12, 13], "answer": ["13+12+1-2", "2/(13/12-1)"], "image": null, "query": "Using the numbers [1, 2, 12, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "117", "question": [1, 2, 13, 13], "answer": ["(13+13-2)×1"], "image": null, "query": "Using the numbers [1, 2, 13, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "118", "question": [1, 3, 3, 3], "answer": ["(3+3)×(3+1)", "(3×3-1)×3"], "image": null, "query": "Using the numbers [1, 3, 3, 3], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "119", "question": [1, 3, 3, 4], "answer": ["(4+3+1)×3", "(3-1)×4×3", "(3+3)×4×1"], "image": null, "query": "Using the numbers [1, 3, 3, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "120", "question": [1, 3, 3, 5], "answer": ["(5+3)×3×1", "(3+3)×(5-1)"], "image": null, "query": "Using the numbers [1, 3, 3, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "121", "question": [1, 3, 3, 6], "answer": ["(6+3-1)×3", "(6+1)×3+3"], "image": null, "query": "Using the numbers [1, 3, 3, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "122", "question": [1, 3, 3, 7], "answer": ["(7×3+3)×1"], "image": null, "query": "Using the numbers [1, 3, 3, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "123", "question": [1, 3, 3, 8], "answer": ["(8+1)×3-3", "(8-1)×3+3"], "image": null, "query": "Using the numbers [1, 3, 3, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "124", "question": [1, 3, 3, 9], "answer": ["(9-3)×(3+1)", "(9+3)×(3-1)", "(3-1/3)×9", "(9×3-3)×1"], "image": null, "query": "Using the numbers [1, 3, 3, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "125", "question": [1, 3, 3, 10], "answer": ["(10+1-3)×3", "(10-1)×3-3"], "image": null, "query": "Using the numbers [1, 3, 3, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "126", "question": [1, 3, 3, 11], "answer": ["(11-3)×3×1"], "image": null, "query": "Using the numbers [1, 3, 3, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "127", "question": [1, 3, 3, 12], "answer": ["(3+1)×3+12", "(12-3-1)×3", "(3/3+1)×12"], "image": null, "query": "Using the numbers [1, 3, 3, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "128", "question": [1, 3, 4, 4], "answer": ["(4+4)×3×1", "(4+3-1)×4"], "image": null, "query": "Using the numbers [1, 3, 4, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "129", "question": [1, 3, 4, 5], "answer": ["5×4+3+1", "(3+1)×5+4", "(5+4-1)×3", "(5+3)×(4-1)"], "image": null, "query": "Using the numbers [1, 3, 4, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "130", "question": [1, 3, 4, 6], "answer": ["6/(1-3/4)"], "image": null, "query": "Using the numbers [1, 3, 4, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "131", "question": [1, 3, 4, 7], "answer": ["7×4-3-1", "(3+1)×7-4", "7×3+4-1", "(4-1)×7+3"], "image": null, "query": "Using the numbers [1, 3, 4, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "132", "question": [1, 3, 4, 8], "answer": ["(3+1)×4+8", "(8+1-3)×4", "(8+4)×(3-1)", "8/(4/3-1)"], "image": null, "query": "Using the numbers [1, 3, 4, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "133", "question": [1, 3, 4, 9], "answer": ["(4+1)×3+9", "(9-3)×4×1", "9×3+1-4", "(4-1)×9-3"], "image": null, "query": "Using the numbers [1, 3, 4, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "134", "question": [1, 3, 4, 10], "answer": ["(10-4)×(3+1)", "(10-3-1)×4", "(3-1)×10+4"], "image": null, "query": "Using the numbers [1, 3, 4, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "135", "question": [1, 3, 4, 11], "answer": ["(11+1-4)×3", "(11-3)×(4-1)", "4×3+11+1"], "image": null, "query": "Using the numbers [1, 3, 4, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "136", "question": [1, 3, 4, 12], "answer": ["(12-4)×3×1", "(4+1-3)×12", "12×4/(3-1)", "(4×3+12)×1"], "image": null, "query": "Using the numbers [1, 3, 4, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "137", "question": [1, 3, 4, 13], "answer": ["(13-4-1)×3", "4×3+13-1"], "image": null, "query": "Using the numbers [1, 3, 4, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "138", "question": [1, 3, 5, 6], "answer": ["6×3+5+1", "(5+1)×3+6"], "image": null, "query": "Using the numbers [1, 3, 5, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "139", "question": [1, 3, 5, 7], "answer": ["(7+5)×(3-1)", "(5+1)×(7-3)"], "image": null, "query": "Using the numbers [1, 3, 5, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "140", "question": [1, 3, 5, 8], "answer": ["(5+1-3)×8", "5×3+8+1", "(8-3)×5-1"], "image": null, "query": "Using the numbers [1, 3, 5, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "141", "question": [1, 3, 5, 9], "answer": ["(9-3)×(5-1)", "(5×3+9)×1", "(5/3+1)×9"], "image": null, "query": "Using the numbers [1, 3, 5, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "142", "question": [1, 3, 5, 10], "answer": ["10×3-5-1", "5×3+10-1"], "image": null, "query": "Using the numbers [1, 3, 5, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "143", "question": [1, 3, 5, 11], "answer": ["(11-5)×(3+1)", "(11+1)×(5-3)"], "image": null, "query": "Using the numbers [1, 3, 5, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "144", "question": [1, 3, 5, 12], "answer": ["(5+1)×12/3", "(12+1-5)×3", "(5-1)×3+12", "(5-3)×12×1"], "image": null, "query": "Using the numbers [1, 3, 5, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "145", "question": [1, 3, 5, 13], "answer": ["(13-5)×3×1", "(13-1)×(5-3)"], "image": null, "query": "Using the numbers [1, 3, 5, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "146", "question": [1, 3, 6, 6], "answer": ["(6+1-3)×6", "(6+6)×(3-1)", "(6×3+6)×1"], "image": null, "query": "Using the numbers [1, 3, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "147", "question": [1, 3, 6, 7], "answer": ["(7-3)×6×1", "(7+1)×(6-3)", "6×3+7-1", "(7-1)×3+6"], "image": null, "query": "Using the numbers [1, 3, 6, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "148", "question": [1, 3, 6, 8], "answer": ["(8-3-1)×6", "8×6/(3-1)", "(6-3)×8×1", "(6/3+1)×8"], "image": null, "query": "Using the numbers [1, 3, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "149", "question": [1, 3, 6, 9], "answer": ["(3-1)×9+6", "(6-1)×3+9", "(9+1)×3-6", "(9-1)×(6-3)", "(9/3+1)×6"], "image": null, "query": "Using the numbers [1, 3, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "150", "question": [1, 3, 6, 10], "answer": ["(10×3-6)×1"], "image": null, "query": "Using the numbers [1, 3, 6, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "151", "question": [1, 3, 6, 11], "answer": ["(11+1)×6/3", "(11-1)×3-6"], "image": null, "query": "Using the numbers [1, 3, 6, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "152", "question": [1, 3, 6, 12], "answer": ["(12-6)×(3+1)", "(6-3-1)×12", "12×6×1/3", "(3-1)×6+12", "12/(1-3/6)"], "image": null, "query": "Using the numbers [1, 3, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "153", "question": [1, 3, 6, 13], "answer": ["(13+1-6)×3", "(13-1)×6/3"], "image": null, "query": "Using the numbers [1, 3, 6, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "154", "question": [1, 3, 7, 7], "answer": ["(7-1)×(7-3)"], "image": null, "query": "Using the numbers [1, 3, 7, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "155", "question": [1, 3, 7, 8], "answer": ["(7-3-1)×8", "3/(1-7/8)"], "image": null, "query": "Using the numbers [1, 3, 7, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "156", "question": [1, 3, 7, 9], "answer": ["(7+1)×9/3"], "image": null, "query": "Using the numbers [1, 3, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "157", "question": [1, 3, 7, 10], "answer": ["(3-1)×7+10", "10×3+1-7"], "image": null, "query": "Using the numbers [1, 3, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "158", "question": [1, 3, 7, 12], "answer": ["(7-1)×12/3"], "image": null, "query": "Using the numbers [1, 3, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "159", "question": [1, 3, 7, 13], "answer": ["13+7+3+1", "(13-7)×(3+1)"], "image": null, "query": "Using the numbers [1, 3, 7, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "160", "question": [1, 3, 8, 8], "answer": ["(3+1)×8-8", "(3-1)×8+8", "(8+1)×8/3"], "image": null, "query": "Using the numbers [1, 3, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "161", "question": [1, 3, 8, 9], "answer": ["9×8×1/3", "3/(9/8-1)"], "image": null, "query": "Using the numbers [1, 3, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "162", "question": [1, 3, 8, 10], "answer": ["(10-1)×8/3"], "image": null, "query": "Using the numbers [1, 3, 8, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "163", "question": [1, 3, 8, 11], "answer": ["11×3-8-1"], "image": null, "query": "Using the numbers [1, 3, 8, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "164", "question": [1, 3, 8, 12], "answer": ["12+8+3+1", "12×8/(3+1)", "(12/3-1)×8"], "image": null, "query": "Using the numbers [1, 3, 8, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "165", "question": [1, 3, 8, 13], "answer": ["(13+8+3)×1"], "image": null, "query": "Using the numbers [1, 3, 8, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "166", "question": [1, 3, 9, 9], "answer": ["(9-1)×9/3"], "image": null, "query": "Using the numbers [1, 3, 9, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "167", "question": [1, 3, 9, 10], "answer": ["(10+1)×3-9"], "image": null, "query": "Using the numbers [1, 3, 9, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "168", "question": [1, 3, 9, 11], "answer": ["11+9+3+1", "(11×3-9)×1", "(11/3-1)×9"], "image": null, "query": "Using the numbers [1, 3, 9, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "169", "question": [1, 3, 9, 12], "answer": ["(3+1)×9-12", "(12-1)×3-9", "(12+9+3)×1", "(9/3-1)×12"], "image": null, "query": "Using the numbers [1, 3, 9, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "170", "question": [1, 3, 9, 13], "answer": ["13+9+3-1"], "image": null, "query": "Using the numbers [1, 3, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "171", "question": [1, 3, 10, 10], "answer": ["10+10+3+1"], "image": null, "query": "Using the numbers [1, 3, 10, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "172", "question": [1, 3, 10, 11], "answer": ["11×3+1-10", "(11+10+3)×1"], "image": null, "query": "Using the numbers [1, 3, 10, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "173", "question": [1, 3, 10, 12], "answer": ["12+10+3-1"], "image": null, "query": "Using the numbers [1, 3, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "174", "question": [1, 3, 11, 11], "answer": ["11+11+3-1"], "image": null, "query": "Using the numbers [1, 3, 11, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "175", "question": [1, 3, 11, 12], "answer": ["12×3-11-1", "(11+1)×3-12"], "image": null, "query": "Using the numbers [1, 3, 11, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "176", "question": [1, 3, 12, 12], "answer": ["(12×3-12)×1"], "image": null, "query": "Using the numbers [1, 3, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "177", "question": [1, 3, 12, 13], "answer": ["12×3+1-13", "(13-1)×3-12"], "image": null, "query": "Using the numbers [1, 3, 12, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "178", "question": [1, 3, 13, 13], "answer": ["13+13+1-3"], "image": null, "query": "Using the numbers [1, 3, 13, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "179", "question": [1, 4, 4, 4], "answer": ["(4+1)×4+4", "(4+4)×(4-1)"], "image": null, "query": "Using the numbers [1, 4, 4, 4], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "180", "question": [1, 4, 4, 5], "answer": ["(5×4+4)×1"], "image": null, "query": "Using the numbers [1, 4, 4, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "181", "question": [1, 4, 4, 6], "answer": ["(6+1)×4-4", "(6-1)×4+4"], "image": null, "query": "Using the numbers [1, 4, 4, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "182", "question": [1, 4, 4, 7], "answer": ["4×4+7+1", "(7×4-4)×1"], "image": null, "query": "Using the numbers [1, 4, 4, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "183", "question": [1, 4, 4, 8], "answer": ["(8-1)×4-4", "(4×4+8)×1"], "image": null, "query": "Using the numbers [1, 4, 4, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "184", "question": [1, 4, 4, 9], "answer": ["(9+1-4)×4", "4×4+9-1"], "image": null, "query": "Using the numbers [1, 4, 4, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "185", "question": [1, 4, 4, 10], "answer": ["(10-4)×4×1"], "image": null, "query": "Using the numbers [1, 4, 4, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "186", "question": [1, 4, 4, 11], "answer": ["(11-4-1)×4"], "image": null, "query": "Using the numbers [1, 4, 4, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "187", "question": [1, 4, 4, 12], "answer": ["(12-4)×(4-1)", "(4-1)×4+12", "(4/4+1)×12"], "image": null, "query": "Using the numbers [1, 4, 4, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "188", "question": [1, 4, 5, 5], "answer": ["5×4+5-1", "(5-1)×5+4"], "image": null, "query": "Using the numbers [1, 4, 5, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "189", "question": [1, 4, 5, 6], "answer": ["6/(5/4-1)", "4/(1-5/6)"], "image": null, "query": "Using the numbers [1, 4, 5, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "190", "question": [1, 4, 5, 7], "answer": ["7×4+1-5", "(5-1)×7-4"], "image": null, "query": "Using the numbers [1, 4, 5, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "191", "question": [1, 4, 5, 8], "answer": ["(5+1)×(8-4)", "(5-1)×4+8"], "image": null, "query": "Using the numbers [1, 4, 5, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "192", "question": [1, 4, 5, 9], "answer": ["(4-1)×5+9", "(9-4)×5-1"], "image": null, "query": "Using the numbers [1, 4, 5, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "193", "question": [1, 4, 5, 10], "answer": ["(10+1-5)×4", "(10-4)×(5-1)"], "image": null, "query": "Using the numbers [1, 4, 5, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "194", "question": [1, 4, 5, 11], "answer": ["(11-5)×4×1"], "image": null, "query": "Using the numbers [1, 4, 5, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "195", "question": [1, 4, 5, 12], "answer": ["(5+1-4)×12", "(12-5-1)×4"], "image": null, "query": "Using the numbers [1, 4, 5, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "196", "question": [1, 4, 5, 13], "answer": ["(13-5)×(4-1)"], "image": null, "query": "Using the numbers [1, 4, 5, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "197", "question": [1, 4, 6, 6], "answer": ["(4+1)×6-6", "(4-1)×6+6"], "image": null, "query": "Using the numbers [1, 4, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "198", "question": [1, 4, 6, 7], "answer": ["(7+1-4)×6", "4/(7/6-1)"], "image": null, "query": "Using the numbers [1, 4, 6, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "199", "question": [1, 4, 6, 8], "answer": ["(6+1-4)×8", "(8-4)×6×1", "8/(1-4/6)"], "image": null, "query": "Using the numbers [1, 4, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "200", "question": [1, 4, 6, 9], "answer": ["(9-4-1)×6"], "image": null, "query": "Using the numbers [1, 4, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "201", "question": [1, 4, 6, 10], "answer": ["(4-1)×10-6"], "image": null, "query": "Using the numbers [1, 4, 6, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "202", "question": [1, 4, 6, 11], "answer": ["(11+1-6)×4", "(11+1)×(6-4)"], "image": null, "query": "Using the numbers [1, 4, 6, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "203", "question": [1, 4, 6, 12], "answer": ["(12-6)×4×1", "12×6/(4-1)", "(6-4)×12×1", "12/(6/4-1)", "(12/4+1)×6"], "image": null, "query": "Using the numbers [1, 4, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "204", "question": [1, 4, 6, 13], "answer": ["13+6+4+1", "(13-6-1)×4", "(13-1)×(6-4)"], "image": null, "query": "Using the numbers [1, 4, 6, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "205", "question": [1, 4, 7, 7], "answer": ["(7+1)×(7-4)"], "image": null, "query": "Using the numbers [1, 4, 7, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "206", "question": [1, 4, 7, 8], "answer": ["8×4-7-1", "(7+1)×4-8", "(7-1)×(8-4)", "(7-4)×8×1"], "image": null, "query": "Using the numbers [1, 4, 7, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "207", "question": [1, 4, 7, 9], "answer": ["(9-1)×(7-4)"], "image": null, "query": "Using the numbers [1, 4, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "208", "question": [1, 4, 7, 11], "answer": ["(4+1)×7-11"], "image": null, "query": "Using the numbers [1, 4, 7, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "209", "question": [1, 4, 7, 12], "answer": ["12+7+4+1", "(7-4-1)×12", "(7+1)×12/4", "(12+1-7)×4"], "image": null, "query": "Using the numbers [1, 4, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "210", "question": [1, 4, 7, 13], "answer": ["(13-7)×4×1", "(13+7+4)×1"], "image": null, "query": "Using the numbers [1, 4, 7, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "211", "question": [1, 4, 8, 8], "answer": ["(8-4-1)×8", "(8×4-8)×1", "(8/4+1)×8"], "image": null, "query": "Using the numbers [1, 4, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "212", "question": [1, 4, 8, 9], "answer": ["9×8/(4-1)", "8×4+1-9", "(9-1)×4-8"], "image": null, "query": "Using the numbers [1, 4, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "213", "question": [1, 4, 8, 11], "answer": ["11+8+4+1", "(11+1)×8/4"], "image": null, "query": "Using the numbers [1, 4, 8, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "214", "question": [1, 4, 8, 12], "answer": ["12×8×1/4", "(8+1)×4-12", "(12+8+4)×1", "12/(1-4/8)"], "image": null, "query": "Using the numbers [1, 4, 8, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "215", "question": [1, 4, 8, 13], "answer": ["13+8+4-1", "(13+1-8)×4", "(13-1)×8/4"], "image": null, "query": "Using the numbers [1, 4, 8, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "216", "question": [1, 4, 9, 10], "answer": ["10+9+4+1"], "image": null, "query": "Using the numbers [1, 4, 9, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "217", "question": [1, 4, 9, 11], "answer": ["(4-1)×11-9", "9×4-11-1", "(11+9+4)×1"], "image": null, "query": "Using the numbers [1, 4, 9, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "218", "question": [1, 4, 9, 12], "answer": ["12+9+4-1", "(9-1)×12/4", "(9×4-12)×1"], "image": null, "query": "Using the numbers [1, 4, 9, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "219", "question": [1, 4, 9, 13], "answer": ["9×4+1-13"], "image": null, "query": "Using the numbers [1, 4, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "220", "question": [1, 4, 10, 10], "answer": ["(10+10+4)×1", "10×10/4-1"], "image": null, "query": "Using the numbers [1, 4, 10, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "221", "question": [1, 4, 10, 11], "answer": ["11+10+4-1"], "image": null, "query": "Using the numbers [1, 4, 10, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "222", "question": [1, 4, 10, 12], "answer": ["12×10/(4+1)", "(10-1)×4-12", "4/(1-10/12)"], "image": null, "query": "Using the numbers [1, 4, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "223", "question": [1, 4, 12, 12], "answer": ["(4-1)×12-12", "(12/4-1)×12"], "image": null, "query": "Using the numbers [1, 4, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "224", "question": [1, 5, 5, 5], "answer": ["(5-1/5)×5"], "image": null, "query": "Using the numbers [1, 5, 5, 5], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "225", "question": [1, 5, 5, 6], "answer": ["6×5-5-1", "(5+1)×5-6"], "image": null, "query": "Using the numbers [1, 5, 5, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "226", "question": [1, 5, 5, 9], "answer": ["(5+1)×(9-5)"], "image": null, "query": "Using the numbers [1, 5, 5, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "227", "question": [1, 5, 5, 10], "answer": ["(10-5)×5-1"], "image": null, "query": "Using the numbers [1, 5, 5, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "228", "question": [1, 5, 5, 11], "answer": ["(11-5)×(5-1)"], "image": null, "query": "Using the numbers [1, 5, 5, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "229", "question": [1, 5, 5, 12], "answer": ["(5/5+1)×12"], "image": null, "query": "Using the numbers [1, 5, 5, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "230", "question": [1, 5, 5, 13], "answer": ["13+5+5+1"], "image": null, "query": "Using the numbers [1, 5, 5, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "231", "question": [1, 5, 6, 6], "answer": ["(6×5-6)×1"], "image": null, "query": "Using the numbers [1, 5, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "232", "question": [1, 5, 6, 7], "answer": ["6×5+1-7", "(7-1)×5-6"], "image": null, "query": "Using the numbers [1, 5, 6, 7], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "233", "question": [1, 5, 6, 8], "answer": ["(8+1-5)×6"], "image": null, "query": "Using the numbers [1, 5, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "234", "question": [1, 5, 6, 9], "answer": ["(9-5)×6×1"], "image": null, "query": "Using the numbers [1, 5, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "235", "question": [1, 5, 6, 10], "answer": ["(5+1)×(10-6)", "(10-5-1)×6"], "image": null, "query": "Using the numbers [1, 5, 6, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "236", "question": [1, 5, 6, 11], "answer": ["(6+1)×5-11", "(11-6)×5-1"], "image": null, "query": "Using the numbers [1, 5, 6, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "237", "question": [1, 5, 6, 12], "answer": ["12+6+5+1", "(5+1)×6-12", "(6+1-5)×12", "(12-6)×(5-1)"], "image": null, "query": "Using the numbers [1, 5, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "238", "question": [1, 5, 6, 13], "answer": ["(13+6+5)×1"], "image": null, "query": "Using the numbers [1, 5, 6, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "239", "question": [1, 5, 7, 8], "answer": ["(7+1-5)×8", "(7+1)×(8-5)"], "image": null, "query": "Using the numbers [1, 5, 7, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "240", "question": [1, 5, 7, 9], "answer": ["(7-1)×(9-5)"], "image": null, "query": "Using the numbers [1, 5, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "241", "question": [1, 5, 7, 10], "answer": ["7×5-10-1", "(7/5+1)×10"], "image": null, "query": "Using the numbers [1, 5, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "242", "question": [1, 5, 7, 11], "answer": ["11+7+5+1", "(5+1)×(11-7)", "(11+1)×(7-5)", "(7×5-11)×1"], "image": null, "query": "Using the numbers [1, 5, 7, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "243", "question": [1, 5, 7, 12], "answer": ["(7-5)×12×1", "7×5+1-12", "(12+7+5)×1", "(12-7)×5-1"], "image": null, "query": "Using the numbers [1, 5, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "244", "question": [1, 5, 7, 13], "answer": ["13+7+5-1", "(13-7)×(5-1)", "(13-1)×(7-5)"], "image": null, "query": "Using the numbers [1, 5, 7, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "245", "question": [1, 5, 8, 8], "answer": ["(5-1)×8-8", "(8-5)×8×1"], "image": null, "query": "Using the numbers [1, 5, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "246", "question": [1, 5, 8, 9], "answer": ["(9-5-1)×8", "(9-1)×(8-5)", "9/(1-5/8)"], "image": null, "query": "Using the numbers [1, 5, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "247", "question": [1, 5, 8, 10], "answer": ["10+8+5+1", "(10/5+1)×8"], "image": null, "query": "Using the numbers [1, 5, 8, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "248", "question": [1, 5, 8, 11], "answer": ["(8-1)×5-11", "(11+8+5)×1"], "image": null, "query": "Using the numbers [1, 5, 8, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "249", "question": [1, 5, 8, 12], "answer": ["(5+1)×(12-8)", "(8-5-1)×12", "12+8+5-1", "12×8/(5-1)"], "image": null, "query": "Using the numbers [1, 5, 8, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "250", "question": [1, 5, 8, 13], "answer": ["(13-8)×5-1"], "image": null, "query": "Using the numbers [1, 5, 8, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "251", "question": [1, 5, 9, 9], "answer": ["9+9+5+1"], "image": null, "query": "Using the numbers [1, 5, 9, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "252", "question": [1, 5, 9, 10], "answer": ["(10+9+5)×1"], "image": null, "query": "Using the numbers [1, 5, 9, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "253", "question": [1, 5, 9, 11], "answer": ["11+9+5-1"], "image": null, "query": "Using the numbers [1, 5, 9, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "254", "question": [1, 5, 9, 12], "answer": ["(5-1)×9-12", "(9+1)×12/5"], "image": null, "query": "Using the numbers [1, 5, 9, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "255", "question": [1, 5, 9, 13], "answer": ["(5+1)×(13-9)"], "image": null, "query": "Using the numbers [1, 5, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "256", "question": [1, 5, 10, 10], "answer": ["10+10+5-1"], "image": null, "query": "Using the numbers [1, 5, 10, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "257", "question": [1, 5, 10, 11], "answer": ["(11+1)×10/5"], "image": null, "query": "Using the numbers [1, 5, 10, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "258", "question": [1, 5, 10, 12], "answer": ["12×10×1/5", "12/(1-5/10)"], "image": null, "query": "Using the numbers [1, 5, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "259", "question": [1, 5, 10, 13], "answer": ["(13-1)×10/5"], "image": null, "query": "Using the numbers [1, 5, 10, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "260", "question": [1, 5, 11, 11], "answer": ["(11×11-1)/5"], "image": null, "query": "Using the numbers [1, 5, 11, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "261", "question": [1, 5, 11, 12], "answer": ["(11-1)×12/5"], "image": null, "query": "Using the numbers [1, 5, 11, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "262", "question": [1, 5, 12, 12], "answer": ["12×12/(5+1)"], "image": null, "query": "Using the numbers [1, 5, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "263", "question": [1, 6, 6, 6], "answer": ["(6-1)×6-6"], "image": null, "query": "Using the numbers [1, 6, 6, 6], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "264", "question": [1, 6, 6, 8], "answer": ["6/(1-6/8)"], "image": null, "query": "Using the numbers [1, 6, 6, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "265", "question": [1, 6, 6, 9], "answer": ["(9+1-6)×6"], "image": null, "query": "Using the numbers [1, 6, 6, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "266", "question": [1, 6, 6, 10], "answer": ["(10-6)×6×1"], "image": null, "query": "Using the numbers [1, 6, 6, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "267", "question": [1, 6, 6, 11], "answer": ["11+6+6+1", "(11-6-1)×6", "6×6-11-1"], "image": null, "query": "Using the numbers [1, 6, 6, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "268", "question": [1, 6, 6, 12], "answer": ["(12+6+6)×1", "(6×6-12)×1", "(6/6+1)×12"], "image": null, "query": "Using the numbers [1, 6, 6, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "269", "question": [1, 6, 6, 13], "answer": ["13+6+6-1", "6×6+1-13"], "image": null, "query": "Using the numbers [1, 6, 6, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "270", "question": [1, 6, 7, 9], "answer": ["(7+1)×(9-6)"], "image": null, "query": "Using the numbers [1, 6, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "271", "question": [1, 6, 7, 10], "answer": ["10+7+6+1", "(10+1-7)×6", "(7-1)×(10-6)"], "image": null, "query": "Using the numbers [1, 6, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "272", "question": [1, 6, 7, 11], "answer": ["(11-7)×6×1", "(6-1)×7-11", "(11+7+6)×1"], "image": null, "query": "Using the numbers [1, 6, 7, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "273", "question": [1, 6, 7, 12], "answer": ["12+7+6-1", "(7+1-6)×12", "(12-7-1)×6", "(7-1)×6-12"], "image": null, "query": "Using the numbers [1, 6, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "274", "question": [1, 6, 8, 8], "answer": ["(8+1-6)×8", "8/(8/6-1)"], "image": null, "query": "Using the numbers [1, 6, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "275", "question": [1, 6, 8, 9], "answer": ["9+8+6+1", "(9-6)×8×1", "8/(1-6/9)"], "image": null, "query": "Using the numbers [1, 6, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "276", "question": [1, 6, 8, 10], "answer": ["(10-6-1)×8", "(10+8+6)×1", "6/(10/8-1)"], "image": null, "query": "Using the numbers [1, 6, 8, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "277", "question": [1, 6, 8, 11], "answer": ["11+8+6-1", "(11+1-8)×6", "(11+1)×(8-6)"], "image": null, "query": "Using the numbers [1, 6, 8, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "278", "question": [1, 6, 8, 12], "answer": ["(12-8)×6×1", "(8-6)×12×1", "(12/6+1)×8"], "image": null, "query": "Using the numbers [1, 6, 8, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "279", "question": [1, 6, 8, 13], "answer": ["(13-8-1)×6", "(13-1)×(8-6)"], "image": null, "query": "Using the numbers [1, 6, 8, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "280", "question": [1, 6, 9, 9], "answer": ["(9-1)×(9-6)", "(9+9+6)×1"], "image": null, "query": "Using the numbers [1, 6, 9, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "281", "question": [1, 6, 9, 10], "answer": ["10+9+6-1", "(10/6+1)×9"], "image": null, "query": "Using the numbers [1, 6, 9, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "282", "question": [1, 6, 9, 12], "answer": ["(9-6-1)×12", "(12+1-9)×6", "12/(9/6-1)", "6/(1-9/12)"], "image": null, "query": "Using the numbers [1, 6, 9, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "283", "question": [1, 6, 9, 13], "answer": ["(13-9)×6×1"], "image": null, "query": "Using the numbers [1, 6, 9, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "284", "question": [1, 6, 10, 12], "answer": ["12×10/(6-1)"], "image": null, "query": "Using the numbers [1, 6, 10, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "285", "question": [1, 6, 10, 13], "answer": ["(13+1-10)×6"], "image": null, "query": "Using the numbers [1, 6, 10, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "286", "question": [1, 6, 11, 12], "answer": ["(11+1)×12/6"], "image": null, "query": "Using the numbers [1, 6, 11, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "287", "question": [1, 6, 11, 13], "answer": ["(13×11+1)/6"], "image": null, "query": "Using the numbers [1, 6, 11, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "288", "question": [1, 6, 12, 12], "answer": ["12×12×1/6", "12/(1-6/12)"], "image": null, "query": "Using the numbers [1, 6, 12, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "289", "question": [1, 6, 12, 13], "answer": ["(13-1)×12/6"], "image": null, "query": "Using the numbers [1, 6, 12, 13], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "290", "question": [1, 7, 7, 9], "answer": ["9+7+7+1"], "image": null, "query": "Using the numbers [1, 7, 7, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "291", "question": [1, 7, 7, 10], "answer": ["(7+1)×(10-7)", "(10+7+7)×1"], "image": null, "query": "Using the numbers [1, 7, 7, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "292", "question": [1, 7, 7, 11], "answer": ["11+7+7-1", "(7-1)×(11-7)"], "image": null, "query": "Using the numbers [1, 7, 7, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "293", "question": [1, 7, 7, 12], "answer": ["(7/7+1)×12"], "image": null, "query": "Using the numbers [1, 7, 7, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "294", "question": [1, 7, 8, 8], "answer": ["8+8+7+1"], "image": null, "query": "Using the numbers [1, 7, 8, 8], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "295", "question": [1, 7, 8, 9], "answer": ["(9+1-7)×8", "(9+8+7)×1"], "image": null, "query": "Using the numbers [1, 7, 8, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "296", "question": [1, 7, 8, 10], "answer": ["10+8+7-1", "(10-7)×8×1"], "image": null, "query": "Using the numbers [1, 7, 8, 10], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "297", "question": [1, 7, 8, 11], "answer": ["(7+1)×(11-8)", "(11-7-1)×8"], "image": null, "query": "Using the numbers [1, 7, 8, 11], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "298", "question": [1, 7, 8, 12], "answer": ["(8+1-7)×12", "(7-1)×(12-8)"], "image": null, "query": "Using the numbers [1, 7, 8, 12], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}, {"pid": "299", "question": [1, 7, 9, 9], "answer": ["9+9+7-1"], "image": null, "query": "Using the numbers [1, 7, 9, 9], create an expression that equals 24. You must use basic arithmetic operations (+, -, ×, /) and parentheses. Example: for [1, 2, 3, 4], one solution is (1+2+3)×4."}]