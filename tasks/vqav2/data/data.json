[{"pid": "0", "raw_id": 262148000, "question": "Where is he looking?", "answer": "down", "answer_mc": "down", "answer_gt": ["down", "down", "at table", "skateboard", "down", "table", "down", "down", "down", "down"], "image": "images/COCO_val2014_000000262148.jpg", "query": "Where is he looking?"}, {"pid": "1", "raw_id": 262148001, "question": "What are the people in the background doing?", "answer": "watching", "answer_mc": "watching", "answer_gt": ["spectating", "watching", "watching", "watching", "watching", "watching", "watching", "watching", "watching"], "image": "images/COCO_val2014_000000262148.jpg", "query": "What are the people in the background doing?"}, {"pid": "2", "raw_id": 262148002, "question": "What is he on top of?", "answer": "picnic table", "answer_mc": "picnic table", "answer_gt": ["table", "table", "table", "picnic table", "picnic table", "picnic table", "picnic table", "picnic table", "skateboard", "picnic table"], "image": "images/COCO_val2014_000000262148.jpg", "query": "What is he on top of?"}, {"pid": "3", "raw_id": 393225000, "question": "What website copyrighted the picture?", "answer": "foodiebakercom", "answer_mc": "foodiebakercom", "answer_gt": ["foodiebakercom", "foodiebakercom", "<PERSON>iebaker", "foodiebakercom", "foodiebakercom", "http://foodiebakercom", "foodiebakercom", "foodiebakercom", "foodiebakercom", "<PERSON>iebaker"], "image": "images/COCO_val2014_000000393225.jpg", "query": "What website copyrighted the picture?"}, {"pid": "4", "raw_id": 393225001, "question": "Is this a creamy soup?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393225.jpg", "query": "Is this a creamy soup?"}, {"pid": "5", "raw_id": 393225002, "question": "Is this rice noodle soup?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393225.jpg", "query": "Is this rice noodle soup?"}, {"pid": "6", "raw_id": 393225003, "question": "What is to the right of the soup?", "answer": "chopsticks", "answer_mc": "chopsticks", "answer_gt": ["chopsticks", "chopsticks", "chopsticks", "chopsticks", "chopsticks", "shrimp", "chopsticks", "chopsticks", "chopsticks"], "image": "images/COCO_val2014_000000393225.jpg", "query": "What is to the right of the soup?"}, {"pid": "7", "raw_id": 393226000, "question": "What is the man doing in the street?", "answer": "walking", "answer_mc": "walking", "answer_gt": ["crossing it", "walking", "walking", "crossing", "crossing road", "walking", "crossing", "walking", "crossing", "walking"], "image": "images/COCO_val2014_000000393226.jpg", "query": "What is the man doing in the street?"}, {"pid": "8", "raw_id": 393226001, "question": "How many photo's can you see?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "4", "4", "1", "1", "4", "1"], "image": "images/COCO_val2014_000000393226.jpg", "query": "How many photo's can you see?"}, {"pid": "9", "raw_id": 393226002, "question": "What does the truck on the left sell?", "answer": "ice cream", "answer_mc": "ice cream", "answer_gt": ["ice cream", "ice cream", "ice cream", "ice cream", "ice cream", "ice cream", "ice cream", "ice cream", "ice cream"], "image": "images/COCO_val2014_000000393226.jpg", "query": "What does the truck on the left sell?"}, {"pid": "10", "raw_id": 240301000, "question": "Why is there a gap between the roof and wall?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["ventilation", "provide air", "to air out barn from stinky bovines", "keep cow safe", "airflow", "yes", "yes", "for air", "air"], "image": "images/COCO_val2014_000000240301.jpg", "query": "Why is there a gap between the roof and wall?"}, {"pid": "11", "raw_id": 240301001, "question": "Is it daylight in this picture?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000240301.jpg", "query": "Is it daylight in this picture?"}, {"pid": "12", "raw_id": 240301002, "question": "Why is the cow laying down?", "answer": "tired", "answer_mc": "tired", "answer_gt": ["tired", "tired", "4", "resting", "tired", "tired", "it's tired", "tired"], "image": "images/COCO_val2014_000000240301.jpg", "query": "Why is the cow laying down?"}, {"pid": "13", "raw_id": 131089000, "question": "What color is the grass in this picture?", "answer": "green", "answer_mc": "green", "answer_gt": ["gray", "green", "black, white", "brown", "gray"], "image": "images/COCO_val2014_000000131089.jpg", "query": "What color is the grass in this picture?"}, {"pid": "14", "raw_id": 131089001, "question": "Did the batter hit the ball?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "no", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131089.jpg", "query": "Did the batter hit the ball?"}, {"pid": "15", "raw_id": 131089002, "question": "How many are playing ball?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000131089.jpg", "query": "How many are playing ball?"}, {"pid": "16", "raw_id": 131089003, "question": "Is there a chain link fence in the image?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000131089.jpg", "query": "Is there a chain link fence in the image?"}, {"pid": "17", "raw_id": 131089004, "question": "Is the boy playing baseball?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131089.jpg", "query": "Is the boy playing baseball?"}, {"pid": "18", "raw_id": 262162000, "question": "Is that a folding chair?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is that a folding chair?"}, {"pid": "19", "raw_id": 262162001, "question": "How many beds?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "10", "1", "1", "1"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many beds?"}, {"pid": "20", "raw_id": 262162002, "question": "What color is the bedspread?", "answer": "white", "answer_mc": "white", "answer_gt": ["beige", "white", "white", "white", "white", "yellow", "yellow", "beige"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What color is the bedspread?"}, {"pid": "21", "raw_id": 262162003, "question": "How many pictures are there?", "answer": "7", "answer_mc": "7", "answer_gt": ["7", "7", "7", "7", "7", "7", "7", "7"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many pictures are there?"}, {"pid": "22", "raw_id": 262162004, "question": "Are these twin mattresses?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no, it is queen", "no", "no", "yes", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Are these twin mattresses?"}, {"pid": "23", "raw_id": 262162005, "question": "How many sources of light are there?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "2", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many sources of light are there?"}, {"pid": "24", "raw_id": 262162006, "question": "Is this room decorated for the 1970s?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is this room decorated for the 1970s?"}, {"pid": "25", "raw_id": 262162007, "question": "Are the lights on in this room?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Are the lights on in this room?"}, {"pid": "26", "raw_id": 262162008, "question": "Are the windows big?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "no", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Are the windows big?"}, {"pid": "27", "raw_id": 262162009, "question": "What is the chair made of?", "answer": "leather", "answer_mc": "leather", "answer_gt": ["leather", "plastic", "plastic, vinyl, metal", "leather", "leather"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What is the chair made of?"}, {"pid": "28", "raw_id": 262162010, "question": "Is this room in someone's home?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is this room in someone's home?"}, {"pid": "29", "raw_id": 262162011, "question": "Which room is this?", "answer": "bedroom", "answer_mc": "bedroom", "answer_gt": ["bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Which room is this?"}, {"pid": "30", "raw_id": 262162012, "question": "Is the bed white?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no", "yes", "no", "no", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is the bed white?"}, {"pid": "31", "raw_id": 262162013, "question": "Could this be a hotel room?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "yes", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Could this be a hotel room?"}, {"pid": "32", "raw_id": 262162014, "question": "Is the bed made?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no", "yes", "yes", "yes", "yes, just one", "yes", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is the bed made?"}, {"pid": "33", "raw_id": 262162015, "question": "Are there bed headboards present in the photo?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Are there bed headboards present in the photo?"}, {"pid": "34", "raw_id": 262162016, "question": "Is there a mirror in the room?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is there a mirror in the room?"}, {"pid": "35", "raw_id": 262162017, "question": "What kind of room is this?", "answer": "bedroom", "answer_mc": "bedroom", "answer_gt": ["bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom", "bedroom"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What kind of room is this?"}, {"pid": "36", "raw_id": 262162018, "question": "How many chairs are in the photo?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many chairs are in the photo?"}, {"pid": "37", "raw_id": 262162019, "question": "Is the desk cluttered?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Is the desk cluttered?"}, {"pid": "38", "raw_id": 262162020, "question": "Has the bed been made?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no", "yes", "yes", "yes", "no", "yes"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Has the bed been made?"}, {"pid": "39", "raw_id": 262162021, "question": "How many seats are there?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many seats are there?"}, {"pid": "40", "raw_id": 262162022, "question": "How many frames are on the wall?", "answer": "7", "answer_mc": "7", "answer_gt": ["7", "7", "7", "7", "7", "7", "7", "7", "7"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many frames are on the wall?"}, {"pid": "41", "raw_id": 262162023, "question": "What color are the walls?", "answer": "white", "answer_mc": "white", "answer_gt": ["white", "off white", "off white", "white", "white", "beige", "white", "white"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What color are the walls?"}, {"pid": "42", "raw_id": 262162024, "question": "Are there any boxes in the room?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Are there any boxes in the room?"}, {"pid": "43", "raw_id": 262162025, "question": "Could this be a multi-purpose room?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262162.jpg", "query": "Could this be a multi-purpose room?"}, {"pid": "44", "raw_id": 262162026, "question": "What animal print does that chair resemble?", "answer": "none", "answer_mc": "none", "answer_gt": ["panther", "none", "leather", "none", "none"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What animal print does that chair resemble?"}, {"pid": "45", "raw_id": 262162027, "question": "What is behind the foot of the bed?", "answer": "chair", "answer_mc": "chair", "answer_gt": ["chair", "chair", "chair", "wall", "chair", "chair", "chair", "chair", "chair"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What is behind the foot of the bed?"}, {"pid": "46", "raw_id": 262162028, "question": "What is the size of the bed?", "answer": "queen", "answer_mc": "queen", "answer_gt": ["queen", "queen sized", "queen", "queen", "double", "queen"], "image": "images/COCO_val2014_000000262162.jpg", "query": "What is the size of the bed?"}, {"pid": "47", "raw_id": 262162029, "question": "How many pictures on the wall?", "answer": "7", "answer_mc": "7", "answer_gt": ["7", "7", "7", "7", "7", "7", "7", "7", "6", "7"], "image": "images/COCO_val2014_000000262162.jpg", "query": "How many pictures on the wall?"}, {"pid": "48", "raw_id": 393243000, "question": "Will this kid leave the powdered sugar on his face?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "no", "yes", "yes"], "image": "images/COCO_val2014_000000393243.jpg", "query": "Will this kid leave the powdered sugar on his face?"}, {"pid": "49", "raw_id": 393243001, "question": "What is the child eating?", "answer": "donut", "answer_mc": "donut", "answer_gt": ["donut", "doughnut", "donut", "donut", "donut hole", "donut", "donut"], "image": "images/COCO_val2014_000000393243.jpg", "query": "What is the child eating?"}, {"pid": "50", "raw_id": 393243002, "question": "Is this person wearing a tie?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393243.jpg", "query": "Is this person wearing a tie?"}, {"pid": "51", "raw_id": 393243003, "question": "What color is the kids hair?", "answer": "blonde", "answer_mc": "blonde", "answer_gt": ["blonde", "blonde", "blonde", "blonde", "blonde", "blonde", "blonde", "blonde", "blonde", "blonde"], "image": "images/COCO_val2014_000000393243.jpg", "query": "What color is the kids hair?"}, {"pid": "52", "raw_id": 262175000, "question": "Is the man's tie purple?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262175.jpg", "query": "Is the man's tie purple?"}, {"pid": "53", "raw_id": 262175001, "question": "Where was the picture taken of the man?", "answer": "forest", "answer_mc": "forest", "answer_gt": ["park", "forest", "forest", "forest", "outside"], "image": "images/COCO_val2014_000000262175.jpg", "query": "Where was the picture taken of the man?"}, {"pid": "54", "raw_id": 262175002, "question": "What is the man wearing on his head?", "answer": "hat", "answer_mc": "hat", "answer_gt": ["hat", "silly hat", "palm leaves", "leaf-hat", "hat", "hat"], "image": "images/COCO_val2014_000000262175.jpg", "query": "What is the man wearing on his head?"}, {"pid": "55", "raw_id": 262175003, "question": "Is this a designer tie?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262175.jpg", "query": "Is this a designer tie?"}, {"pid": "56", "raw_id": 131108000, "question": "Is this photo color?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000131108.jpg", "query": "Is this photo color?"}, {"pid": "57", "raw_id": 131108001, "question": "Does the door open inward or outward?", "answer": "inward", "answer_mc": "inward", "answer_gt": ["outward", "out"], "image": "images/COCO_val2014_000000131108.jpg", "query": "Does the door open inward or outward?"}, {"pid": "58", "raw_id": 131108002, "question": "Is there a drainage in the pic?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131108.jpg", "query": "Is there a drainage in the pic?"}, {"pid": "59", "raw_id": 393254000, "question": "What does the sentence on the top say?", "answer": "this is camping", "answer_mc": "this is camping", "answer_gt": ["this is camping", "this is camping", "this is camping", "this is camping", "this is camping", "this is camping", "this is camping", "this is camping", "this is camping", "this is camping"], "image": "images/COCO_val2014_000000393254.jpg", "query": "What does the sentence on the top say?"}, {"pid": "60", "raw_id": 393254001, "question": "What kind of vehicle is the RV pulling on the bottom picture?", "answer": "jeep", "answer_mc": "jeep", "answer_gt": ["jeep", "bus", "jeep", "brown", "suv", "suv", "car", "jeep", "jeep"], "image": "images/COCO_val2014_000000393254.jpg", "query": "What kind of vehicle is the RV pulling on the bottom picture?"}, {"pid": "61", "raw_id": 393254002, "question": "Where is the camping tent?", "answer": "woods", "answer_mc": "woods", "answer_gt": ["forest", "in wood", "woods", "woods", "in woods", "top picture", "top picture", "top picture", "campground", "woods"], "image": "images/COCO_val2014_000000393254.jpg", "query": "Where is the camping tent?"}, {"pid": "62", "raw_id": 42000, "question": "What color are the gym shoes?", "answer": "white", "answer_mc": "white", "answer_gt": ["white", "white", "white", "white", "white", "white", "white", "white", "white"], "image": "images/COCO_val2014_000000000042.jpg", "query": "What color are the gym shoes?"}, {"pid": "63", "raw_id": 42001, "question": "Is there a red sandal here?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000000042.jpg", "query": "Is there a red sandal here?"}, {"pid": "64", "raw_id": 42002, "question": "What color is the flip flop?", "answer": "red", "answer_mc": "red", "answer_gt": ["black", "red", "red", "red", "red and blue", "red", "red", "red", "red", "red"], "image": "images/COCO_val2014_000000000042.jpg", "query": "What color is the flip flop?"}, {"pid": "65", "raw_id": 131115000, "question": "Are there players in the dugout?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131115.jpg", "query": "Are there players in the dugout?"}, {"pid": "66", "raw_id": 131115001, "question": "Is the battery looking at the ball?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "yes", "no", "no", "no", "no", "no", "yes"], "image": "images/COCO_val2014_000000131115.jpg", "query": "Is the battery looking at the ball?"}, {"pid": "67", "raw_id": 131115002, "question": "How many people are on the field?", "answer": "3", "answer_mc": "3", "answer_gt": ["3", "3", "3", "3", "3", "3", "3", "3"], "image": "images/COCO_val2014_000000131115.jpg", "query": "How many people are on the field?"}, {"pid": "68", "raw_id": 262189000, "question": "How many signs?", "answer": "4", "answer_mc": "4", "answer_gt": ["4", "4", "4", "4", "4", "4", "4", "4", "4", "4"], "image": "images/COCO_val2014_000000262189.jpg", "query": "How many signs?"}, {"pid": "69", "raw_id": 262189001, "question": "What color is the Salisbury Rd. sign?", "answer": "white", "answer_mc": "white", "answer_gt": ["white with blue lettering", "white and blue", "white", "blue and white", "white, brown", "white", "white", "white", "white and blue"], "image": "images/COCO_val2014_000000262189.jpg", "query": "What color is the Salisbury Rd. sign?"}, {"pid": "70", "raw_id": 262189002, "question": "What does the last sign say?", "answer": "ross st", "answer_mc": "ross st", "answer_gt": ["ross st", "ross st", "ross st", "ross st", "ross st", "ross street", "ross", "ross st", "ross st", "camperdown ross st"], "image": "images/COCO_val2014_000000262189.jpg", "query": "What does the last sign say?"}, {"pid": "71", "raw_id": 393266000, "question": "What color is the sign?", "answer": "red and yellow", "answer_mc": "red and yellow", "answer_gt": ["red and yellow", "red", "red", "orange, yellow, white, black", "red and yellow", "red/yellow", "red orange", "red and orange", "red and yellow", "red"], "image": "images/COCO_val2014_000000393266.jpg", "query": "What color is the sign?"}, {"pid": "72", "raw_id": 393266001, "question": "Can you turn right or left in the tunnel?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393266.jpg", "query": "Can you turn right or left in the tunnel?"}, {"pid": "73", "raw_id": 393266002, "question": "What color is the plant?", "answer": "green", "answer_mc": "green", "answer_gt": ["green", "white", "green", "green", "green", "green", "green", "red, orange", "green", "green"], "image": "images/COCO_val2014_000000393266.jpg", "query": "What color is the plant?"}, {"pid": "74", "raw_id": 393267000, "question": "What color is the woman's shirt on the left?", "answer": "black", "answer_mc": "black", "answer_gt": ["black", "black", "black", "black", "black", "black", "black", "black", "black"], "image": "images/COCO_val2014_000000393267.jpg", "query": "What color is the woman's shirt on the left?"}, {"pid": "75", "raw_id": 393267001, "question": "What type of beverage is being displayed?", "answer": "wine", "answer_mc": "wine", "answer_gt": ["wine", "wine", "wine", "wine", "wine", "wine", "wine", "wine", "wine", "wine"], "image": "images/COCO_val2014_000000393267.jpg", "query": "What type of beverage is being displayed?"}, {"pid": "76", "raw_id": 393267002, "question": "Why are some people wearing hats?", "answer": "nobody is wearing hats", "answer_mc": "nobody is wearing hats", "answer_gt": ["they aren't", "none are wearing hats", "style", "nobody's wearing hats", "to keep warm", "they are not"], "image": "images/COCO_val2014_000000393267.jpg", "query": "Why are some people wearing hats?"}, {"pid": "77", "raw_id": 393267003, "question": "What kind of event are the people involved in?", "answer": "wine tasting", "answer_mc": "wine tasting", "answer_gt": ["wine", "taste testing", "alcohol show", "wine tasting", "wine tasting", "wine tasting"], "image": "images/COCO_val2014_000000393267.jpg", "query": "What kind of event are the people involved in?"}, {"pid": "78", "raw_id": 262197000, "question": "How many skyscrapers are there?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "0", "0", "0"], "image": "images/COCO_val2014_000000262197.jpg", "query": "How many skyscrapers are there?"}, {"pid": "79", "raw_id": 262197001, "question": "What is the color of the building in the background?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["gray", "brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown"], "image": "images/COCO_val2014_000000262197.jpg", "query": "What is the color of the building in the background?"}, {"pid": "80", "raw_id": 262197002, "question": "Is there a tree in front of the building?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262197.jpg", "query": "Is there a tree in front of the building?"}, {"pid": "81", "raw_id": 262197003, "question": "Is there a person riding a bike?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262197.jpg", "query": "Is there a person riding a bike?"}, {"pid": "82", "raw_id": 262197004, "question": "Are there lights on in the two buildings?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "2", "yes", "no"], "image": "images/COCO_val2014_000000262197.jpg", "query": "Are there lights on in the two buildings?"}, {"pid": "83", "raw_id": 262197005, "question": "How many stories is the building on the left?", "answer": "3", "answer_mc": "3", "answer_gt": ["3", "3", "4", "5", "many", "6"], "image": "images/COCO_val2014_000000262197.jpg", "query": "How many stories is the building on the left?"}, {"pid": "84", "raw_id": 262197006, "question": "Is this a hospital?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "yes", "no", "maybe"], "image": "images/COCO_val2014_000000262197.jpg", "query": "Is this a hospital?"}, {"pid": "85", "raw_id": 262197007, "question": "How many umbrellas do you see?", "answer": "0", "answer_mc": "0", "answer_gt": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "image": "images/COCO_val2014_000000262197.jpg", "query": "How many umbrellas do you see?"}, {"pid": "86", "raw_id": 262197008, "question": "How many colors is the building on the right painted?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "2", "1", "brown", "2", "1", "2", "2", "1"], "image": "images/COCO_val2014_000000262197.jpg", "query": "How many colors is the building on the right painted?"}, {"pid": "87", "raw_id": 262197009, "question": "What is on the boy's back walking?", "answer": "backpack", "answer_mc": "backpack", "answer_gt": ["backpack", "backpack", "backpack", "backpack", "backpack", "backpack", "backpack", "backpack", "backpack"], "image": "images/COCO_val2014_000000262197.jpg", "query": "What is on the boy's back walking?"}, {"pid": "88", "raw_id": 262197010, "question": "How many modes of transportation are depicted?", "answer": "0", "answer_mc": "0", "answer_gt": ["2", "1", "1", "0", "0", "0", "0"], "image": "images/COCO_val2014_000000262197.jpg", "query": "How many modes of transportation are depicted?"}, {"pid": "89", "raw_id": 262197011, "question": "What color is the building?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["brown", "brown", "brown", "brown", "gray", "brown", "brown", "brown", "brown", "brown"], "image": "images/COCO_val2014_000000262197.jpg", "query": "What color is the building?"}, {"pid": "90", "raw_id": 393271000, "question": "Is it still snowing in the picture?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no", "yes", "no", "yes", "no", "no"], "image": "images/COCO_val2014_000000393271.jpg", "query": "Is it still snowing in the picture?"}, {"pid": "91", "raw_id": 393271001, "question": "Does this appear to be a photo of multiple exposures of the black clad snowboarder?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393271.jpg", "query": "Does this appear to be a photo of multiple exposures of the black clad snowboarder?"}, {"pid": "92", "raw_id": 393271002, "question": "How many people can you see in the picture?", "answer": "8", "answer_mc": "8", "answer_gt": ["8", "8", "8", "8", "8", "8", "6", "6"], "image": "images/COCO_val2014_000000393271.jpg", "query": "How many people can you see in the picture?"}, {"pid": "93", "raw_id": 262200000, "question": "Is this an adult party?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262200.jpg", "query": "Is this an adult party?"}, {"pid": "94", "raw_id": 262200001, "question": "What is being celebrated?", "answer": "birthday", "answer_mc": "birthday", "answer_gt": ["birthday", "birthday", "birthday", "birthday", "birthday", "birthday", "birthday", "birthday", "birthday"], "image": "images/COCO_val2014_000000262200.jpg", "query": "What is being celebrated?"}, {"pid": "95", "raw_id": 262200002, "question": "Who is in front of the cake with candles?", "answer": "boy", "answer_mc": "boy", "answer_gt": ["cup", "birthday boy", "woman", "little girl", "boy"], "image": "images/COCO_val2014_000000262200.jpg", "query": "Who is in front of the cake with candles?"}, {"pid": "96", "raw_id": 393274000, "question": "What is this vehicle?", "answer": "train", "answer_mc": "train", "answer_gt": ["train", "train", "train", "train", "train", "yes", "train", "train", "train", "bus"], "image": "images/COCO_val2014_000000393274.jpg", "query": "What is this vehicle?"}, {"pid": "97", "raw_id": 393274001, "question": "Can you see the hook up for the train?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "yes", "no", "no", "yes", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393274.jpg", "query": "Can you see the hook up for the train?"}, {"pid": "98", "raw_id": 393274002, "question": "Is this train at the station?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "no", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393274.jpg", "query": "Is this train at the station?"}, {"pid": "99", "raw_id": 393274003, "question": "What is cast?", "answer": "shadow", "answer_mc": "shadow", "answer_gt": ["shadows", "human", "shadow", "sun"], "image": "images/COCO_val2014_000000393274.jpg", "query": "What is cast?"}, {"pid": "100", "raw_id": 393274004, "question": "What is the background metal structure?", "answer": "platform", "answer_mc": "platform", "answer_gt": ["trees", "station", "awning", "platform", "platform", "platform", "roof", "train stop", "awning"], "image": "images/COCO_val2014_000000393274.jpg", "query": "What is the background metal structure?"}, {"pid": "101", "raw_id": 393274005, "question": "Is there smoke coming out of the train?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393274.jpg", "query": "Is there smoke coming out of the train?"}, {"pid": "102", "raw_id": 393274006, "question": "Is this station in a rural setting?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393274.jpg", "query": "Is this station in a rural setting?"}, {"pid": "103", "raw_id": 393274007, "question": "Are the train tracks clear?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "no", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393274.jpg", "query": "Are the train tracks clear?"}, {"pid": "104", "raw_id": 131131000, "question": "What type of cat is this?", "answer": "tabby", "answer_mc": "tabby", "answer_gt": ["tabby", "tabby", "tabby", "house cat", "tabby"], "image": "images/COCO_val2014_000000131131.jpg", "query": "What type of cat is this?"}, {"pid": "105", "raw_id": 131131001, "question": "What is written lg in the pic?", "answer": "monitor", "answer_mc": "monitor", "answer_gt": ["tv", "screen", "tv", "monitor", "monitor", "nothing", "white"], "image": "images/COCO_val2014_000000131131.jpg", "query": "What is written lg in the pic?"}, {"pid": "106", "raw_id": 131131002, "question": "How many cats are in the image?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "1", "1", "2"], "image": "images/COCO_val2014_000000131131.jpg", "query": "How many cats are in the image?"}, {"pid": "107", "raw_id": 393277000, "question": "Are there any tour buses?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Are there any tour buses?"}, {"pid": "108", "raw_id": 393277001, "question": "What's the statue of?", "answer": "clock tower", "answer_mc": "clock tower", "answer_gt": ["clock tower", "nothing", "person", "clock tower", "clock building", "clock tower"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What's the statue of?"}, {"pid": "109", "raw_id": 393277002, "question": "Are all three cars facing the same direction?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "yes", "yes", "no", "no"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Are all three cars facing the same direction?"}, {"pid": "110", "raw_id": 393277003, "question": "What year is the car?", "answer": "2010", "answer_mc": "2010", "answer_gt": ["unknown"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What year is the car?"}, {"pid": "111", "raw_id": 393277004, "question": "What color is the sky?", "answer": "blue", "answer_mc": "blue", "answer_gt": ["blue", "blue", "blue", "blue, gray", "blue", "blue", "blue", "blue", "blue", "blue"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What color is the sky?"}, {"pid": "112", "raw_id": 393277005, "question": "What time is it on the clock?", "answer": "8:35", "answer_mc": "8:35", "answer_gt": ["830", "9:35", "6", "738", "8:35", "7", "8:35"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What time is it on the clock?"}, {"pid": "113", "raw_id": 393277006, "question": "Are there a lot of people milling around?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Are there a lot of people milling around?"}, {"pid": "114", "raw_id": 393277007, "question": "Is the building large?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Is the building large?"}, {"pid": "115", "raw_id": 393277008, "question": "How is the weather?", "answer": "cloudy", "answer_mc": "cloudy", "answer_gt": ["clear", "clear", "gloomy", "cloudy", "cloudy", "cloudy", "cloudy"], "image": "images/COCO_val2014_000000393277.jpg", "query": "How is the weather?"}, {"pid": "116", "raw_id": 393277009, "question": "What color is the building the background?", "answer": "gray", "answer_mc": "gray", "answer_gt": ["beige", "tan", "white", "gray", "gray"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What color is the building the background?"}, {"pid": "117", "raw_id": 393277010, "question": "What time is it?", "answer": "8:35", "answer_mc": "8:35", "answer_gt": ["8:35", "8:35", "9:35", "8:35", "7:45", "8:35", "8:35", "8:35"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What time is it?"}, {"pid": "118", "raw_id": 393277011, "question": "Does it rain here a lot?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Does it rain here a lot?"}, {"pid": "119", "raw_id": 393277012, "question": "Which famous landmark appears in the background?", "answer": "big ben", "answer_mc": "big ben", "answer_gt": ["clock tower", "big ben", "unknown", "big ben"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Which famous landmark appears in the background?"}, {"pid": "120", "raw_id": 393277013, "question": "Is that a cake?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Is that a cake?"}, {"pid": "121", "raw_id": 393277014, "question": "Is this a one way street?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "yes"], "image": "images/COCO_val2014_000000393277.jpg", "query": "Is this a one way street?"}, {"pid": "122", "raw_id": 393277015, "question": "What color is the dome of the building off in the distance?", "answer": "red", "answer_mc": "red", "answer_gt": ["red", "red", "brown", "red"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What color is the dome of the building off in the distance?"}, {"pid": "123", "raw_id": 393277016, "question": "What is in the tower?", "answer": "clock", "answer_mc": "clock", "answer_gt": ["clock", "clock", "clock", "clock", "clock", "clock", "clock", "clock", "clock"], "image": "images/COCO_val2014_000000393277.jpg", "query": "What is in the tower?"}, {"pid": "124", "raw_id": 393282000, "question": "Is the giraffe a baby?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "no", "yes", "no", "yes"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Is the giraffe a baby?"}, {"pid": "125", "raw_id": 393282001, "question": "How many giraffes are there?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000393282.jpg", "query": "How many giraffes are there?"}, {"pid": "126", "raw_id": 393282002, "question": "Are the animals in the shade?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Are the animals in the shade?"}, {"pid": "127", "raw_id": 393282003, "question": "How many giraffes can been seen?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000393282.jpg", "query": "How many giraffes can been seen?"}, {"pid": "128", "raw_id": 393282004, "question": "Are the animals in the wild?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "yes", "no", "yes", "yes"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Are the animals in the wild?"}, {"pid": "129", "raw_id": 393282005, "question": "Why is there a white roofed open pavilion?", "answer": "for shade", "answer_mc": "for shade", "answer_gt": ["shade", "tent", "for shade", "shade", "let in sun", "yes"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Why is there a white roofed open pavilion?"}, {"pid": "130", "raw_id": 393282006, "question": "Will these giraffes be eating the grass for dinner?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Will these giraffes be eating the grass for dinner?"}, {"pid": "131", "raw_id": 393282007, "question": "Do you see a body of water in the picture?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Do you see a body of water in the picture?"}, {"pid": "132", "raw_id": 393282008, "question": "What is in front of the giraffe?", "answer": "dirt", "answer_mc": "dirt", "answer_gt": ["dirt", "canopy", "nothing", "dirt", "shelter", "photographer"], "image": "images/COCO_val2014_000000393282.jpg", "query": "What is in front of the giraffe?"}, {"pid": "133", "raw_id": 393282009, "question": "How many giraffe are pictured?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000393282.jpg", "query": "How many giraffe are pictured?"}, {"pid": "134", "raw_id": 393282010, "question": "Where do these animals live?", "answer": "zoo", "answer_mc": "zoo", "answer_gt": ["at zoo", "zoo", "africa", "plains", "sahara", "africa"], "image": "images/COCO_val2014_000000393282.jpg", "query": "Where do these animals live?"}, {"pid": "135", "raw_id": 393282011, "question": "What are these animals?", "answer": "giraffes", "answer_mc": "giraffes", "answer_gt": ["giraffes", "2", "giraffes", "giraffes", "giraffe", "giraffe", "giraffes", "giraffes", "griffes"], "image": "images/COCO_val2014_000000393282.jpg", "query": "What are these animals?"}, {"pid": "136", "raw_id": 393284000, "question": "Is this a beach scene?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is this a beach scene?"}, {"pid": "137", "raw_id": 393284001, "question": "What kind of object is the kid riding?", "answer": "snowboard", "answer_mc": "snowboard", "answer_gt": ["snowboard", "snowboard", "snowboard", "snowboard", "snowboard", "snowboard", "snowboard", "snowboard", "snowboard"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What kind of object is the kid riding?"}, {"pid": "138", "raw_id": 393284002, "question": "What is the this person doing?", "answer": "snowboarding", "answer_mc": "snowboarding", "answer_gt": ["snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What is the this person doing?"}, {"pid": "139", "raw_id": 393284003, "question": "What sport is the man doing?", "answer": "snowboarding", "answer_mc": "snowboarding", "answer_gt": ["snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What sport is the man doing?"}, {"pid": "140", "raw_id": 393284004, "question": "Is this man snowboarding?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is this man snowboarding?"}, {"pid": "141", "raw_id": 393284005, "question": "Is there any snow on the trees?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "yes", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is there any snow on the trees?"}, {"pid": "142", "raw_id": 393284006, "question": "Is this person flying?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "yes", "yes", "no", "no", "no"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is this person flying?"}, {"pid": "143", "raw_id": 393284007, "question": "Is this man flying off of the ski slope?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no", "no", "yes", "yes", "hard to tell"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is this man flying off of the ski slope?"}, {"pid": "144", "raw_id": 393284008, "question": "What is the person wearing?", "answer": "ski gear", "answer_mc": "ski gear", "answer_gt": ["snow suit and snowboard", "pants and jacket", "snow clothes", "winter clothes", "ski jacket/pants/gloves/boots", "ski gear", "skiing gear", "winter wear", "snow pants"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What is the person wearing?"}, {"pid": "145", "raw_id": 393284009, "question": "What is the man doing?", "answer": "snowboarding", "answer_mc": "snowboarding", "answer_gt": ["snowboarding", "skiing", "snowboarding", "snowboarding", "skateboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What is the man doing?"}, {"pid": "146", "raw_id": 393284010, "question": "What pattern is painted on the helmet?", "answer": "no helmet", "answer_mc": "no helmet", "answer_gt": ["no helmet", "none", "solid"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What pattern is painted on the helmet?"}, {"pid": "147", "raw_id": 393284011, "question": "Is he going to land?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Is he going to land?"}, {"pid": "148", "raw_id": 393284012, "question": "What sport is the man participating in?", "answer": "snowboarding", "answer_mc": "snowboarding", "answer_gt": ["skateboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "snowboarding", "skiing", "snowboarding", "snowboarding", "snowboarding"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What sport is the man participating in?"}, {"pid": "149", "raw_id": 393284013, "question": "What is in the air?", "answer": "snowboarder", "answer_mc": "snowboarder", "answer_gt": ["snowboarder", "skier", "person", "snowboarder", "snowboarder", "snow", "snowboarder", "snowboarder", "person on snowboard"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What is in the air?"}, {"pid": "150", "raw_id": 393284014, "question": "Does the man have goggles on?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "no", "yes", "no", "no", "yes", "yes"], "image": "images/COCO_val2014_000000393284.jpg", "query": "Does the man have goggles on?"}, {"pid": "151", "raw_id": 393284015, "question": "What color is the man's jacket?", "answer": "red", "answer_mc": "red", "answer_gt": ["red", "red", "red", "orange", "red", "red", "red", "red", "red", "red"], "image": "images/COCO_val2014_000000393284.jpg", "query": "What color is the man's jacket?"}, {"pid": "152", "raw_id": 393288000, "question": "What kind of building is this?", "answer": "church", "answer_mc": "church", "answer_gt": ["church", "church", "church", "church", "church", "cathedral", "church", "church", "church", "church"], "image": "images/COCO_val2014_000000393288.jpg", "query": "What kind of building is this?"}, {"pid": "153", "raw_id": 393288001, "question": "What color is the dome shaped roof in the back?", "answer": "gray", "answer_mc": "gray", "answer_gt": ["brown", "red", "black", "gray", "blue"], "image": "images/COCO_val2014_000000393288.jpg", "query": "What color is the dome shaped roof in the back?"}, {"pid": "154", "raw_id": 393288002, "question": "What color is the church?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["brown", "brown", "red brick and white", "brown", "red", "brown", "red", "brown", "brown"], "image": "images/COCO_val2014_000000393288.jpg", "query": "What color is the church?"}, {"pid": "155", "raw_id": 393288003, "question": "Where are the cars?", "answer": "street", "answer_mc": "street", "answer_gt": ["in front of church", "street", "on street", "street", "street", "on road", "parked on street", "on street", "street"], "image": "images/COCO_val2014_000000393288.jpg", "query": "Where are the cars?"}, {"pid": "156", "raw_id": 393288004, "question": "What is displayed under the triangular dome?", "answer": "windows", "answer_mc": "windows", "answer_gt": ["windows", "windows", "windows", "cross"], "image": "images/COCO_val2014_000000393288.jpg", "query": "What is displayed under the triangular dome?"}, {"pid": "157", "raw_id": 524361000, "question": "Where is the bird?", "answer": "on metal", "answer_mc": "on metal", "answer_gt": ["in tree", "in machine", "on wheel"], "image": "images/COCO_val2014_000000524361.jpg", "query": "Where is the bird?"}, {"pid": "158", "raw_id": 524361001, "question": "How many birds?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000524361.jpg", "query": "How many birds?"}, {"pid": "159", "raw_id": 524361002, "question": "What color are the bird's feathers?", "answer": "gray", "answer_mc": "gray", "answer_gt": ["gray and black", "gray", "white gray and black", "gray", "gray", "gray", "gray", "gray and black", "black and white", "white, gray"], "image": "images/COCO_val2014_000000524361.jpg", "query": "What color are the bird's feathers?"}, {"pid": "160", "raw_id": 74000, "question": "Does this dog have a collar?", "answer": "no", "answer_mc": "no", "answer_gt": ["yes", "no", "no", "no", "no", "no", "no", "yes", "no"], "image": "images/COCO_val2014_000000000074.jpg", "query": "Does this dog have a collar?"}, {"pid": "161", "raw_id": 74001, "question": "Where is the dog laying?", "answer": "sidewalk", "answer_mc": "sidewalk", "answer_gt": ["outside", "sidewalk", "sidewalk", "sidewalk", "sidewalk", "sidewalk", "ground", "street", "yes", "sidewalk"], "image": "images/COCO_val2014_000000000074.jpg", "query": "Where is the dog laying?"}, {"pid": "162", "raw_id": 74002, "question": "What is the dog doing?", "answer": "sleeping", "answer_mc": "sleeping", "answer_gt": ["laying down", "sleeping", "resting", "lying down", "it is sleeping", "sleeping", "laying down", "sleeping"], "image": "images/COCO_val2014_000000000074.jpg", "query": "What is the dog doing?"}, {"pid": "163", "raw_id": 131152000, "question": "Are any of these people wearing a wetsuit?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131152.jpg", "query": "Are any of these people wearing a wetsuit?"}, {"pid": "164", "raw_id": 131152001, "question": "How are the waves?", "answer": "small", "answer_mc": "small", "answer_gt": ["low", "small", "nice", "small", "1-2 ft", "low", "small", "small"], "image": "images/COCO_val2014_000000131152.jpg", "query": "How are the waves?"}, {"pid": "165", "raw_id": 131152002, "question": "How many people are here?", "answer": "5", "answer_mc": "5", "answer_gt": ["5", "5", "5", "5", "5", "5", "5", "5", "5", "5"], "image": "images/COCO_val2014_000000131152.jpg", "query": "How many people are here?"}, {"pid": "166", "raw_id": 131152003, "question": "Are these people wet?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131152.jpg", "query": "Are these people wet?"}, {"pid": "167", "raw_id": 262227000, "question": "Are there pill bottles?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262227.jpg", "query": "Are there pill bottles?"}, {"pid": "168", "raw_id": 262227001, "question": "What kind of sign is above the doorway?", "answer": "exit", "answer_mc": "exit", "answer_gt": ["exit", "exit", "exit", "exit", "exit", "exit", "neon", "exit", "exit", "exit"], "image": "images/COCO_val2014_000000262227.jpg", "query": "What kind of sign is above the doorway?"}, {"pid": "169", "raw_id": 262227002, "question": "Is the computer a popular model?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262227.jpg", "query": "Is the computer a popular model?"}, {"pid": "170", "raw_id": 262227003, "question": "What color is the ribbon?", "answer": "purple", "answer_mc": "purple", "answer_gt": ["purple", "purple", "purple", "purple", "purple", "purple"], "image": "images/COCO_val2014_000000262227.jpg", "query": "What color is the ribbon?"}, {"pid": "171", "raw_id": 262228000, "question": "What is the ground made of?", "answer": "bricks", "answer_mc": "bricks", "answer_gt": ["bricks", "bricks", "bricks", "bricks", "brick", "brick", "brick", "brick", "bricks"], "image": "images/COCO_val2014_000000262228.jpg", "query": "What is the ground made of?"}, {"pid": "172", "raw_id": 262228001, "question": "Why are the bicycles chained?", "answer": "bike rack", "answer_mc": "bike rack", "answer_gt": ["security", "bike rack", "not used", "not being ridden", "theft", "keep them from being stolen", "for security", "protection"], "image": "images/COCO_val2014_000000262228.jpg", "query": "Why are the bicycles chained?"}, {"pid": "173", "raw_id": 262228002, "question": "What type of vehicles are these?", "answer": "bikes", "answer_mc": "bikes", "answer_gt": ["bikes", "bikes", "bikes", "bikes", "bicycles", "bikes", "bicycle", "bikes", "bikes"], "image": "images/COCO_val2014_000000262228.jpg", "query": "What type of vehicles are these?"}, {"pid": "174", "raw_id": 262228003, "question": "Are these comfortable chairs?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262228.jpg", "query": "Are these comfortable chairs?"}, {"pid": "175", "raw_id": 262228004, "question": "Where are these bicycles chained at?", "answer": "bike rack", "answer_mc": "bike rack", "answer_gt": ["bike rack", "bike rack", "stand", "bike racks", "bike stand", "fence", "rack", "bike rack"], "image": "images/COCO_val2014_000000262228.jpg", "query": "Where are these bicycles chained at?"}, {"pid": "176", "raw_id": 262228005, "question": "What is on the ground?", "answer": "bricks", "answer_mc": "bricks", "answer_gt": ["bikes", "bricks", "bicycle", "bricks", "bicycles", "bicycles", "bike and bike racks", "bicycles", "bricks"], "image": "images/COCO_val2014_000000262228.jpg", "query": "What is on the ground?"}, {"pid": "177", "raw_id": 262229000, "question": "What is gliding in the air?", "answer": "nothing", "answer_mc": "nothing", "answer_gt": ["nothing", "ball", "nothing", "nothing", "ball", "nothing", "soccer ball", "hair"], "image": "images/COCO_val2014_000000262229.jpg", "query": "What is gliding in the air?"}, {"pid": "178", "raw_id": 262229001, "question": "What sport are the girls playing?", "answer": "soccer", "answer_mc": "soccer", "answer_gt": ["soccer", "soccer", "soccer", "soccer", "soccer", "soccer", "soccer", "soccer", "soccer", "soccer"], "image": "images/COCO_val2014_000000262229.jpg", "query": "What sport are the girls playing?"}, {"pid": "179", "raw_id": 262229002, "question": "Are the girls professionals?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "yes", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262229.jpg", "query": "Are the girls professionals?"}, {"pid": "180", "raw_id": 262229003, "question": "How many babies are there?", "answer": "0", "answer_mc": "0", "answer_gt": ["0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "image": "images/COCO_val2014_000000262229.jpg", "query": "How many babies are there?"}, {"pid": "181", "raw_id": 262229004, "question": "Are these men or women?", "answer": "women", "answer_mc": "women", "answer_gt": ["women", "women", "women", "women", "women", "women", "women", "women", "women", "women"], "image": "images/COCO_val2014_000000262229.jpg", "query": "Are these men or women?"}, {"pid": "182", "raw_id": 262229005, "question": "What are on the girls heads?", "answer": "headbands", "answer_mc": "headbands", "answer_gt": ["headbands", "headbands", "hair combs", "headbands", "hair band", "headbands", "headbands", "headbands", "headbands"], "image": "images/COCO_val2014_000000262229.jpg", "query": "What are on the girls heads?"}, {"pid": "183", "raw_id": 262229006, "question": "What game are they playing?", "answer": "soccer", "answer_mc": "soccer", "answer_gt": ["soccer", "soccer", "soccer", "soccer", "soccer", "soccer"], "image": "images/COCO_val2014_000000262229.jpg", "query": "What game are they playing?"}, {"pid": "184", "raw_id": 131159000, "question": "Where is the bear sitting?", "answer": "car floor", "answer_mc": "car floor", "answer_gt": ["car floor", "car floor", "in car", "mat in car", "yes", "floorboard", "floorboard", "car floor"], "image": "images/COCO_val2014_000000131159.jpg", "query": "Where is the bear sitting?"}, {"pid": "185", "raw_id": 131159001, "question": "Is the bear real?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000131159.jpg", "query": "Is the bear real?"}, {"pid": "186", "raw_id": 131159002, "question": "What is the color of the bear?", "answer": "yellow", "answer_mc": "yellow", "answer_gt": ["brown", "yellow", "yellow", "brown", "white", "yellow", "brown", "yellow", "tan", "yellow"], "image": "images/COCO_val2014_000000131159.jpg", "query": "What is the color of the bear?"}, {"pid": "187", "raw_id": 262235000, "question": "What type of facial expression does the woman have?", "answer": "smile", "answer_mc": "smile", "answer_gt": ["smile", "happy", "smiling", "happy", "smile", "smile", "smile", "smile", "happy", "smile"], "image": "images/COCO_val2014_000000262235.jpg", "query": "What type of facial expression does the woman have?"}, {"pid": "188", "raw_id": 262235001, "question": "What covers the window?", "answer": "curtain", "answer_mc": "curtain", "answer_gt": ["curtain", "curtain", "curtains", "curtain", "curtains", "curtain", "curtain", "curtain", "curtain"], "image": "images/COCO_val2014_000000262235.jpg", "query": "What covers the window?"}, {"pid": "189", "raw_id": 262235002, "question": "What color are the woman's nails?", "answer": "clear", "answer_mc": "clear", "answer_gt": ["pink", "natural", "red", "clear"], "image": "images/COCO_val2014_000000262235.jpg", "query": "What color are the woman's nails?"}, {"pid": "190", "raw_id": 262235003, "question": "What does the woman have around her neck?", "answer": "lanyard", "answer_mc": "lanyard", "answer_gt": ["necklace", "lanyard", "lanyard", "name tag", "necklace", "lanyard", "badge", "lanyard", "security tag"], "image": "images/COCO_val2014_000000262235.jpg", "query": "What does the woman have around her neck?"}, {"pid": "191", "raw_id": 262235004, "question": "Who is smiling?", "answer": "woman", "answer_mc": "woman", "answer_gt": ["woman", "girl", "woman", "girl in photo", "girl on cell phone", "woman", "lady", "woman", "woman", "woman"], "image": "images/COCO_val2014_000000262235.jpg", "query": "Who is smiling?"}, {"pid": "192", "raw_id": 262235005, "question": "Is this a woman or man?", "answer": "woman", "answer_mc": "woman", "answer_gt": ["woman", "woman", "woman", "woman", "woman", "woman", "woman", "woman", "woman", "woman"], "image": "images/COCO_val2014_000000262235.jpg", "query": "Is this a woman or man?"}, {"pid": "193", "raw_id": 262235006, "question": "What is the girl doing?", "answer": "talking on phone", "answer_mc": "talking on phone", "answer_gt": ["talking on phone", "talking on phone", "talking on phone", "talking", "talking on phone", "talking on phone", "on phone smiling", "talking", "talking"], "image": "images/COCO_val2014_000000262235.jpg", "query": "What is the girl doing?"}, {"pid": "194", "raw_id": 524382000, "question": "What brand of dogs are these?", "answer": "beagles", "answer_mc": "beagles", "answer_gt": ["beagles", "beagles", "beagle"], "image": "images/COCO_val2014_000000524382.jpg", "query": "What brand of dogs are these?"}, {"pid": "195", "raw_id": 524382001, "question": "What is keeping the dogs from running away?", "answer": "fence", "answer_mc": "fence", "answer_gt": ["fence", "fence", "fence", "fence", "fence", "fence", "french", "cage", "fence", "fence"], "image": "images/COCO_val2014_000000524382.jpg", "query": "What is keeping the dogs from running away?"}, {"pid": "196", "raw_id": 524382002, "question": "Are all the dogs looking in the same direction?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000524382.jpg", "query": "Are all the dogs looking in the same direction?"}, {"pid": "197", "raw_id": 262242000, "question": "What is the women's racket touching?", "answer": "ball", "answer_mc": "ball", "answer_gt": ["floor and ball", "ground and tennis ball", "ground", "ground", "court and ball", "ball", "ball", "ball", "ground/ball", "ball"], "image": "images/COCO_val2014_000000262242.jpg", "query": "What is the women's racket touching?"}, {"pid": "198", "raw_id": 262242001, "question": "What is stretched across the court?", "answer": "net", "answer_mc": "net", "answer_gt": ["net", "tennis net", "net", "net", "net", "net", "net", "net", "net"], "image": "images/COCO_val2014_000000262242.jpg", "query": "What is stretched across the court?"}, {"pid": "199", "raw_id": 262242002, "question": "Does the man enjoy tennis?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Does the man enjoy tennis?"}, {"pid": "200", "raw_id": 262242003, "question": "What color is the court?", "answer": "green", "answer_mc": "green", "answer_gt": ["green", "green", "green", "green", "green", "green", "green", "green", "green", "green"], "image": "images/COCO_val2014_000000262242.jpg", "query": "What color is the court?"}, {"pid": "201", "raw_id": 262242004, "question": "Are the players tired?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "no"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Are the players tired?"}, {"pid": "202", "raw_id": 262242005, "question": "Is the court blue?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Is the court blue?"}, {"pid": "203", "raw_id": 262242006, "question": "Is he playing doubles?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Is he playing doubles?"}, {"pid": "204", "raw_id": 262242007, "question": "Are both men wearing hats?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "yes"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Are both men wearing hats?"}, {"pid": "205", "raw_id": 262242008, "question": "Are these two men professional tennis players?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262242.jpg", "query": "Are these two men professional tennis players?"}, {"pid": "206", "raw_id": 131171000, "question": "Is this bread?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "there is bread"], "image": "images/COCO_val2014_000000131171.jpg", "query": "Is this bread?"}, {"pid": "207", "raw_id": 131171001, "question": "How many bins?", "answer": "2", "answer_mc": "2", "answer_gt": ["4", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000131171.jpg", "query": "How many bins?"}, {"pid": "208", "raw_id": 131171002, "question": "Does this look like a healthy meal?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "no", "no", "yes", "no"], "image": "images/COCO_val2014_000000131171.jpg", "query": "Does this look like a healthy meal?"}, {"pid": "209", "raw_id": 262161000, "question": "How many exhaust pipes are shown in this photo?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262161.jpg", "query": "How many exhaust pipes are shown in this photo?"}, {"pid": "210", "raw_id": 262161001, "question": "Is the silver bike new?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "yes", "no", "no"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Is the silver bike new?"}, {"pid": "211", "raw_id": 262161002, "question": "Is the grass bright green?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "no", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Is the grass bright green?"}, {"pid": "212", "raw_id": 262161003, "question": "What is the bike on?", "answer": "grass", "answer_mc": "grass", "answer_gt": ["grass", "grass", "grass", "grass", "ducati", "grass", "grass", "grass", "grass", "grass"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What is the bike on?"}, {"pid": "213", "raw_id": 262161004, "question": "Why is there a number on this vehicle?", "answer": "750", "answer_mc": "750", "answer_gt": ["model number", "750", "750"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Why is there a number on this vehicle?"}, {"pid": "214", "raw_id": 262161005, "question": "Why is there a number on the vehicle?", "answer": "750", "answer_mc": "750", "answer_gt": ["750", "engine size", "750", "750", "engine", "750"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Why is there a number on the vehicle?"}, {"pid": "215", "raw_id": 262161006, "question": "Who is the maker of the scooter?", "answer": "ducati", "answer_mc": "ducati", "answer_gt": ["ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Who is the maker of the scooter?"}, {"pid": "216", "raw_id": 262161007, "question": "What color is the bike on the right?", "answer": "gray", "answer_mc": "gray", "answer_gt": ["green, silver, and black", "green and silver", "silver and sea foam green", "gray", "gray", "green"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What color is the bike on the right?"}, {"pid": "217", "raw_id": 262161008, "question": "Does the bike in the foreground have a single or a double seat?", "answer": "single", "answer_mc": "single", "answer_gt": ["single", "single", "single", "single", "single", "single", "single", "single", "single"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Does the bike in the foreground have a single or a double seat?"}, {"pid": "218", "raw_id": 262161009, "question": "Is the blue container on the ground blocking the motorcycle?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Is the blue container on the ground blocking the motorcycle?"}, {"pid": "219", "raw_id": 262161010, "question": "What is that white thing behind the motorcyclist?", "answer": "car", "answer_mc": "car", "answer_gt": ["car", "car", "car", "car", "car", "car", "table"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What is that white thing behind the motorcyclist?"}, {"pid": "220", "raw_id": 262161011, "question": "What color is the bike?", "answer": "silver", "answer_mc": "silver", "answer_gt": ["white and green", "silver", "gray, green", "silver", "silver", "silver/green", "silver", "silver", "silver"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What color is the bike?"}, {"pid": "221", "raw_id": 262161012, "question": "How many wheels do you see?", "answer": "4", "answer_mc": "4", "answer_gt": ["4", "2", "4", "4", "2", "6"], "image": "images/COCO_val2014_000000262161.jpg", "query": "How many wheels do you see?"}, {"pid": "222", "raw_id": *********, "question": "How is this red bike called?", "answer": "motorcycle", "answer_mc": "motorcycle", "answer_gt": ["motorbike", "motorcycle", "motorcycle", "motorcycle", "motorcycle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ducati"], "image": "images/COCO_val2014_000000262161.jpg", "query": "How is this red bike called?"}, {"pid": "223", "raw_id": *********, "question": "Does this bike travel often?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "no", "no", "yes", "no"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Does this bike travel often?"}, {"pid": "224", "raw_id": *********, "question": "What color is the seat?", "answer": "black", "answer_mc": "black", "answer_gt": ["black", "black", "black", "black", "black", "black", "black", "black", "black", "black"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What color is the seat?"}, {"pid": "225", "raw_id": *********, "question": "What kind of a show does this look like it is despite the motorcycle?", "answer": "car", "answer_mc": "car", "answer_gt": ["car show", "vintage cars", "car show", "car", "car show", "car show", "car"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What kind of a show does this look like it is despite the motorcycle?"}, {"pid": "226", "raw_id": *********, "question": "Is this motorcycle propped up?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Is this motorcycle propped up?"}, {"pid": "227", "raw_id": 262161018, "question": "What number is on the motorcycle?", "answer": "750", "answer_mc": "750", "answer_gt": ["750", "750", "750", "750", "750", "750", "750", "750", "750", "750"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What number is on the motorcycle?"}, {"pid": "228", "raw_id": 262161019, "question": "What make is the bike?", "answer": "ducati", "answer_mc": "ducati", "answer_gt": ["ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati", "ducati"], "image": "images/COCO_val2014_000000262161.jpg", "query": "What make is the bike?"}, {"pid": "229", "raw_id": 262161020, "question": "Are both motorbikes the same size?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "almost", "yes", "no", "no", "yes", "no", "no"], "image": "images/COCO_val2014_000000262161.jpg", "query": "Are both motorbikes the same size?"}, {"pid": "230", "raw_id": 524392000, "question": "What color is the fridge?", "answer": "silver", "answer_mc": "silver", "answer_gt": ["gray or silver", "silver", "steel gray", "stainless", "gray", "silver", "stainless steel (gray)", "gray", "silver"], "image": "images/COCO_val2014_000000524392.jpg", "query": "What color is the fridge?"}, {"pid": "231", "raw_id": 524392001, "question": "Is the light on?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000524392.jpg", "query": "Is the light on?"}, {"pid": "232", "raw_id": 524392002, "question": "What is this appliance used for?", "answer": "refrigeration", "answer_mc": "refrigeration", "answer_gt": ["keep food", "refrigeration", "refrigeration", "keep food fresh", "keeping food cool/cold", "keep food cold", "storing food", "refrigeration", "cooling", "food storage"], "image": "images/COCO_val2014_000000524392.jpg", "query": "What is this appliance used for?"}, {"pid": "233", "raw_id": 262262000, "question": "Is the time correct?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "yes", "yes"], "image": "images/COCO_val2014_000000262262.jpg", "query": "Is the time correct?"}, {"pid": "234", "raw_id": 262262001, "question": "Is it an overcast day?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262262.jpg", "query": "Is it an overcast day?"}, {"pid": "235", "raw_id": 262262002, "question": "What type of building is this?", "answer": "clock tower", "answer_mc": "clock tower", "answer_gt": ["skyscraper", "clock tower", "clock tower", "church", "church", "clock"], "image": "images/COCO_val2014_000000262262.jpg", "query": "What type of building is this?"}, {"pid": "236", "raw_id": 393338000, "question": "What  2 colors are the flowers?", "answer": "red and yellow", "answer_mc": "red and yellow", "answer_gt": ["red and yellow", "red and yellow", "red and yellow", "red,yellow", "yellow & orange", "orange and yellow", "yellow and orange", "orange and yellow", "yellow and orange", "red and yellow"], "image": "images/COCO_val2014_000000393338.jpg", "query": "What  2 colors are the flowers?"}, {"pid": "237", "raw_id": 393338001, "question": "What kind of flowers are in the vase?", "answer": "tulips", "answer_mc": "tulips", "answer_gt": ["tulips", "tulips", "tulips", "tulips", "tulips", "tulips", "tulips", "tulips", "tulips"], "image": "images/COCO_val2014_000000393338.jpg", "query": "What kind of flowers are in the vase?"}, {"pid": "238", "raw_id": 393338002, "question": "What color is the vase?", "answer": "clear", "answer_mc": "clear", "answer_gt": ["clear", "clear", "clear", "red and yellow", "clear", "clear", "clear", "clear", "clear", "no color"], "image": "images/COCO_val2014_000000393338.jpg", "query": "What color is the vase?"}, {"pid": "239", "raw_id": 262274000, "question": "Who left their surfboard?", "answer": "lifeguard", "answer_mc": "lifeguard", "answer_gt": ["lifeguard", "lifeguard", "nobody", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard"], "image": "images/COCO_val2014_000000262274.jpg", "query": "Who left their surfboard?"}, {"pid": "240", "raw_id": 262274001, "question": "What is written on the surfboard?", "answer": "lifeguard", "answer_mc": "lifeguard", "answer_gt": ["lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard"], "image": "images/COCO_val2014_000000262274.jpg", "query": "What is written on the surfboard?"}, {"pid": "241", "raw_id": 262274002, "question": "What is written on the board?", "answer": "lifeguard", "answer_mc": "lifeguard", "answer_gt": ["lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard", "keracare lifeguard", "lifeguard", "lifeguard", "lifeguard", "lifeguard"], "image": "images/COCO_val2014_000000262274.jpg", "query": "What is written on the board?"}, {"pid": "242", "raw_id": 262274003, "question": "How many boards are there?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262274.jpg", "query": "How many boards are there?"}, {"pid": "243", "raw_id": 262274004, "question": "Is it going to rain?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262274.jpg", "query": "Is it going to rain?"}, {"pid": "244", "raw_id": 262274005, "question": "Is it low tide?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "yes", "yes", "yes", "no", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262274.jpg", "query": "Is it low tide?"}, {"pid": "245", "raw_id": 262274006, "question": "Are there palm trees on the board?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262274.jpg", "query": "Are there palm trees on the board?"}, {"pid": "246", "raw_id": 262274007, "question": "Is it cloudy?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000262274.jpg", "query": "Is it cloudy?"}, {"pid": "247", "raw_id": 262275000, "question": "How old is the girl?", "answer": "6", "answer_mc": "6", "answer_gt": ["6", "4", "6"], "image": "images/COCO_val2014_000000262275.jpg", "query": "How old is the girl?"}, {"pid": "248", "raw_id": 262275001, "question": "What color is the horse?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown"], "image": "images/COCO_val2014_000000262275.jpg", "query": "What color is the horse?"}, {"pid": "249", "raw_id": 262275002, "question": "Is the girl sitting on the horse correctly?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000262275.jpg", "query": "Is the girl sitting on the horse correctly?"}, {"pid": "250", "raw_id": 262275003, "question": "Is this an English saddle?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "no", "unknown"], "image": "images/COCO_val2014_000000262275.jpg", "query": "Is this an English saddle?"}, {"pid": "251", "raw_id": 262275004, "question": "What is on the little girls head?", "answer": "helmet", "answer_mc": "helmet", "answer_gt": ["helmet", "helmet", "helmet", "helmet", "helmet", "helmet", "helmet", "helmet", "helmet"], "image": "images/COCO_val2014_000000262275.jpg", "query": "What is on the little girls head?"}, {"pid": "252", "raw_id": 133000, "question": "What color is lamp?", "answer": "blue", "answer_mc": "blue", "answer_gt": ["blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue"], "image": "images/COCO_val2014_000000000133.jpg", "query": "What color is lamp?"}, {"pid": "253", "raw_id": 133001, "question": "Is this a child room?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000000133.jpg", "query": "Is this a child room?"}, {"pid": "254", "raw_id": 133002, "question": "What size mattress would you need for this bed?", "answer": "twin", "answer_mc": "twin", "answer_gt": ["twin", "twin", "twin", "single", "twin", "twin", "twin", "single", "twin"], "image": "images/COCO_val2014_000000000133.jpg", "query": "What size mattress would you need for this bed?"}, {"pid": "255", "raw_id": 131207000, "question": "How many birds?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000131207.jpg", "query": "How many birds?"}, {"pid": "256", "raw_id": 131207001, "question": "How many birds can be seen?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000131207.jpg", "query": "How many birds can be seen?"}, {"pid": "257", "raw_id": 131207002, "question": "Are the birds legs touching the water?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000131207.jpg", "query": "Are the birds legs touching the water?"}, {"pid": "258", "raw_id": 131207003, "question": "Does the water look calm?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000131207.jpg", "query": "Does the water look calm?"}, {"pid": "259", "raw_id": 131207004, "question": "What color are the birds?", "answer": "white", "answer_mc": "white", "answer_gt": ["white", "white", "white", "white", "white", "white", "white", "white", "white", "white"], "image": "images/COCO_val2014_000000131207.jpg", "query": "What color are the birds?"}, {"pid": "260", "raw_id": 131207005, "question": "What kind of birds are these?", "answer": "crane", "answer_mc": "crane", "answer_gt": ["herons", "heron", "egrets", "gulls"], "image": "images/COCO_val2014_000000131207.jpg", "query": "What kind of birds are these?"}, {"pid": "261", "raw_id": 131207006, "question": "What color is the bench?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown", "brown"], "image": "images/COCO_val2014_000000131207.jpg", "query": "What color is the bench?"}, {"pid": "262", "raw_id": 131207007, "question": "Are this flamingos?", "answer": "no", "answer_mc": "no", "answer_gt": ["yes", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000131207.jpg", "query": "Are this flamingos?"}, {"pid": "263", "raw_id": 136000, "question": "Is this in a museum?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "yes", "no", "no", "no", "no", "yes", "no"], "image": "images/COCO_val2014_000000000136.jpg", "query": "Is this in a museum?"}, {"pid": "264", "raw_id": 136001, "question": "How many animals are in the picture?", "answer": "2", "answer_mc": "2", "answer_gt": ["2", "2", "2", "2", "2", "2", "2", "2", "2", "2"], "image": "images/COCO_val2014_000000000136.jpg", "query": "How many animals are in the picture?"}, {"pid": "265", "raw_id": 136002, "question": "What kind of animal is shown?", "answer": "giraffe", "answer_mc": "giraffe", "answer_gt": ["giraffe", "giraffe", "giraffe", "brown", "giraffe", "giraffe", "giraffe", "giraffe", "giraffe", "giraffe"], "image": "images/COCO_val2014_000000000136.jpg", "query": "What kind of animal is shown?"}, {"pid": "266", "raw_id": 139000, "question": "What is the woman in the room doing?", "answer": "talking", "answer_mc": "talking", "answer_gt": ["talking to someone", "talking", "talking", "talking to someone outside of window", "talking", "talking out window"], "image": "images/COCO_val2014_000000000139.jpg", "query": "What is the woman in the room doing?"}, {"pid": "267", "raw_id": 139001, "question": "How many yellow vases?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000000139.jpg", "query": "How many yellow vases?"}, {"pid": "268", "raw_id": 139002, "question": "What color is the floor?", "answer": "brown", "answer_mc": "brown", "answer_gt": ["brown", "brown", "brown", "brown", "brown", "brown", "brown"], "image": "images/COCO_val2014_000000000139.jpg", "query": "What color is the floor?"}, {"pid": "269", "raw_id": 139003, "question": "What color is the flower?", "answer": "pink", "answer_mc": "pink", "answer_gt": ["pink", "pink", "pink", "pink", "red", "pink", "pink", "pink", "pink"], "image": "images/COCO_val2014_000000000139.jpg", "query": "What color is the flower?"}, {"pid": "270", "raw_id": 262284000, "question": "How many sets of doors are open?", "answer": "1", "answer_mc": "1", "answer_gt": ["1", "1", "0", "1", "1", "1", "1", "1", "1"], "image": "images/COCO_val2014_000000262284.jpg", "query": "How many sets of doors are open?"}, {"pid": "271", "raw_id": 262284001, "question": "What is this?", "answer": "shower", "answer_mc": "shower", "answer_gt": ["shower", "shower", "shower", "shower", "shower", "shower", "shower", "shower", "shower"], "image": "images/COCO_val2014_000000262284.jpg", "query": "What is this?"}, {"pid": "272", "raw_id": 262284002, "question": "Is the floor tilted?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "yes", "yes", "no", "no"], "image": "images/COCO_val2014_000000262284.jpg", "query": "Is the floor tilted?"}, {"pid": "273", "raw_id": 262284003, "question": "What room is this?", "answer": "bathroom", "answer_mc": "bathroom", "answer_gt": ["bathroom", "bathroom", "bathroom", "bathroom", "bathroom", "bathroom", "bathroom", "bathroom", "bathroom", "bathroom"], "image": "images/COCO_val2014_000000262284.jpg", "query": "What room is this?"}, {"pid": "274", "raw_id": 143000, "question": "What color is the tip of the birds' tails?", "answer": "yellow", "answer_mc": "yellow", "answer_gt": ["yellow", "yellow", "yellow", "yellow", "yellow", "yellow", "yellow", "yellow", "yellow", "yellow"], "image": "images/COCO_val2014_000000000143.jpg", "query": "What color is the tip of the birds' tails?"}, {"pid": "275", "raw_id": 143001, "question": "How many birds are in the tree?", "answer": "8", "answer_mc": "8", "answer_gt": ["8", "8", "8", "8", "8", "8", "8", "8", "8", "8"], "image": "images/COCO_val2014_000000000143.jpg", "query": "How many birds are in the tree?"}, {"pid": "276", "raw_id": 143002, "question": "Who many birds are black?", "answer": "0", "answer_mc": "0", "answer_gt": ["0", "0", "7", "0", "0", "0", "0", "0"], "image": "images/COCO_val2014_000000000143.jpg", "query": "Who many birds are black?"}, {"pid": "277", "raw_id": 240323000, "question": "Is there meat on this plate?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "turkey", "yes", "yes", "turkey", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000240323.jpg", "query": "Is there meat on this plate?"}, {"pid": "278", "raw_id": 240323001, "question": "Is this a vegetarian meal?", "answer": "no", "answer_mc": "no", "answer_gt": ["yes", "yes", "no", "no", "no", "yes", "yes", "no", "no"], "image": "images/COCO_val2014_000000240323.jpg", "query": "Is this a vegetarian meal?"}, {"pid": "279", "raw_id": 240323002, "question": "What is the green fruit called?", "answer": "avocado", "answer_mc": "avocado", "answer_gt": ["pears", "avocado", "avocado", "avocado", "avocado", "avocado", "avocado", "avocado", "avocado", "avocado"], "image": "images/COCO_val2014_000000240323.jpg", "query": "What is the green fruit called?"}, {"pid": "280", "raw_id": 524436000, "question": "Is there a woman sitting on this bench??", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000524436.jpg", "query": "Is there a woman sitting on this bench??"}, {"pid": "281", "raw_id": 524436001, "question": "Where are they sitting?", "answer": "bench", "answer_mc": "bench", "answer_gt": ["on bench", "on bench", "bench", "bench", "bench", "bench", "bench", "bench", "bench", "on bench"], "image": "images/COCO_val2014_000000524436.jpg", "query": "Where are they sitting?"}, {"pid": "282", "raw_id": 524436002, "question": "Are there cars in the picture?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000524436.jpg", "query": "Are there cars in the picture?"}, {"pid": "283", "raw_id": 524436003, "question": "What is the woman sitting on?", "answer": "bench", "answer_mc": "bench", "answer_gt": ["bench", "bench", "bench", "bench", "bench", "bench", "bench", "bench", "bench", "bench"], "image": "images/COCO_val2014_000000524436.jpg", "query": "What is the woman sitting on?"}, {"pid": "284", "raw_id": 524436004, "question": "Is it raining?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000524436.jpg", "query": "Is it raining?"}, {"pid": "285", "raw_id": 524436005, "question": "Are these two people a couple?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "probably", "yes", "yes"], "image": "images/COCO_val2014_000000524436.jpg", "query": "Are these two people a couple?"}, {"pid": "286", "raw_id": 393372000, "question": "Is her hair blue?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["no", "yes", "yes", "yes", "yes", "no", "yes", "no", "yes"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Is her hair blue?"}, {"pid": "287", "raw_id": 393372001, "question": "Can her eyes be seen?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "yes", "yes"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Can her eyes be seen?"}, {"pid": "288", "raw_id": 393372002, "question": "Is she wearing long sleeves?", "answer": "yes", "answer_mc": "yes", "answer_gt": ["yes", "yes", "yes", "yes", "yes", "yes", "no", "yes"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Is she wearing long sleeves?"}, {"pid": "289", "raw_id": 393372003, "question": "What color is the wig?", "answer": "purple", "answer_mc": "purple", "answer_gt": ["purple", "purple", "purple", "purple", "blue", "purple", "blue", "purple"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What color is the wig?"}, {"pid": "290", "raw_id": 393372004, "question": "Is the girl smiling?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Is the girl smiling?"}, {"pid": "291", "raw_id": 393372005, "question": "Is this a male or female?", "answer": "female", "answer_mc": "female", "answer_gt": ["female", "female", "male", "female", "female", "female", "female", "female"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Is this a male or female?"}, {"pid": "292", "raw_id": 393372006, "question": "Is this a man or woman?", "answer": "woman", "answer_mc": "woman", "answer_gt": ["woman", "woman", "woman", "woman", "woman", "woman", "woman", "woman"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Is this a man or woman?"}, {"pid": "293", "raw_id": 393372007, "question": "What facial piercing does the girl have?", "answer": "none", "answer_mc": "none", "answer_gt": ["none", "ear", "none", "none", "ears", "earring", "none"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What facial piercing does the girl have?"}, {"pid": "294", "raw_id": 393372008, "question": "Which  ear is pierced?", "answer": "right", "answer_mc": "right", "answer_gt": ["right", "right", "right", "right", "right", "right", "right", "right", "right", "right"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Which  ear is pierced?"}, {"pid": "295", "raw_id": 393372009, "question": "Does the woman look happy?", "answer": "no", "answer_mc": "no", "answer_gt": ["no", "no", "no", "no", "no", "no", "no", "no", "no", "no"], "image": "images/COCO_val2014_000000393372.jpg", "query": "Does the woman look happy?"}, {"pid": "296", "raw_id": 393372010, "question": "What color is her hair?", "answer": "blue", "answer_mc": "blue", "answer_gt": ["blue", "electric blue", "blue", "blue", "blue", "purple", "blue", "purple", "blue"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What color is her hair?"}, {"pid": "297", "raw_id": 393372011, "question": "What type of shirt is the girl wearing?", "answer": "button up", "answer_mc": "button up", "answer_gt": ["dress", "black", "official black", "collared", "button up"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What type of shirt is the girl wearing?"}, {"pid": "298", "raw_id": 393372012, "question": "What color is her eyes?", "answer": "blue", "answer_mc": "blue", "answer_gt": ["blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What color is her eyes?"}, {"pid": "299", "raw_id": 393372013, "question": "What color hair does the woman have?", "answer": "purple", "answer_mc": "purple", "answer_gt": ["blue", "blue", "purple", "purple", "blue", "purple", "purple", "purple", "blue"], "image": "images/COCO_val2014_000000393372.jpg", "query": "What color hair does the woman have?"}]