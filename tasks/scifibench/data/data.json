[{"image": "images/image_0.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. fer performance of the conventional stc-ofdm system for different time delays .\nB. fer performance of the conventional stc-ofdm system for different sirs .\nC. ber performance of tmp receiver with different number of iterations for otfs system .\nD. ber performance of several s-bf decoder as a functionof snr for code 2 .\nE. the performance of mpsk modulation schemes under different modulation orders .", "pid": 0, "answer": "A", "category": "cs.CL"}, {"image": "images/image_1.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. unweighted sum rates for the systems withe [ |hsr|2 ] = 1 , e [ |hsd|2 ] = 1 ande [ |hrd|2 ] = 5 .\nB. unweighted sum rates for the systems with extra direct-link transmission , ande [ |hsr|2 ] = 1 , e [ |hsd|2 ] = 1 , e [ |hrd|2 ] = 5 .\nC. unweighted sum rates for the systems with extra direct-link transmission , ande [ |hsr|2 ] = 5 , e [ |hsd|2 ] = 1 , e [ |hrd|2 ] = 1 .\nD. unweighted sum rates for the systems with extra direct-link transmission , ande [ |hsr|2 ] = 3 , e [ |hsd|2 ] = 1 , e [ |hrd|2 ] = 3 .\nE. unweighted sum rates for the systems withe [ |hsr|2 ] = , e [ |hsd|2 ] = 1 ande [ |hrd|2 ] = .", "pid": 1, "answer": "C", "category": "cross-list"}, {"image": "images/image_2.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the error performance of the ( 16129 , 1437 ) qc-ldpc code given in example over the awgn channel .\nB. the error performance of the ( 3969 , 365 ) qc-ldpc code given in example over the awgn channel .\nC. the error performances of the ( 399 , 3243 ) qc-ldpc code given in example over the awgn channel decoded with the ibmpda and the spa .\nD. the error performance of the ( 6486 , 219 ) qc-ldpc code given in example over the awgn channel .\nE. the error performance of the ( 1096 , 10587 ) qc-ldpc code given in example over the awgn channel .", "pid": 2, "answer": "E", "category": "cross-list"}, {"image": "images/image_3.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the error performance of the ( 3969 , 365 ) qc-ldpc code given in example over the awgn channel .\nB. the error performance of the ( 1096 , 10587 ) qc-ldpc code given in example over the awgn channel .\nC. the error performances of the ( 399 , 3243 ) qc-ldpc code given in example over the awgn channel decoded with the ibmpda and the spa .\nD. the error performance of the ( 6486 , 219 ) qc-ldpc code given in example over the awgn channel .\nE. the error performance of the ( 16129 , 1437 ) qc-ldpc code given in example over the awgn channel .", "pid": 3, "answer": "A", "category": "cross-list"}, {"image": "images/image_4.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. simulation for the improved method ( nnsizze=100 , sc=50 , sum-squared error of 1x10^ ( -6 ) ) .\nB. simulation for the improved method ( nnsizze=30 , sc=50 , sum-squared error of 1x10^ ( - ) ) .\nC. simulation for the improvedl method ( nnsizze=100 , sc=100 , sum-squared error of 1x10^ ( -10 ) ) .\nD. simulation for the conventional method ( nnsizze=100 , sc=100 , sum-squared error of 1x10^ ( -10 ) ) .\nE. simulation for the conventional method ( nnsizze=0 , sc=30 , sum-squared error of 1x10^ ( -6 ) ) .", "pid": 4, "answer": "A", "category": "other cs"}, {"image": "images/image_5.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. error after applying the kalman filter .\nB. error after applying the neural network .\nC. error after applying the 135 sample wiener filter .\nD. error while training the network .\nE. error after applying the original wiener filter .", "pid": 5, "answer": "B", "category": "other cs"}, {"image": "images/image_6.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the total distance measure with different window sizes on evolving graph 2 .\nB. the total distance measure with different window sizes on evolving graph 1 .\nC. the total distance measure with different window sizes on evolving graph 4 .\nD. the total distance measure with different window sizes on evolving graph 5 .\nE. the total distance measure with different window sizes on evolving graph 3 .", "pid": 6, "answer": "A", "category": "cross-list"}, {"image": "images/image_7.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the total distance per graph measure with different window sizes on evolving graph 5 .\nB. the total distance per graph measure with different window sizes on evolving graph 1 .\nC. the total distance per graph measure with different window sizes on evolving graph 3 .\nD. the total distance per graph measure with different window sizes on evolving graph 4 .\nE. the total distance per graph measure with different window sizes on evolving graph 2 .", "pid": 7, "answer": "E", "category": "cross-list"}, {"image": "images/image_8.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mcb algorithms – maximum yield vs. slack for large problem instances .\nB. mcb algorithms – average yield vs. slack for large problem instances .\nC. maximum yield vs. slack for large problem instances .\nD. mcb algorithms – minimum yield vs. slack for small problem instances .\nE. mcb algorithms – average yield vs. slack for small problem instances .", "pid": 8, "answer": "D", "category": "cs.DC"}, {"image": "images/image_9.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mcb algorithms – average yield vs. slack for small problem instances .\nB. mcb algorithms – maximum yield vs. slack for small problem instances .\nC. minimum yield vs. slack for large problem instances .\nD. mcb algorithms – average yield vs. slack for large problem instances .\nE. mcb algorithms – minimum yield vs. slack for large problem instances .", "pid": 9, "answer": "E", "category": "cs.DC"}, {"image": "images/image_10.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mcb algorithms – minimum yield vs. slack for small problem instances .\nB. mcb algorithms – average yield vs. slack for large problem instances .\nC. mcb algorithms – minimum yield vs. slack for large problem instances .\nD. mcb algorithms – maximum yield vs. slack for small problem instances .\nE. average yield vs. slack for small problem instances .", "pid": 10, "answer": "B", "category": "cs.DC"}, {"image": "images/image_11.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. network usage progression with synthetic heavy weight workload and locally varying churn .\nB. expected lookup time progression with synthetic heavy weight workload and locally varying churn .\nC. expected lookup time progression with synthetic heavy weight workload and high churn .\nD. expected lookup time progression with synthetic heavy weight workload and low churn .\nE. expected lookup time progression with synthetic heavy weight workload and temporally varying churn .", "pid": 11, "answer": "E", "category": "cs.DC"}, {"image": "images/image_12.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. expected lookup time progression with synthetic heavy weight workload and temporally varying churn .\nB. expected lookup time progression with synthetic heavy weight workload and locally varying churn .\nC. maintenance-interval progression with synthetic heavy weight workload and low churn .\nD. expected lookup time progression with synthetic heavy weight workload and high churn .\nE. expected lookup time progression with synthetic heavy weight workload and low churn .", "pid": 12, "answer": "E", "category": "cs.DC"}, {"image": "images/image_13.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. expected lookup time progression with synthetic heavy weight workload and high churn .\nB. network usage progression with synthetic heavy weight workload and high churn .\nC. expected lookup time progression with synthetic heavy weight workload and temporally varying churn .\nD. expected lookup time progression with synthetic heavy weight workload and low churn .\nE. expected lookup time progression with synthetic heavy weight workload and locally varying churn .", "pid": 13, "answer": "A", "category": "cs.DC"}, {"image": "images/image_14.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. network usage progression with synthetic heavy weight workload and locally varying churn .\nB. expected lookup time progression with synthetic heavy weight workload and high churn .\nC. expected lookup time progression with synthetic heavy weight workload and low churn .\nD. expected lookup time progression with synthetic heavy weight workload and temporally varying churn .\nE. expected lookup time progression with synthetic heavy weight workload and locally varying churn .", "pid": 14, "answer": "E", "category": "cs.DC"}, {"image": "images/image_15.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. snr vs. the number of modules for classical modular method and our proposed optimized modular method for s & h interpolation .\nB. snr vs. the number of modules for classical modular method and our proposed optimized modular method for linear interpolati n .\nC. snr vs. the number of modules for classical modular method and our proposed optimized method for s & h interpolation .\nD. snr vs. the number of modules for classical modular method and our proposed optimized method for s & h interpolation for 2-d signals .\nE. snr vs. the number of modules for classical modular method and our proposed optimized modular method for opt. interpolation .", "pid": 15, "answer": "B", "category": "other cs"}, {"image": "images/image_16.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ser performance at 8 bpcu for codes for× systems .\nB. ser performance at 4 bpcu for codes for4× systems .\nC. ser performance at 6 bpcu for codes for× 3 systems .\nD. ser performance at 4 bpcu for codes for8× 2 systems .\nE. ser performance at 8 bpcu for codes for4× 4 systems .", "pid": 16, "answer": "E", "category": "cross-list"}, {"image": "images/image_17.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. a graph showing the value ofǫ satisfied by a givenk if δ ≤ 10− with varying sampling probabilities .\nB. a graph showing the relationship between the values of k needed to achieve roughly the sameδ if we doubleβ .\nC. a graph showing the relationship betweenǫ and 1 δ if we vary the values ofk under fixed β .\nD. a graph showing the relationship betweenǫ and 1 δ if we vary the values ofβ under fixed k .\nE. a graph showing the relationship betweenǫ and 1 δ with small k ’ s , varying k and fixing β .", "pid": 17, "answer": "D", "category": "cs.CR"}, {"image": "images/image_18.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. end-to-end bit error rate versus snr of the fscr scheme using a df transmission in the ln case fork = 2 and k = 4 .\nB. comparisons of error performance versus snr of the fscr and dsc schemes using a df transmission in the ln case whenk = 2 andk = ..\nC. error rate incurred in the detection of the pss vs. snr for θ = 0 .\nD. end-to-end bit error-rate versus snr of the sr schemeusing a df transmission in the ln case whenk = 2 and k = 4 .\nE. end-to-end bit error rate versus snr of the dsc schemeusing a df transmission in the ln case whenk = and k = 4 .", "pid": 18, "answer": "E", "category": "other cs"}, {"image": "images/image_19.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the number of citations , ∑p i=h+1 ci , and its upper bound , ( p − h ) h , for authors a through f .\nB. the number of citations , ∑g i=h+1 ci , and its upper bounds , ( g − h ) h , and ( g − h ) g , for authors a through f .\nC. the g-index and its upper bounds given by lemma 1 and theorem for authors a through f .\nD. the h-index and its lower bounds given by theorem 1 , theorem and theorem 4 for authors a through f .\nE. the number of citations , ∑p i=g+1 ci , and its upper bound , ( p − g ) h , for authors a through f .", "pid": 19, "answer": "B", "category": "other cs"}, {"image": "images/image_20.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the number of citations , ∑g i=h+1 ci , and its upper bounds , ( g − h ) h , and ( g − h ) g , for authors a through f .\nB. the number of citations , ∑p i=g+1 ci , and its upper bound , ( p − g ) h , for authors a through f .\nC. the number of citations , ∑p i=h+1 ci , and its upper bound , ( p − h ) h , for authors a through f .\nD. the values ofh2 , g2 , andg2 −h2 as a function of the total number of citations , where journals are arranged according to increasing number of citations .\nE. the h-index and its lower bounds given by theorem 1 , theorem and theorem 4 for authors a through f .", "pid": 20, "answer": "C", "category": "other cs"}, {"image": "images/image_21.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the number of citations , ∑p i=h+1 ci , and its upper bound , ( p − h ) h , for authors a through f .\nB. the g-index and its upper bounds given by lemma 1 and theorem for authors a through f .\nC. the h-index and its lower bounds given by theorem 1 , theorem and theorem 4 for authors a through f .\nD. the number of citations , ∑g i=h+1 ci , and its upper bounds , ( g − h ) h , and ( g − h ) g , for authors a through f .\nE. the number of citations , ∑p i=g+1 ci , and its upper bound , ( p − g ) h , for authors a through f .", "pid": 21, "answer": "E", "category": "other cs"}, {"image": "images/image_22.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. speedup obtained in model problem with 135 , 005 , 697 dofs .\nB. experimental vs. ideal speed up for problem 1 .\nC. experimental speed up for problem2 .\nD. experimental speed up for problem 1 .\nE. temperature experiments , with ϵ = 0.05 .", "pid": 22, "answer": "B", "category": "cs.SE"}, {"image": "images/image_23.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. met prediction using extended met model in comparison with shoulder static models .\nB. met prediction using extended met model in comparison with hip/back static models .\nC. comparison of equation-based and model-based prediction of braking distance .\nD. met prediction using extended met model in comparison with elbow static models .\nE. met prediction using extended met model in comparison with general static models .", "pid": 23, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_24.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. met prediction using extended met model in comparison with shoulder static models .\nB. performance comparison between lightweight models and unconstrained models .\nC. met prediction using extended met model in comparison with hip/back static models .\nD. met prediction using extended met model in comparison with elbow static models .\nE. met prediction using extended met model in comparison with general static models .", "pid": 24, "answer": "A", "category": "cs.R<PERSON>"}, {"image": "images/image_25.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. probability of detection vs. probability of false alarm for n = 2 , m = 8 , l = 1 .\nB. probability of detection vs. probability of false alarm for n = 8 , m = 2 , l = 1 .\nC. probability of detection vs. probability of false alarm for n = 8 , m = 8 , l = 1 .\nD. probability of detection vs. probability of false alarm for n = 2 , m = 2 , l = 1 .\nE. probability of detection vs. probability of false alarm for n = , m = 4 , l = 1 .", "pid": 25, "answer": "A", "category": "cross-list"}, {"image": "images/image_26.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pdr of 30 nodes using tcp .\nB. pdr in presence of three malicious node .\nC. pdr of 60 nodes using tcp .\nD. pdr in presence of two malicious node .\nE. pdr in presence of one malicious node .", "pid": 26, "answer": "B", "category": "cs.CR"}, {"image": "images/image_27.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. wcrt comparison : 4 processors .\nB. wcrt comparison : 16 processors .\nC. success ratio : 16 processors .\nD. wcrt comparison : 8 processors .\nE. success ratio : 8 processors .", "pid": 27, "answer": "B", "category": "other cs"}, {"image": "images/image_28.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. success ratio : 4 processors .\nB. success ratio : 2 processors .\nC. wcrt comparison : 4 processors .\nD. success ratio : 8 processors .\nE. success ratio : 16 processors .", "pid": 28, "answer": "B", "category": "other cs"}, {"image": "images/image_29.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. success ratio : 16 processors .\nB. wcrt comparison : 4 processors .\nC. success ratio : 2 processors .\nD. success ratio : 4 processors .\nE. success ratio : 8 processors .", "pid": 29, "answer": "D", "category": "other cs"}, {"image": "images/image_30.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. snri versus l for feds algorithm .\nB. snri versus l for fap algorithm .\nC. snri versus µ for feds algorithm .\nD. snri versus m for feds algorithm .\nE. snri versus µ for fap algorithm .", "pid": 30, "answer": "A", "category": "other cs"}, {"image": "images/image_31.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pk−l+1−t is above l and pk+u−l−t is above l .\nB. pk−l+1−t is below l and pk+u−l−t is above l .\nC. pk−l+1+t is above while pk+t is below l .\nD. pk−l+1+t is below l and pk+t is below l ′ .\nE. pk−l+1−t is below l and pk+u−l−t is also below l .", "pid": 31, "answer": "B", "category": "other cs"}, {"image": "images/image_32.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pk−l+1+t is below l and pk+t is below l ′ .\nB. pk−l+1−t is above l and pk+u−l−t is above l .\nC. pk−l+1+t is above while pk+t is below l .\nD. pk−l+1−t is below l and pk+u−l−t is above l .\nE. pk−l+1−t is below l and pk+u−l−t is also below l .", "pid": 32, "answer": "E", "category": "other cs"}, {"image": "images/image_33.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pk−l+1−t is below l and pk+u−l−t is above l .\nB. both pk−l+1+t and pk+t are above l .\nC. pk−l+1+t is below l and pk+t is above l ′ .\nD. pk−l+1+t is above while pk+t is below l .\nE. pk−l+1+t is below l and pk+t is below l ′ .", "pid": 33, "answer": "B", "category": "other cs"}, {"image": "images/image_34.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. both pk−l+1+t and pk+t are above l .\nB. pk−l+1−t is below l and pk+u−l−t is above l .\nC. pk−l+1+t is below l and pk+t is below l ′ .\nD. pk−l+1+t is above while pk+t is below l .\nE. pk−l+1+t is below l and pk+t is above l ′ .", "pid": 34, "answer": "E", "category": "other cs"}, {"image": "images/image_35.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pk−l+1+t is above while pk+t is below l .\nB. pk−l+1−t is below l and pk+u−l−t is above l .\nC. both pk−l+1+t and pk+t are above l .\nD. pk−l+1+t is below l and pk+t is above l ′ .\nE. pk−l+1+t is below l and pk+t is below l ′ .", "pid": 35, "answer": "E", "category": "other cs"}, {"image": "images/image_36.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pk−l+1+t is above while pk+t is below l .\nB. pk−l+1−t is below l and pk+u−l−t is also below l .\nC. pk−l+1−t is above l and pk+u−l−t is above l .\nD. pk−l+1+t is below l and pk+t is below l ′ .\nE. pk−l+1−t is below l and pk+u−l−t is above l .", "pid": 36, "answer": "C", "category": "other cs"}, {"image": "images/image_37.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average user time of gridsim , dwgrd and cwgrd with users=20 .\nB. average user time of gridsim , dwgrd and cwgrd with resources=20 .\nC. average user time of gridsim , dwgrd and cwgrd with resources=60 .\nD. average user time of gridsim , dwgrd and cwgrd with users=100 .\nE. average user time of gridsim , dwgrd and cwgrd with users=60 .", "pid": 37, "answer": "D", "category": "cross-list"}, {"image": "images/image_38.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. waterfall frequency diagram for z direction in p1 position .\nB. waterfall frequency diagram for y direction in p2 position .\nC. waterfall frequency diagram for x direction in p2 position .\nD. frequency spectrum on x , y and z direction without cutting .\nE. waterfall frequency diagram for z direction in p3 position .", "pid": 38, "answer": "C", "category": "cs.R<PERSON>"}, {"image": "images/image_39.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. waterfall frequency diagram for x direction in p2 position .\nB. frequency spectrum on x , y and z direction without cutting .\nC. waterfall frequency diagram for z direction in p3 position .\nD. waterfall frequency diagram for y direction in p2 position .\nE. waterfall frequency diagram for z direction in p1 position .", "pid": 39, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_40.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber vs snr for fft size=2048 using ls , lmmse , lr-lmmse .\nB. ber vs snr for fft size=1024 using ls , lmmse , lr-lmmse algorithms .\nC. ber vs snr for fft size=512 using ls , lmmse , lr-lmmse algorithms .\nD. ber vs snr for fft size=256 using ls , lmmse , lr-lmmse algorithms with a 16 qam modulation .\nE. ber vs snr for a bpsk modulation .", "pid": 40, "answer": "D", "category": "other cs"}, {"image": "images/image_41.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber vs snr for a bpsk modulation .\nB. ber vs snr for fft size=1024 using ls , lmmse , lr-lmmse algorithms .\nC. ber vs snr for fft size=256 using ls , lmmse , lr-lmmse algorithms with a 16 qam modulation .\nD. ber vs snr for fft size=2048 using ls , lmmse , lr-lmmse .\nE. ber vs snr for fft size=512 using ls , lmmse , lr-lmmse algorithms .", "pid": 41, "answer": "E", "category": "other cs"}, {"image": "images/image_42.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber vs snr for fft size=512 using ls , lmmse , lr-lmmse algorithms .\nB. mse vs snr for a 16 qam modulation .\nC. ber vs snr for fft size=1024 using ls , lmmse , lr-lmmse algorithms .\nD. ber vs snr for fft size=256 using ls , lmmse , lr-lmmse algorithms with a 16 qam modulation .\nE. ber vs snr for a bpsk modulation .", "pid": 42, "answer": "E", "category": "other cs"}, {"image": "images/image_43.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. averaged variation of ϕ ( fitness ) against increasing amount of gaussian blur and a fixed amount of gaussian noise for reference images in tid200 database .\nB. averaged variation of ϕ ( fitness ) against increasing amount of gaussian blur for reference images in tid2008 database .\nC. averaged variation of κ against increasing amount of gaussian noise for reference images in tid2008 database .\nD. averaged variation of κ against increasing amount of gaussian blur and a fixed amount of gaussian noise for reference images in tid2008 database .\nE. averaged variation of κ against increasing amount of gaussian blur for reference images in tid2008 database .", "pid": 43, "answer": "D", "category": "cs.CV"}, {"image": "images/image_44.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. control signal of 1p for step input and load disturbance with itae based tuning .\nB. control signal of 1p for step input and load disturbance with istes based tuning .\nC. control signal of 2p for step input and load disturbance with istes based tuning .\nD. control signal of 1p for step input and load disturbance with istse based tuning .\nE. control signal of 1p for step input and load disturbance with itse based tuning .", "pid": 44, "answer": "B", "category": "cs.SY"}, {"image": "images/image_45.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. step input and load disturbance response for plant 2p with itae based tuning .\nB. control signal of 1p for step input and load disturbance with itse based tuning .\nC. control signal of 1p for step input and load disturbance with itae based tuning .\nD. step input and load disturbance response for plant 1p with itae based tuning .\nE. control signal of 2p for step input and load disturbance with itse based tuning .", "pid": 45, "answer": "A", "category": "cs.SY"}, {"image": "images/image_46.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. control signal of 1p for step input and load disturbance with itae based tuning .\nB. control signal of 1p for step input and load disturbance with itse based tuning .\nC. control signal of 2p for step input and load disturbance with istes based tuning .\nD. control signal of 2p for step input and load disturbance with istse based tuning .\nE. control signal of 2p for step input and load disturbance with itse based tuning .", "pid": 46, "answer": "E", "category": "cs.SY"}, {"image": "images/image_47.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. control signal of 1p for step input and load disturbance with istes based tuning .\nB. control signal of 2p for step input and load disturbance with itse based tuning .\nC. control signal of 1p for step input and load disturbance with itse based tuning .\nD. control signal of 2p for step input and load disturbance with istse based tuning .\nE. control signal of 2p for step input and load disturbance with istes based tuning .", "pid": 47, "answer": "E", "category": "cs.SY"}, {"image": "images/image_48.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. control signal of 2p for step input and load disturbance with istes based tuning .\nB. control signal of 2p for step input and load disturbance with itse based tuning .\nC. control signal of 1p for step input and load disturbance with itse based tuning .\nD. control signal of 2p for step input and load disturbance with istse based tuning .\nE. control signal of 1p for step input and load disturbance with istes based tuning .", "pid": 48, "answer": "D", "category": "cs.SY"}, {"image": "images/image_49.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. control signal of 2p for step input and load disturbance with itse based tuning .\nB. control signal of 1p for step input and load disturbance with itae based tuning .\nC. control signal of 1p for step input and load disturbance with istse based tuning .\nD. control signal of 1p for step input and load disturbance with itse based tuning .\nE. control signal of 1p for step input and load disturbance with istes based tuning .", "pid": 49, "answer": "D", "category": "cs.SY"}, {"image": "images/image_50.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curve of the generated multibiometrics fusion functions on the validation set of the private dataset .\nB. roc curve for the kinovis-mst dataset .\nC. roc curve of algorithms and result of fused classifier on test set .\nD. roc curve of the generated multibiometrics fusion functions on the validation set of the banca dataset .\nE. roc curve of the generated multibiometrics fusion functions on the validation set of the bssr1 dataset .", "pid": 50, "answer": "A", "category": "other cs"}, {"image": "images/image_51.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. snr vs. the number of modules for classical modular method and our proposed optimized method for s & h interpolation for 2-d signals .\nB. snr vs. the number of modules for classical modular method and our proposed optimized modular method for s & h interpolation .\nC. snr vs. the number of modules for classical modular method and our proposed optimized modular method for linear interpolation .\nD. snr vs. the number of modules for classical modular method and our proposed optimized method for s & h interpolation .\nE. snr vs. the number of modules for classical modular method and our proposed optimized modular method for linear interpolati n .", "pid": 51, "answer": "A", "category": "cross-list"}, {"image": "images/image_52.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. psnr of various algorithms for baboon image corrupted by rvin .\nB. psnr of various algorithms for baboon image corrupted by zero mean gaussian noise .\nC. mse of various algorithms for baboon image corrupted by zero mean gaussian noise .\nD. mse of various algorithms for baboon image corrupted by rvin .\nE. ief of various algorithms for baboon image corrupted by rvin .", "pid": 52, "answer": "D", "category": "cs.CV"}, {"image": "images/image_53.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. proposed protocol : reconstruction error performance for different values oftout , m = 0 , n = 25 , l = 5 .\nB. proposed protocol : reconstruction error performance for different values ofλ ands = 6 , 8 , 10 ; tout = 30 , n = 49 , m = 40 andl = 7 .\nC. example 1 : the accuracy errors rel ( κ ) of adnn using various numbers of the threshold tol .\nD. reconstruction error on the normal measurements in t validlstm with different missing ratios γ .\nE. proposed protocol : reconstruction error performance for different values ofm , λ andtout = 30 , n = 9 , l = 7 .", "pid": 53, "answer": "B", "category": "other cs"}, {"image": "images/image_54.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 8-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nB. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 1-fsk wsn system over rayleigh fading channels for probabilities of correct detection equal to 0.999 .\nC. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 16-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nD. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of -fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nE. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 10 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .", "pid": 54, "answer": "D", "category": "cross-list"}, {"image": "images/image_55.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 16-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nB. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 8-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nC. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 10 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .\nD. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of -fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nE. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 20 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .", "pid": 55, "answer": "B", "category": "cross-list"}, {"image": "images/image_56.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 20 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .\nB. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of -fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nC. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 16-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nD. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 4 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .\nE. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 8-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .", "pid": 56, "answer": "C", "category": "cross-list"}, {"image": "images/image_57.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 16-fsk 8 node wsn system over rayleigh fading channels for probabilities of correct detection less than unity .\nB. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of mfsk for 10 nodes wsn system over rayleigh fading channels for different modulation levels with probability of correct detection equal to unity .\nC. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 1-fsk wsn system over rayleigh fading channels for probabilities of correct detection equal to 0.999 .\nD. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of 16-fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .\nE. bit error rate ( ber ) vs signal to noise ratio ( snr ) performance of -fsk wsn for different number of sensors over rayleigh fading channels with probability of correct detection equal to unity .", "pid": 57, "answer": "A", "category": "cross-list"}, {"image": "images/image_58.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ccdf vs. papr curve of ofdm signal using convolution codes with code rate 12 and constraint length ( k = 3− 8 ) .\nB. ccdf vs. papr curve of ofdm signal using convolution codes with code rate 13 and constraint length ( k = 3− 8 ) .\nC. ccdf vs. papr curve of coded ofdm signal using different channel coding techniques .\nD. ccdf vs. papr curve of ofdm signal using convolution codes with code rate 13 and constraint length ( k=6-14 ) .\nE. ccdf vs. papr curve of ofdm signal using convolution codes with code rate 12 and constraint length ( k = 9− 14 ) .", "pid": 58, "answer": "E", "category": "cross-list"}, {"image": "images/image_59.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average end to end delay ( sec ) vs number of nodes .\nB. end-to-end delay vs. number of sensor nodes .\nC. data accuracy versus number of sensor nodes .\nD. end-to-end delay bounds vs number of nodes .\nE. computation overhead vs. number of sensor nodes .", "pid": 59, "answer": "B", "category": "cs.CR"}, {"image": "images/image_60.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the accuracy of different number of training set .\nB. precision over the test set with respect to the number of training pairs .\nC. performance over the test set with respect to the number of training pairs when the grades of documents are not known .\nD. precision of the enhanced model over the test set with respect to the number of training pairs .\nE. similarity of the enhanced model with respect to the number of training pairs .", "pid": 60, "answer": "E", "category": "other cs"}, {"image": "images/image_61.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the accuracy of different number of training set .\nB. precision of the enhanced model over the test set with respect to the number of training pairs .\nC. precision over the test set with respect to the number of training pairs .\nD. similarity of the enhanced model with respect to the number of training pairs .\nE. performance over the test set with respect to the number of training pairs when the grades of documents are not known .", "pid": 61, "answer": "B", "category": "other cs"}, {"image": "images/image_62.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. recall error rate and the theoretical bounds for different architectures of network with n = 960 and n = 400 pattern neurons and l = 60 and l = 50 clusters , respectively..\nB. pattern error rate against the initial number of erroneous nodes for two different network sizes n = 800 and k = 400. in both cases k = n/2 .\nC. average normalized error of estimated matrix norm versusnl with different number of ers , k , wheremt = 4 andmr = 2 .\nD. pattern error rate in the first and last iterations against the initial number of erroneous nodes for n = 800 , k = 400 , α0 = 0.95 , θ0 = 0.029 and ϕ = 0.99 .\nE. pattern error rate against the initial number of erroneous nodes and comparison with theoretical upper bounds for n = 800 , k = 400 , α0 = 0.95 and θ0 = 0.029 .", "pid": 62, "answer": "E", "category": "other cs"}, {"image": "images/image_63.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. number of bs 's in cwn-pam by method i for 30 nodes .\nB. number of bs 's in cwn-pam by method ii for 101 nodes .\nC. number of bs 's in m-pam by method ii for 101 nodes .\nD. number of bs 's in cwn-pam by method ii for 30 nodes .\nE. number of bs 's in cwn-pam by method i for 1 nodes .", "pid": 63, "answer": "E", "category": "cs.NI"}, {"image": "images/image_64.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. number of bs 's in cwn-pam by method ii for 101 nodes .\nB. number of bs 's in m-pam by method ii for 101 nodes .\nC. number of bs 's in cwn-pam by method i for 1 nodes .\nD. number of bs 's in cwn-pam by method ii for 30 nodes .\nE. number of bs 's in cwn-pam by method i for 30 nodes .", "pid": 64, "answer": "A", "category": "cs.NI"}, {"image": "images/image_65.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. number of bs 's in cwn-pam by method ii for 101 nodes .\nB. number of bs 's in cwn-pam by method i for 30 nodes .\nC. number of bs 's in m-pam by method i for 30 node .\nD. number of bs 's in cwn-pam by method i for 1 nodes .\nE. number of bs 's in cwn-pam by method ii for 30 nodes .", "pid": 65, "answer": "B", "category": "cs.NI"}, {"image": "images/image_66.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance evaluation of gpcb-rs codes , with m=100 , over awgn channel .\nB. performance evaluation of gpcb-bch codes , with m=100 , over awgn channel .\nC. effect of the parameter m on iterative decoding of gpcb-bch-rs ( 1 , 113 ) code , over awgn channel .\nD. performances comparison between gpcb-rs , gpcbbch-rs and gpcb-bch codes , with m=100 , over awgn channel .\nE. performance evaluation of gpcb-bch-rs codes , with m=100 , over awgn channel .", "pid": 66, "answer": "B", "category": "cross-list"}, {"image": "images/image_67.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance evaluation of gpcb-bch codes , with m=100 , over awgn channel .\nB. effect of iterations on iterative decoding of gpcbbch ( 148 , 106 ) code , over awgn channel .\nC. performance evaluation of gpcb-bch-rs codes , with m=100 , over awgn channel .\nD. performances comparison between gpcb-rs , gpcbbch-rs and gpcb-bch codes , with m=100 , over awgn channel .\nE. performance evaluation of gpcb-rs codes , with m=100 , over awgn channel .", "pid": 67, "answer": "C", "category": "cross-list"}, {"image": "images/image_68.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. variation of the average delay of nrt packets according to arrival rate of rrt packets .\nB. average delays of nrt packets according to the arrival rate of rt packets .\nC. average delays of nrt packets versus service rate of rt packets .\nD. average delay of nrt packets according to the arrival rate of nrt packets .\nE. average delays of nrt packets versus service rate of nrt packets .", "pid": 68, "answer": "B", "category": "cs.NI"}, {"image": "images/image_69.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. optimal pareto front by nsga ii for test problem i .\nB. optimal pareto front by spea ii for test problem i .\nC. the approximated pareto front obtained by different algorithms on the rosetta problem .\nD. optimal pareto front by nsga i for test problem ii .\nE. optimal pareto front by spea ii for dew point .", "pid": 69, "answer": "A", "category": "other cs"}, {"image": "images/image_70.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. optimal pareto front by nsga ii for test problem ii .\nB. optimal pareto front by spea ii for test problem i .\nC. optimal pareto front by spea ii for dew point .\nD. the approximated pareto front obtained by different algorithms on the rosetta problem .\nE. optimal pareto front by nsga i for test problem i .", "pid": 70, "answer": "A", "category": "other cs"}, {"image": "images/image_71.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. plot of path cost v/s generation for 2 nodes , 1000 iterations .\nB. plot of path cost v/s generation for 100 nodes , 100 iterations .\nC. plot of path cost v/s generation for 500 nodes , 200 iterations .\nD. plot of path cost v/s generation for 1000 nodes , 1000 iterations .\nE. plot of path cost v/s generation for 50 nodes , 10 iterations .", "pid": 71, "answer": "B", "category": "cross-list"}, {"image": "images/image_72.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. plot of path cost v/s generation for 50 nodes , 100 iterations .\nB. plot of path cost v/s generation for 500 nodes , 200 iterations .\nC. plot of path cost v/s generation for 2 nodes , 100 iterations .\nD. plot of path cost v/s generation for 100 nodes , 100 iterations .\nE. plot of path cost v/s generation for 1000 nodes , 100 iterations .", "pid": 72, "answer": "B", "category": "cross-list"}, {"image": "images/image_73.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. lower level function evaluations for smd from 3 runs of the nested bilevel solution procedure .\nB. simulation for the improved method ( nnsizze=100 , sc=50 , sum-squared error of 1x10^ ( -6 ) ) .\nC. upper level function evaluations for smd2 from 31 runs of the nested bilevel solution procedure .\nD. upper level function evaluations for smd1 from 31 runs of the nested bilevel solution procedure .\nE. lower level function evaluations for smd2 from 1 runs of the nested bilevel solution procedure .", "pid": 73, "answer": "E", "category": "other cs"}, {"image": "images/image_74.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. evolution average ncloc per class in freemind .\nB. evolution of function per class in freemind .\nC. evolution of classes in freemind .\nD. evolution of average classes per package in freemind .\nE. evolution of modularity index in freemind .", "pid": 74, "answer": "B", "category": "cs.SE"}, {"image": "images/image_75.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. evolution of average package quality in freemind .\nB. evolution of packages in freemind .\nC. evolution of modularity index in freemind .\nD. evolution of system architecture in freemind .\nE. evolution of average classes per package in freemind .", "pid": 75, "answer": "A", "category": "cs.SE"}, {"image": "images/image_76.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. histogram of texts with different anger word densities .\nB. histogram of texts with different anticip word densities .\nC. histogram of utterance distribution across speakers .\nD. histogram of texts with different surprise word densities .\nE. histogram of texts with different joy word densities .", "pid": 76, "answer": "D", "category": "cs.CL"}, {"image": "images/image_77.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. dr , raw feature [ 12-12 ] pair vs. enhanced features .\nB. dr , raw feature [ 8-8 ] pair vs. enhanced features .\nC. mttd , raw feature [ 8-8 ] pair vs. enhanced features .\nD. mttd , raw feature [ 12-12 ] pair vs. enhanced features .\nE. mttd , raw feature [ -2 ] pair vs. enhanced features .", "pid": 77, "answer": "C", "category": "cs.LG"}, {"image": "images/image_78.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mttd , raw feature [ 12-12 ] pair vs. enhanced features .\nB. dr , raw feature [ 12-12 ] pair vs. enhanced features .\nC. dr , raw feature [ 8-8 ] pair vs. enhanced features .\nD. mttd , raw feature [ 8-8 ] pair vs. enhanced features .\nE. dr , raw feature [ 4-2 ] pair vs. enhanced features .", "pid": 78, "answer": "B", "category": "cs.LG"}, {"image": "images/image_79.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. cross correlation function between example 1 and 2 when mod n=2151 .\nB. cross correlation function between example 1 and 4 when mod n=7283 .\nC. cross correlation function between example 1 and 3 when mod n=3121 .\nD. cross correlation function between example 3 and 4 when mod n=3121 .\nE. cross correlation function between example 2 and 4 when mod n=21851 .", "pid": 79, "answer": "E", "category": "cs.CR"}, {"image": "images/image_80.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification results .\nB. performance of different feature selection methods on orl data set .\nC. results of learning methods for feature engineering ii .\nD. results of learning methods for feature engineering i .\nE. results of learning methods for baseline .", "pid": 80, "answer": "D", "category": "other cs"}, {"image": "images/image_81.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. computational complexity in terms of visited nodes required by g<PERSON><PERSON><PERSON>lson ’ s sphere decoder with s-e for 3d mimo code and proposed simplified ml decoders , in quasi-static rayleigh channel with qpsk modulation .\nB. computational complexity in terms of multiplications required by g<PERSON><PERSON><PERSON>lson ’ s sphere decoder with s-e for 3d mimo code and proposed simplified ml decoders , in quasi-static rayleigh channel with 16-qam modulation .\nC. computational complexity in terms of multiplications required by g<PERSON><PERSON><PERSON>lson ’ s sphere decoder with s-e for 3d mimo code and proposed simplified ml decoders , in quasi-static rayleigh channel with qpsk modulation .\nD. computational complexity in terms of additions required by g<PERSON><PERSON><PERSON>lson ’ s sphere decoder with s-e for 3d mimo code and proposed simplified ml decoders , in quasi-static rayleigh channel with 16-qam modulation .\nE. computational complexity in terms of divisions required by g<PERSON><PERSON><PERSON>lson ’ s sphere decoder with s-e for 3d mimo code and proposed simplified ml decoders , in quasi-static rayleigh channel with qpsk modulation .", "pid": 81, "answer": "E", "category": "cross-list"}, {"image": "images/image_82.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the different sampling strategies at estimating worker performance in the simulated pool with binary worker performance levels using pearson correlation .\nB. comparison of the different sampling strategies at estimating worker performance in the simulated pool with gaussian worker performance levels using pearson correlation .\nC. comparison of different worker selection strategies at estimated worker performance in the simulated pool with gaussian worker performance levels using chi correlation .\nD. comparison of different sampling strategies at estimated worker performance in the simulated pool with gaussian worker performance levels using spearman correlation .\nE. comparison of different sampling strategies at estimated worker performance in the simulated pool with binary worker performance levels using r correlation .", "pid": 82, "answer": "D", "category": "cross-list"}, {"image": "images/image_83.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ta-mse comparison for the adaptive rx3 and rx4 for acgn channels .\nB. ta-mse comparison for awgn channel to verify robustness to noise model .\nC. ta-mse comparison for the katayama noise model of [ 6 ] .\nD. convergence of the ta-mse of the adaptive rx4 with training to the ta-mse of the optimal rx4 .\nE. theoretical ta-mse comparison with simulated ta-mse .", "pid": 83, "answer": "A", "category": "other cs"}, {"image": "images/image_84.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. theoretical ta-mse comparison with simulated ta-mse .\nB. ta-mse comparison for the katayama noise model of [ 6 ] .\nC. ta-mse comparison for awgn channel to verify robustness to noise model .\nD. mse performance of the phase noise estimation .\nE. ta-mse comparison for the lptv noise model of [ 9 ] .", "pid": 84, "answer": "B", "category": "other cs"}, {"image": "images/image_85.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance comparison on accuracy .\nB. oarae parameter values for different methods .\nC. oatprate parameter values for different methods .\nD. arae parameter values for different methods .\nE. test results of different parameters with same amount of data in one epoch .", "pid": 85, "answer": "D", "category": "cs.LG"}, {"image": "images/image_86.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\n<PERSON><PERSON> o<PERSON>e parameter values for different methods .\nB. oatprate parameter values for different methods .\nC. performance comparison on accuracy .\nD. parameter sensitivity analysis results .\nE. arae parameter values for different methods .", "pid": 86, "answer": "B", "category": "cs.LG"}, {"image": "images/image_87.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. plot of β ( σ1 , α ) for σ1 = 0.5 , and α = 0 and α = π/ .\nB. the optimum pe1 as a function of υ for different values π1 .\nC. function υ ( α ) for α ∈ ( 0 , π2 ) and ς = 1.03 , % = 0.06 .\nD. the loss function eθ as function of ∆θ .\nE. the behavior of ϕ∗ as a function of υ for π1 = 0.01 .", "pid": 87, "answer": "E", "category": "other cs"}, {"image": "images/image_88.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance of the proposed asynchronous differential schemes for τ2−τ1 = ts/2 and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 4 transmit antennas and 1 receiver with 1 receive antenna .\nB. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nC. performance of the proposed asynchronous differential schemes for τj+1 − τj = ts/3 , ∀j , and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 3 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nD. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nE. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 1 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 3 receive antennas .", "pid": 88, "answer": "B", "category": "cross-list"}, {"image": "images/image_89.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nB. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nC. performance of the proposed asynchronous differential schemes for τj+1 − τj = ts/3 , ∀j , and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 3 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nD. performance of the proposed asynchronous differential schemes for τ2−τ1 = ts/2 and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 4 transmit antennas and 1 receiver with 1 receive antenna .\nE. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 1 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 3 receive antennas .", "pid": 89, "answer": "E", "category": "cross-list"}, {"image": "images/image_90.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nB. performance of the proposed asynchronous differential schemes for τ2−τ1 = ts/2 and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 4 transmit antennas and 1 receiver with 1 receive antenna .\nC. performance of the proposed asynchronous differential schemes for τj+1 − τj = ts/3 , ∀j , and the synchronous coherent scheme using ml decoding at a rate of 1 b/ ( s hz ) for 3 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .\nD. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 1 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 2 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 3 receive antennas .\nE. performance of the proposed asynchronous differential schemes for τ2 − τ1 = ts/2 , the synchronous differential schemes in [ 17 ] , and the synchronous coherent schemes using zf and ml decoding at a rate of 1 b/ ( s hz ) for 2 users each with 2 transmit antennas and 1 receiver with 2 receive antennas .", "pid": 90, "answer": "B", "category": "cross-list"}, {"image": "images/image_91.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. relative phase difference between port 4 and output ports .\nB. relative phase difference between port 1 and output ports .\nC. relative phase difference between port 2 and output ports .\nD. relative phase difference between port 3 and output ports .\nE. relative phase difference between output ports of the blc .", "pid": 91, "answer": "B", "category": "other cs"}, {"image": "images/image_92.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. relative phase difference between output ports of the blc .\nB. relative phase difference between port 2 and output ports .\nC. relative phase difference between port 4 and output ports .\nD. relative phase difference between port 1 and output ports .\nE. relative phase difference between port 3 and output ports .", "pid": 92, "answer": "E", "category": "other cs"}, {"image": "images/image_93.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. relative phase difference between port 2 and output ports .\nB. relative phase difference between output ports of the blc .\nC. relative phase difference between port 1 and output ports .\nD. relative phase difference between port 4 and output ports .\nE. relative phase difference between port 3 and output ports .", "pid": 93, "answer": "D", "category": "other cs"}, {"image": "images/image_94.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. 8x8 rl beam to array spillover coupling amplitude .\nB. 4x4 rl beam to array spillover coupling amplitude .\nC. 8x8 rl beam to array coupling amplitude .\nD. 8x8 rl beam to array phase error .\nE. 4x4 rl beam to array spillover amplitude .", "pid": 94, "answer": "C", "category": "other cs"}, {"image": "images/image_95.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. accuracy comparison with different number of selected features on isolet5 dataset .\nB. accuracy comparison with different number of selected features on musk2 dataset .\nC. accuracy comparison with different number of selected features on splice dataset .\nD. accuracy comparison with different number of selected features on isolets .\nE. accuracy comparison with different number of selected features on mfeat-zernike dataset .", "pid": 95, "answer": "A", "category": "cross-list"}, {"image": "images/image_96.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the misdetection probability of various algorithms as a function of the sample size n for the case of one strong signal with ] 1 [ when 40p .\nB. comparison of the misdetection probability of various algorithms as a function of system size p with fixed ratio 4// np when there is no signal with [ ] .\nC. comparison of the misdetection probability of various algorithms as a function of the sample size n for the case of no signal with [ ] when 0p .\nD. comparison of the misdetection probability of various algorithms as a function of system size p with fixed ratio 4/1/ np when there is one strong signal with ] 15 [ .\nE. comparison of the misdetection probability of various algorithms as function of system size p with fixed ratio .", "pid": 96, "answer": "B", "category": "cross-list"}, {"image": "images/image_97.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the misdetection probability of various algorithms as a function of system size p with fixed ratio 4// np when there is no signal with [ ] .\nB. comparison of the misdetection probability of various algorithms as a function of the sample size n for the case of no signal with [ ] when 0p .\nC. comparison of the misdetection probability of various algorithms as a function of the sample size n for the case of one strong signal with ] 1 [ when 40p .\nD. comparison of the misdetection probability of various algorithms as a function of system size p with fixed ratio 4/1/ np when there is one strong signal with ] 15 [ .\nE. comparison of the misdetection probability of various algorithms as function of system size p with fixed ratio .", "pid": 97, "answer": "D", "category": "cross-list"}, {"image": "images/image_98.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison between predicted values and actual values of final boiling point during training process .\nB. comparison between predicted values and actual values during training process .\nC. comparison between residual values and actual values during training process .\nD. comparison between predicted values and actual values of initial boiling point during training process .\nE. comparison between predicted values and actual values during training process using mlfn-7 ( forest stock volume ) .", "pid": 98, "answer": "B", "category": "other cs"}, {"image": "images/image_99.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time for the task with matrix addition kernels .\nB. execution time ( ms ) on random arrays - log10 scale .\nC. execution time ( ms ) on sorted arrays .\nD. execution time ( ms ) on random arrays .\nE. execution time ( ms ) on sorted arrays - log10 scale .", "pid": 99, "answer": "C", "category": "other cs"}, {"image": "images/image_100.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time .\nB. execution time ( ms ) on sorted arrays - log10 scale .\nC. execution time ( ms ) on sorted arrays .\nD. execution time ( ms ) on random arrays - log10 scale .\nE. execution time ( ms ) on random arrays .", "pid": 100, "answer": "B", "category": "other cs"}, {"image": "images/image_101.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the epdf of the time between commits of popular and unpopular projects .\nB. the epdf and ecdf of time between commits ( up to an interval of week ) .\nC. the epdf and ecdf of time between two consecutive commits of ( up to an interval of 1 day ) .\nD. the epdf of the median time between commits of popular and unpopular projects .\nE. the epdf and ecdf of median commit intervals per committer .", "pid": 101, "answer": "E", "category": "cs.SE"}, {"image": "images/image_102.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comments per line of code ( min ) of all tasks ( python vs. other languages ) .\nB. lines of code ( mean ) of all tasks ( python vs. other languages ) .\nC. lines of code ( mean ) of tasks compiling successfully ( python vs. other languages ) .\nD. comments per line of code ( mean ) of all tasks ( python vs. other languages ) .\nE. performance ( min ) of tasks running successfully ( python vs. other languages ) .", "pid": 102, "answer": "B", "category": "cs.SE"}, {"image": "images/image_103.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance ( min ) of tasks running successfully ( python vs. other languages ) .\nB. comments per line of code ( min ) of all tasks ( python vs. other languages ) .\nC. lines of code ( mean ) of tasks compiling successfully ( python vs. other languages ) .\nD. comments per line of code ( mean ) of all tasks ( python vs. other languages ) .\nE. lines of code ( mean ) of all tasks ( python vs. other languages ) .", "pid": 103, "answer": "D", "category": "cs.SE"}, {"image": "images/image_104.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance ( mean ) of tasks running successfully ( all languages ) .\nB. runtime ( mean ) of tasks running successfully ( all languages ) .\nC. scalability ( min ) of tasks running successfully ( all languages ) .\nD. maximum ram usage ( mean ) of tasks running successfully ( all languages ) .\nE. performance ( min ) of tasks running successfully ( all languages ) .", "pid": 104, "answer": "B", "category": "cs.SE"}, {"image": "images/image_105.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. maximum ram usage ( mean ) of tasks running successfully ( all languages ) .\nB. performance ( min ) of tasks running successfully ( all languages ) .\nC. maximum ram usage ( min ) of tasks running successfully ( all languages ) .\nD. scalability ( mean ) of tasks running successfully ( all languages ) .\nE. scalability ( min ) of tasks running successfully ( all languages ) .", "pid": 105, "answer": "C", "category": "cs.SE"}, {"image": "images/image_106.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance ( mean ) of tasks running successfully ( all languages ) .\nB. performance ( min ) of tasks running successfully ( all languages ) .\nC. page faults ( min ) of tasks running successfully ( all languages ) .\nD. page faults ( mean ) of tasks running successfully ( all languages ) .\nE. scalability ( mean ) of tasks running successfully ( all languages ) .", "pid": 106, "answer": "D", "category": "cs.SE"}, {"image": "images/image_107.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. s-band microwave lna measured frequency response .\nB. simulated s–parameters of the 0db crossover .\nC. s-band microwave lna simulated stability µ-test .\nD. frequency response of the rsiw directional coupler .\nE. s-band microwave lna simulated frequency response .", "pid": 107, "answer": "A", "category": "other cs"}, {"image": "images/image_108.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mse of ule for n=1 and its theoretical curve in case when k = 1 , ts = 3µ , where µ is the mean of t .\nB. the mse of ule for n = n in case when k = 6 , and n1 = n2 = ... = n6 , where µ is the mean of t .\nC. comparison of mse normalized by n for different values of k with σ = 1 .\nD. the mse of mle and ule with cmlb as benchmark in case when k = 1 , n1 = n , and x = 0n1 .\nE. mse of decision feedback and theoretical decision-directed parameter estimation in case when k = 1 , ts = 3µ .", "pid": 108, "answer": "B", "category": "other cs"}, {"image": "images/image_109.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of mse normalized by n for different values of k with σ = 1 .\nB. the mse of mle and ule with cmlb as benchmark in case when k = 1 , n1 = n , and x = 0n1 .\nC. mse of ule for n=1 and its theoretical curve in case when k = 1 , ts = 3µ , where µ is the mean of t .\nD. mse of decision feedback and theoretical decision-directed parameter estimation in case when k = 1 , ts = 3µ .\nE. the mse of ule for n = n in case when k = 6 , and n1 = n2 = ... = n6 , where µ is the mean of t .", "pid": 109, "answer": "C", "category": "other cs"}, {"image": "images/image_110.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of bit error rates of uncoded modulations .\nB. uncoded qpsk vs. coded higher-order modulations ( v=10 ) .\nC. bit error rate for differents modulations .\nD. uncoded ber performance comparison with 16-qam modulation .\nE. bit error rates of the proposed decoders .", "pid": 110, "answer": "A", "category": "cs.CR"}, {"image": "images/image_111.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. uncoded qpsk vs. coded higher-order modulations ( v=10 ) .\nB. uncoded 16-qam vs. coded 64-qam ( v= ) .\nC. uncoded 8-psk vs. coded higher-order modulations ( v=10 ) .\nD. comparison of bit error rates of uncoded modulations .\nE. comparison with ldpc coded modulation at r = 1 .", "pid": 111, "answer": "C", "category": "cs.CR"}, {"image": "images/image_112.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\n<PERSON><PERSON> sad of the learned f2 features for coronary .\n<PERSON><PERSON> sad of the learned f3 features for coronary .\n<PERSON><PERSON> sad of the learned f1 features for coronary .\n<PERSON><PERSON> sad of the learned f4 features for coronary .\n<PERSON><PERSON> sad of the learned f5 features for coronary .", "pid": 112, "answer": "C", "category": "cross-list"}, {"image": "images/image_113.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\n<PERSON><PERSON> sad of the learned f1 features for coronary .\n<PERSON><PERSON> sad of the learned f4 features for coronary .\n<PERSON><PERSON> sad of the learned f5 features for coronary .\n<PERSON><PERSON> sad of the learned f2 features for coronary .\n<PERSON><PERSON> sad of the learned f3 features for coronary .", "pid": 113, "answer": "D", "category": "cross-list"}, {"image": "images/image_114.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\n<PERSON><PERSON> sad of the learned f5 features for coronary .\n<PERSON><PERSON> sad of the learned f1 features for coronary .\n<PERSON><PERSON> sad of the learned f3 features for coronary .\n<PERSON><PERSON> sad of the learned f4 features for coronary .\n<PERSON><PERSON> sad of the learned f2 features for coronary .", "pid": 114, "answer": "D", "category": "cross-list"}, {"image": "images/image_115.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\n<PERSON><PERSON> sad of the learned f4 features for coronary .\n<PERSON><PERSON> sad of the learned f5 features for coronary .\n<PERSON><PERSON> sad of the learned f2 features for coronary .\n<PERSON><PERSON> sad of the learned f1 features for coronary .\n<PERSON><PERSON> sad of the learned f3 features for coronary .", "pid": 115, "answer": "B", "category": "cross-list"}, {"image": "images/image_116.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. msd performance comparisons ( 𝜇 = 2.5 ) .\nB. msd performance comparisons ( 𝜇 = 1.5 ) .\nC. msd performance comparisons taps ( snr=8db ) .\nD. msd performance comparisons ( k=6 ) .\nE. msd performance comparisons ( k=4 ) .", "pid": 116, "answer": "B", "category": "cross-list"}, {"image": "images/image_117.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. msd performance comparisons taps ( snr=8db ) .\nB. msd performance comparisons ( 𝜇 = 1.5 ) .\nC. msd performance comparisons ( 𝜇 = 2.5 ) .\nD. msd performance comparisons ( k=6 ) .\nE. msd performance comparisons ( k=4 ) .", "pid": 117, "answer": "C", "category": "cross-list"}, {"image": "images/image_118.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. task allocation proportion for m-i team .\nB. task allocation proportion for s-c team .\nC. task allocation proportion for m-m team .\nD. task allocation proportion for m-c team .\nE. task allocation proportion for s-i team .", "pid": 118, "answer": "B", "category": "cs.SE"}, {"image": "images/image_119.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. task allocation proportion for s-i team .\nB. task allocation proportion for m-m team .\nC. task allocation proportion for m-c team .\nD. task allocation proportion for m-i team .\nE. task allocation proportion for l-m team .", "pid": 119, "answer": "D", "category": "cs.SE"}, {"image": "images/image_120.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. task allocation proportion for s-i team .\nB. task allocation proportion for m-i team .\nC. task allocation proportion for l-m team .\nD. task allocation proportion for m-m team .\nE. task allocation proportion for m-c team .", "pid": 120, "answer": "D", "category": "cs.SE"}, {"image": "images/image_121.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on gdata1 .\nB. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on heart disease .\nC. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on gdata2 .\nD. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on wine .\nE. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on new thyroid .", "pid": 121, "answer": "A", "category": "cs.LG"}, {"image": "images/image_122.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on gdata1 .\nB. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on heart disease .\nC. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on new thyroid .\nD. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on gdata2 .\nE. the classification accuracy of self-adaptive , entropy-based , distance-based methods for training data labeling on wine .", "pid": 122, "answer": "D", "category": "cs.LG"}, {"image": "images/image_123.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. dynamics of the numbers of us-patents granted per million inhabitants of the usa during the period 1945-1980 ; the straight line is based on linear regression .\nB. dynamics of the numbers of us patents granted per million inhabitants of the usa during the period from 2006 ; the straight line is based on linear regression .\nC. dynamics of the numbers of us-patent granted per year per million inhabitants of the usa during the period 1840-1945 ; the straight line is based on linear regression .\nD. dynamics of the numbers of us-patents granted per million inhabitants of the .\nE. dynamics of the numbers of us patents granted per million inhabitants of the usa during the period 1979-2006 ; the straight line is based on linear regression .", "pid": 123, "answer": "C", "category": "other cs"}, {"image": "images/image_124.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. dynamics of the numbers of us-patents granted per million inhabitants of the usa during the period 1945-1980 ; the straight line is based on linear regression .\nB. dynamics of the numbers of us-patent granted per year per million inhabitants of the usa during the period 1840-1945 ; the straight line is based on linear regression .\nC. dynamics of the numbers of us patents granted per million inhabitants of the usa during the period from 2006 ; the straight line is based on linear regression .\nD. dynamics of the numbers of us-patents granted per million inhabitants of the .\nE. dynamics of the numbers of us patents granted per million inhabitants of the usa during the period 1979-2006 ; the straight line is based on linear regression .", "pid": 124, "answer": "C", "category": "other cs"}, {"image": "images/image_125.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. psnr based comparison with same size cipher different image dimensions .\nB. comparative analysis using psnr with variable image dimensions .\nC. comparative analysis of both methods using psnr with different images .\nD. comparison based on psnr with same image dimension and variable amount of cipher .\nE. comparative analysis using psnr with variable amount of embedded cipher .", "pid": 125, "answer": "A", "category": "other cs"}, {"image": "images/image_126.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time of qmul .\nB. execution time of smul .\nC. execution time of t3mul .\nD. execution time of kmul .\nE. execution time of omul .", "pid": 126, "answer": "E", "category": "other cs"}, {"image": "images/image_127.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the objective function values as iteration proceeds .\nB. objective value vs. number of iterations .\nC. objective function value versus the number of iterations .\nD. objective function value vs iteration index .\nE. the objective function value versus iteration .", "pid": 127, "answer": "D", "category": "other cs"}, {"image": "images/image_128.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. substrate bw utilization comparison using 50 substrate nodes .\nB. substrate cpu utilization comparison using 50 substrate nodes .\nC. substrate resources utilization comparison using 50 substrate nodes .\nD. available substrate bw comparison using 50 substrate nodes .\nE. substrate cpu utilization comparison using 200 substrate nodes .", "pid": 128, "answer": "A", "category": "cs.DC"}, {"image": "images/image_129.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. cost function along the iterations of the alternating optimization algorithm trajectory .\nB. change of the cost during iterations in example .\nC. normalized cost of the acs approach for different number of ants .\nD. normalized cost of the acs approach in different iterations .\nE. performance of the proposed acs algorithm with respect to normalized cost in each iteration number .", "pid": 129, "answer": "D", "category": "other cs"}, {"image": "images/image_130.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. four-layer networks with different initial values .\nB. three-layer networks with different number of filters .\nC. three-layer networks with different filter sizes .\nD. four-layer networks with different filter sizes .\nE. comparison between three-layer and the four-layer networks .", "pid": 130, "answer": "B", "category": "cs.CV"}, {"image": "images/image_131.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. three-layer networks with different number of filters .\nB. four-layer networks with different initial values .\nC. four-layer networks with different filter sizes .\nD. comparison between three-layer and the four-layer networks .\nE. three-layer networks with different filter sizes .", "pid": 131, "answer": "D", "category": "cs.CV"}, {"image": "images/image_132.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. three-layer networks with different number of filters .\nB. four-layer networks with different initial values .\nC. three-layer networks with different filter sizes .\nD. comparison between three-layer and the four-layer networks .\nE. four-layer networks with different filter sizes .", "pid": 132, "answer": "E", "category": "cs.CV"}, {"image": "images/image_133.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison between three-layer and the four-layer networks .\nB. four-layer networks with different filter sizes .\nC. three-layer networks with different number of filters .\nD. four-layer networks with different initial values .\nE. three-layer networks with different filter sizes .", "pid": 133, "answer": "D", "category": "cs.CV"}, {"image": "images/image_134.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the dtit of non-blocking put operation .\nB. comparison of the dtit of non-blocking get operation .\nC. comparison of the bandwidth of the non-blocking put operation .\nD. comparison of the dtct of the blocking put operation .\nE. comparison of the dtct of the blocking get operation .", "pid": 134, "answer": "A", "category": "cs.DC"}, {"image": "images/image_135.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the bandwidth of the non-blocking get operation .\nB. comparison of the bandwidth of the blocking put operation .\nC. comparison of the dtit of non-blocking get operation .\nD. comparison of the bandwidth of the non-blocking put operation .\nE. comparison of the bandwidth of the blocking get operation .", "pid": 135, "answer": "E", "category": "cs.DC"}, {"image": "images/image_136.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. comparison of the dtct of the blocking put operation .\nB. comparison of the dtct of the blocking get operation .\nC. comparison of the dtit of non-blocking get operation .\nD. comparison of the dtit of non-blocking put operation .\nE. comparison of the bandwidth of the blocking get operation .", "pid": 136, "answer": "B", "category": "cs.DC"}, {"image": "images/image_137.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 4 .\nB. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 64 .\nC. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 16 .\nD. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 8 .\nE. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 32 .", "pid": 137, "answer": "C", "category": "cs.NI"}, {"image": "images/image_138.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. uncertainty in localization v/s beacon radius for number of beacons = 32 .\nB. uncertainty in localization v/s beacon radius for number of beacons = 64 .\nC. uncertainty in localization v/s beacon radius for number of beacons = 8 .\nD. uncertainty in localization v/s beacon radius for number of beacons = 4 .\nE. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 4 .", "pid": 138, "answer": "D", "category": "cs.NI"}, {"image": "images/image_139.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. uncertainty in localization v/s beacon radius for number of beacons = 4 .\nB. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 8 .\nC. uncertainty in localization v/s beacon radius for number of beacons = 64 .\nD. uncertainty in localization v/s beacon radius for number of beacons = 8 .\nE. uncertainty in localization v/s beacon radius for number of beacons = 32 .", "pid": 139, "answer": "D", "category": "cs.NI"}, {"image": "images/image_140.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 64 .\nB. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 16 .\nC. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 32 .\nD. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 8 .\nE. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 4 .", "pid": 140, "answer": "E", "category": "cs.NI"}, {"image": "images/image_141.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 32 .\nB. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 64 .\nC. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 4 .\nD. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 8 .\nE. standard deviation uncertainty in localization v/s beacon radius for number of beacons = 16 .", "pid": 141, "answer": "D", "category": "cs.NI"}, {"image": "images/image_142.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nB. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nC. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several l in a scale-free network .\nD. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nE. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .", "pid": 142, "answer": "C", "category": "cs.NI"}, {"image": "images/image_143.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a regular network .\nB. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nC. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a er network .\nD. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nE. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .", "pid": 143, "answer": "A", "category": "cs.NI"}, {"image": "images/image_144.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a regular network .\nB. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nC. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nD. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nE. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a er network .", "pid": 144, "answer": "E", "category": "cs.NI"}, {"image": "images/image_145.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a regular network .\nB. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nC. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nD. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nE. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .", "pid": 145, "answer": "C", "category": "cs.NI"}, {"image": "images/image_146.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nB. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nC. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nD. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nE. choose-first pw-rw with dynamic resources : expected search length ls vs. pw length s for several d in a er network .", "pid": 146, "answer": "B", "category": "cs.NI"}, {"image": "images/image_147.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nB. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several l in a scale-free network .\nC. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nD. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nE. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .", "pid": 147, "answer": "D", "category": "cs.NI"}, {"image": "images/image_148.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several l in a scale-free network .\nB. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .\nC. check-first pw-rw ( w = 5 ) with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nD. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a regular network .\nE. choose-first pw-rw with dynamic nodes : expected search length ls vs. pw length s for several d in a er network .", "pid": 148, "answer": "B", "category": "cs.NI"}, {"image": "images/image_149.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. function f ( x ) = x3 for x ∈ [ 1 , 3 ] .\nB. the function drawn with solid line is φξ ( x ) = ξ + ( 1 − ξ ) φ0 ( x ) with ξ = 0.1 , obtained from φ0 ( x ) = 0.2ϕ2,4 ( x ) + 0.4ϕ2,13 ( x ) + 0.4ϕ2,21 ( x ) ( dashed line ) .\nC. the function ϕε ( x ) for ε = 14 .\nD. the function a ( x ) .\nE. the function ϕ ( x ) = 2x2 − x4 .", "pid": 149, "answer": "E", "category": "other cs"}, {"image": "images/image_150.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification accuracy .\nB. accuracy under different prediction percentage .\nC. accuracy of classification model for both classes .\nD. percentage of data used vs accuracy of -star classification .\nE. percentage of data used vs accuracy of positive/negative classification .", "pid": 150, "answer": "E", "category": "other cs"}, {"image": "images/image_151.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. percentage of data used vs accuracy of five-star classification .\nB. accuracy ( in % ) vs. data size .\nC. classification accuracy vs. snr for synthetic data .\nD. accuracy of classification model for both classes .\nE. percentage of data used vs accuracy of positive/negative classification .", "pid": 151, "answer": "A", "category": "other cs"}, {"image": "images/image_152.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. training cost on the arithmetic task when using associative lstm with 3 writing and reading heads .\nB. training cost on the variable assignment task .\nC. training cost on the arithmetic task .\nD. training cost on the xml task .\nE. training cost on the variable assignment task .", "pid": 152, "answer": "A", "category": "other cs"}, {"image": "images/image_153.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. estimation of injury rate .\nB. estimation of the conflict rate .\nC. convergence of crash rate estimation .\nD. estimation of the crash rate .\nE. convergence of collision rate estimation .", "pid": 153, "answer": "B", "category": "cs.R<PERSON>"}, {"image": "images/image_154.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. estimation of injury rate .\nB. estimation of the conflict rate .\nC. convergence of injury rate estimation .\nD. estimation of the crash rate .\nE. convergence of collision rate estimation .", "pid": 154, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_155.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the accuracy of different number of training set .\nB. the average accuracy of test .\nC. comparison of f1 scores on the test set .\nD. classification evaluation for ohsumed dataset .\nE. f1-score comparison for different levels of correlation for the condmat dataset .", "pid": 155, "answer": "C", "category": "cs.CL"}, {"image": "images/image_156.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance comparison between the optimal 2-jump algorithm and the halving 2-jump algorithm , as a function of β .\nB. performance comparison between the optimal 1-jump algorithm and the halving 1-jump algorithm , as a function of β .\nC. performance comparison between the optimal 3-jump algorithm and the halving 3-jump algorithm , as a function of β. notably , performance is nearly the same for all values of β > 5. the bigger discrepancy is observed for values of β close to 4 , for which also the jump steps between the two algorithms exhibit the larger gaps ( see figure 10 ) .\nD. comparison of the two jump choices between the 2-jump algorithm and the halving 2-jump algorithm , as a function of β. the first jump of each algorithm is always no smaller than the second one , and eventually they all attain the value 2π − β .\nE. comparison of jump choices between the optimal 1-jump algorithm and the halving 1-jump algorithm , as a function of β .", "pid": 156, "answer": "A", "category": "other cs"}, {"image": "images/image_157.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance comparison between the optimal 3-jump algorithm and the halving 3-jump algorithm , as a function of β. notably , performance is nearly the same for all values of β > 5. the bigger discrepancy is observed for values of β close to 4 , for which also the jump steps between the two algorithms exhibit the larger gaps ( see figure 10 ) .\nB. performance comparison between the optimal 1-jump algorithm and the halving 1-jump algorithm , as a function of β .\nC. performance comparison between the optimal 2-jump algorithm and the halving 2-jump algorithm , as a function of β .\nD. comparison of jump choices between the optimal 1-jump algorithm, the halving 1-jump algorithm and the avg. 1-jump algorithm, as a function of β .\nE. comparison of the two jump choices between the optimal 2-jump algorithm and the halving 2-jump algorithm , as a function of β. the first jump of each algorithm is always no smaller than the second one , and eventually they all attain the value 2π − β .", "pid": 157, "answer": "B", "category": "other cs"}, {"image": "images/image_158.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ml normalized spectrum with perfect csi , θt = 23◦ , θj = 40◦ and pj = 4pt .\nB. ml normalized spectrum with perfect csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nC. ml normalized spectrum without csi , θt = 23◦ , θj = 40◦ and pj = 4pt .\nD. ml normalized spectrum without csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nE. ml normalized spectrum without csi , θt = 12◦ , θj = 50◦ and pj = pt .", "pid": 158, "answer": "E", "category": "cross-list"}, {"image": "images/image_159.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ml normalized spectrum with perfect csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nB. ml normalized spectrum without csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nC. ml normalized spectrum without csi , θt = 16◦ , θj = 40◦ and pj = 4pt .\nD. ml normalized spectrum without csi , θt = 12◦ , θj = 50◦ and pj = pt .\nE. ml normalized spectrum with perfect csi , θt = 23◦ , θj = 40◦ and pj = 4pt .", "pid": 159, "answer": "E", "category": "cross-list"}, {"image": "images/image_160.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ml normalized spectrum without csi , θt = 23◦ , θj = 35◦ and pj = 4pt .\nB. ml normalized spectrum with perfect csi , θt = 33◦ , θj = −63◦ and pj = 2pt .\nC. ml normalized spectrum without csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nD. ml normalized spectrum with perfect csi , θt = 23◦ , θj = 35◦ and pj = 4pt .\nE. ml normalized spectrum without csi , θt = 12◦ , θj = 50◦ and pj = pt .", "pid": 160, "answer": "C", "category": "cross-list"}, {"image": "images/image_161.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ml normalized spectrum without csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nB. ml normalized spectrum without csi , θt = 23◦ , θj = 40◦ and pj = 4pt .\nC. ml normalized spectrum with perfect csi , θt = 43◦ , θj = −63◦ and pj = 2pt .\nD. ml normalized spectrum with perfect csi , θt = 28◦ , θj = 40◦ and pj = 4pt .\nE. ml normalized spectrum without csi , θt = 12◦ , θj = 50◦ and pj = pt .", "pid": 161, "answer": "B", "category": "cross-list"}, {"image": "images/image_162.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 2 im/dd mimo-owc with l = 4 .\nB. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 1 im/dd miso-owc with different variances .\nC. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 1 im/dd mimo-owc with l = 3 .\nD. average codeword ( x ( s ) ) performances of cstbcs and rc for n ×m im/dd mimo-owc with l = 3 and k = 5 .\nE. average codeword ( x ( p ) ) performances of cstbcs and rc for 2× 1 im/dd mimo-owc .", "pid": 162, "answer": "E", "category": "cross-list"}, {"image": "images/image_163.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 2 im/dd mimo-owc with l = 4 .\nB. average codeword ( x ( s ) ) performances of cstbcs and rc for n ×m im/dd mimo-owc with l = 3 and k = 5 .\nC. average codeword ( x ( p ) ) performances of cstbcs and rc for 2× 1 im/dd mimo-owc .\nD. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 1 im/dd mimo-owc with l = 3 .\nE. average codeword ( x ( s ) ) performances of cstbcs and rc for 2× 1 im/dd miso-owc with different variances .", "pid": 163, "answer": "D", "category": "cross-list"}, {"image": "images/image_164.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. taskset schedulability as the task utilization per vcpu increases .\nB. taskset schedulability as the number of tasks increases .\nC. taskset schedulability when tasks have medium memory intensity .\nD. taskset schedulability as the number of cores increases .\nE. taskset schedulability as the ratio of memory-intensive tasks increases .", "pid": 164, "answer": "B", "category": "cs.DC"}, {"image": "images/image_165.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average ber among all users against snr , m = 10 , n = 7 , 8 , 9 , 10 with ρ = 0 .\nB. average ber among all users against snr , m = 20 , n = 1 with ρ = 0 , 0.3 exp ( 0.5j ) , 0.6 exp ( 0.5j ) and 0.9 exp ( 0.5j ) .\nC. average ber among all users against snr , m = 20 , n = 14 , 17 , 19 , 20 with ρ = 0 .\nD. average ber performance against snr with m = 6 , n = 2 with different ρ. each user uses a -qam .\nE. average ber among all users against snr , m = 20 , n = 20 with ρ = 0 , 0.3 exp ( 0.5j ) and 0.6 exp ( 0.5j ) .", "pid": 165, "answer": "E", "category": "cross-list"}, {"image": "images/image_166.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the pos_max_max_bump_on_decreasing_sequence constraint of the example slot .\nB. illustrating the pos_min_min_bump_on_decreasing_sequence constraint of the example slot .\nC. illustrating the pos_max_surf_bump_on_decreasing_sequence constraint of the example slot .\nD. illustrating the pos_min_max_bump_on_decreasing_sequence constraint of the example slot .\nE. illustrating the pos_max_min_bump_on_decreasing_sequence constraint of the example slot .", "pid": 166, "answer": "E", "category": "cs.AI"}, {"image": "images/image_167.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the pos_min_max_increasing_sequence constraint of the example slot .\nB. illustrating the pos_max_min_increasing_sequence constraint of the example slot .\nC. illustrating the pos_min_max_decreasing_sequence constraint of the example slot .\nD. illustrating the pos_max_min_decreasing_sequence constraint of the example slot .\nE. illustrating the pos_max_max_decreasing_sequence constraint of the example slot .", "pid": 167, "answer": "D", "category": "cs.AI"}, {"image": "images/image_168.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the pos_min_min_dip_on_increasing_sequence constraint of the example slot .\nB. illustrating the pos_max_min_dip_on_increasing_sequence constraint of the example slot .\nC. illustrating the pos_max_max_dip_on_increasing_sequence constraint of the example slot .\nD. illustrating the pos_min_max_dip_on_increasing_sequence constraint of the example slot .\nE. illustrating the pos_max_surf_dip_on_increasing_sequence constraint of the example slot .", "pid": 168, "answer": "B", "category": "cs.AI"}, {"image": "images/image_169.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the pos_max_min_decreasing_sequence constraint of the example slot .\nB. illustrating the pos_max_max_increasing_sequence constraint of the example slot .\nC. illustrating the pos_min_max_increasing_sequence constraint of the example slot .\nD. illustrating the pos_max_min_increasing_sequence constraint of the example slot .\nE. illustrating the pos_min_max_strictly_increasing_sequence constraint of the example slot .", "pid": 169, "answer": "D", "category": "cs.AI"}, {"image": "images/image_170.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the decreasing_width_increasing_sequence constraint of the example slot .\nB. illustrating the decreasing_width_strictly_increasing_sequence constraint of the example slot .\nC. illustrating the increasing_width_increasing_sequence constraint of the example slot .\nD. illustrating the decreasing_width_decreasing_sequence constraint of the example slot .\nE. illustrating the increasing_width_decreasing_sequence constraint of the example slot .", "pid": 170, "answer": "A", "category": "cs.AI"}, {"image": "images/image_171.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the max_max_dip_on_increasing_sequence constraint of the example slot .\nB. illustrating the max_min_dip_on_increasing_sequence constraint of the example slot .\nC. illustrating the min_min_dip_on_increasing_sequence constraint of the example slot .\nD. illustrating the sum_max_dip_on_increasing_sequence constraint of the example slot .\nE. illustrating the min_max_dip_on_increasing_sequence constraint of the example slot .", "pid": 171, "answer": "E", "category": "cs.AI"}, {"image": "images/image_172.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. illustrating the max_min_bump_on_decreasing_sequence constraint of the example slot .\nB. illustrating the min_surf_bump_on_decreasing_sequence constraint of the example slot .\nC. illustrating the min_max_bump_on_decreasing_sequence constraint of the example slot .\nD. illustrating the min_min_bump_on_decreasing_sequence constraint of the example slot .\nE. illustrating the sum_min_bump_on_decreasing_sequence constraint of the example slot .", "pid": 172, "answer": "D", "category": "cs.AI"}, {"image": "images/image_173.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. evolution of the error 2 vs. the wavenumber k , for m = 2 , nλ = 10 and p = 1 , ... , 6 .\nB. evolution of the error 2 vs. the discretization density nλ for k = 40 and p = 1 , ... , 4 .\nC. evolution of the error 2 vs. the wavenumber k for m = 2 , nλ = 10 and p = 1 , ... , 8 .\nD. evolution of the error 2 vs. the wavenumber k , for nλ = 10 and p = 1 , ... , 5 .\nE. evolution of the error 2 vs. the wavenumber k , for nλ = 20 and various approximation orders p = 1 , ... , 6 .", "pid": 173, "answer": "D", "category": "cross-list"}, {"image": "images/image_174.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. normalized revenues versus total number of ads for line count=40 .\nB. normalized revenues versus total number of ads for line count=45 .\nC. normalized revenues versus total number of ads for line count=35 .\nD. normalized revenues versus total number of ads for line count=30 .\nE. normalized revenues versus total number of ads for line count=25 .", "pid": 174, "answer": "A", "category": "cross-list"}, {"image": "images/image_175.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves for individual classes for the test data from the random forest model .\nB. roc curves for random forest algorithms .\nC. roc curves for naive bayes algorithms .\nD. roc curves for each classification model .\nE. roc curves for the test data from the random forest model .", "pid": 175, "answer": "B", "category": "other cs"}, {"image": "images/image_176.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. sgj execution time ( type ii , 100 % connectivity ) .\nB. rjg execution time ( type i , 100 % connectivity ) .\nC. ngj execution time ( type ii , no connectivity ) .\nD. rgj execution time ( type ii , 100 % connectivity ) .\nE. sgj execution time ( type i , 100 % connectivity ) .", "pid": 176, "answer": "D", "category": "cross-list"}, {"image": "images/image_177.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. propulsion power coefficient .\nB. propulsion thrust coefficient .\nC. propulsion power at selected pitch angles and rpm .\nD. propulsion efficiency .\nE. propulsion efficiency at selected pitch angles and rpm .", "pid": 177, "answer": "B", "category": "cs.R<PERSON>"}, {"image": "images/image_178.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. propulsion efficiency at selected pitch angles and rpm .\nB. propulsion power at selected pitch angles and rpm .\nC. propulsion efficiency .\nD. propulsion power coefficient .\nE. propulsion thrust coefficient .", "pid": 178, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_179.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. error probability as a function of m for the following settings : n = 20 , markovian sequence of states ρ = 0.95 , ε = 0.15 and α = 0.45 .\nB. error probability as a function of α for the following setting : n = 20 , markovian sequence of states ρ = 0.95 , ε = 0.15 , m = 30 and pmal = 1.0 .\nC. error probability as a function of α for the following setting : n = 20 , markovian sequence of states ρ = 0.95 , ε = 0.15 , m = and pmal = 1.0 .\nD. error probability as a function of α for the following setting : n = 20 , independent sequence of states ρ = 0.5 , ε = 0.15 , m = 10 and pmal = 1.0 .\nE. error probability as a function of α for the following setting : n = 20 , markovian sequence of states ρ = 0.95 , ε = 0.15 , m = 30 and pmal = 0.5 .", "pid": 179, "answer": "A", "category": "cs.SY"}, {"image": "images/image_180.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. accuracy with varied number of neighbors k .\nB. comparison of domain discrepancy and target accuracy .\nC. accuracy with varied target value t .\nD. achievable accuracy for different target values .\nE. accuracy with missing data .", "pid": 180, "answer": "C", "category": "cs.SE"}, {"image": "images/image_181.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. impacts of the reservation wage c on the gross profit f ∗ ( · ) , privacy level r∗ , subscription fee p∗s , and total data cost .\nB. gross profit f ∗ ( · ) , subscription revenue , subscription fee p∗s , and total data cost under varied privacy level r .\nC. impacts of the reservation wage c1 on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , and subscription fee p∗b .\nD. impacts of the degree of contingency γ on the gross profit g∗s ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and total data cost .\nE. impacts of the degree of contingency γ on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and total data cost .", "pid": 181, "answer": "C", "category": "other cs"}, {"image": "images/image_182.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. gross profit f ∗ ( · ) , subscription revenue , subscription fee p∗s , and total data cost under varied privacy level r .\nB. impacts of the degree of contingency γ on the gross profit g∗s ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and total data cost .\nC. impacts of the degree of contingency γ on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and average data cost .\nD. impacts of the reservation wage c1 on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , and subscription fee p∗b .\nE. impacts of the reservation wage c on the gross profit f ∗ ( · ) , privacy level r∗ , subscription fee p∗s , and total data cost .", "pid": 182, "answer": "B", "category": "other cs"}, {"image": "images/image_183.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. impacts of the reservation wage c1 on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , and subscription fee p∗b .\nB. impacts of the degree of contingency γ on the gross profit g∗s ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and total data cost .\nC. impacts of the degree of contingency γ on the gross profit g∗c ( · ) , privacy levels r∗1 and r∗2 , subscription fee p∗b , and total data cost .\nD. impacts of the reservation wage c on the gross profit f ∗ ( · ) , privacy level r∗ , subscription fee p∗s , and total data cost .\nE. gross profit f ∗ ( · ) , subscription revenue , subscription fee p∗s , and total data cost under varied privacy level r .", "pid": 183, "answer": "D", "category": "other cs"}, {"image": "images/image_184.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. change of expected accuracy interval with respect to the number of training samples observed on all three cnn models .\nB. recognition accuracy by using different numbers of training samples using svm .\nC. change of expected accuracy interval on class basis with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nD. change of class specific dataset robustness with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nE. change of robustness with respect to the number of training samples observed on all three cnn models .", "pid": 184, "answer": "D", "category": "cs.LG"}, {"image": "images/image_185.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. change of robustness with respect to the number of training samples observed on all three cnn models .\nB. change of class specific dataset robustness with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nC. accuracy for different numbers of training samples .\nD. change of expected accuracy interval on class basis with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nE. change of expected accuracy interval with respect to the number of training samples observed on all three cnn models .", "pid": 185, "answer": "E", "category": "cs.LG"}, {"image": "images/image_186.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. change of class specific dataset robustness with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nB. change of robustness with respect to the number of training samples observed on all three cnn models .\nC. change of expected accuracy interval with respect to the number of training samples observed on all three cnn models .\nD. change of expected accuracy interval on class basis with respect to the number of training samples observed on cnn-3 for digit-0 , digit-1 and digit-5 .\nE. recognition accuracy by using different numbers of training samples using svm .", "pid": 186, "answer": "D", "category": "cs.LG"}, {"image": "images/image_187.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. rank-50 cmc plot .\nB. rank-10 cmc plot .\nC. rank-5 cmc plot .\nD. rank-25 cmc plot .\nE. rank-1 cmc plot .", "pid": 187, "answer": "C", "category": "cs.CV"}, {"image": "images/image_188.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. rank-5 cmc plot .\nB. rank-50 cmc plot .\nC. rank-1 cmc plot .\nD. rank-25 cmc plot .\nE. rank-10 cmc plot .", "pid": 188, "answer": "E", "category": "cs.CV"}, {"image": "images/image_189.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. rank-50 cmc plot .\nB. rank-5 cmc plot .\nC. rank-1 cmc plot .\nD. rank-10 cmc plot .\nE. rank-25 cmc plot .", "pid": 189, "answer": "E", "category": "cs.CV"}, {"image": "images/image_190.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. bounds and fer performance for bp and near-ml decoding of r = 1/ irregular ldpc codes of length n = 576 , where the following notations are used : “ shannon bound ” denotes the bound ( 4 ) , “ random code ” denotes the bound ( 6 ) computed for ensemble of linear codes , “ ldpc ( 4,8 ) -regular ” denotes the bound ( 6 ) computed for the gallager ensemble of the ( 4,8 ) -regular ldpc codes .\nB. bounds and fer performance for bp and near-ml decoding of r = 1/2 nonbinary ldpc code of length n = 1 over gf ( 28 ) [ 3 ] , where the following notations are used : “ shannon bound ” denotes the bound ( 4 ) , “ random code ” denotes the bound ( ) computed for ensemble of linear codes , “ nb ldpc ( 2,4 ) -regular ” denotes the bound ( ) computed for the gallager ensemble of binary images of the ( 2,4 ) -regular nb ldpc codes .\nC. bounds and fer performance for bp and near-ml decoding of r = 1/2 ( 4 , 8 ) -regular ldpc codes of length n = 96 , where the following notations are used : “ shannon bound ” denotes the bound ( 4 ) , “ random code ” denotes the bound ( 6 ) computed for ensemble of linear codes , “ ldpc ( 4,8 ) -regular ” denotes the bound ( 6 ) computed for the gallager ensemble of the ( 4,8 ) -regular ldpc codes .\nD. bounds and fer performance for bp and near-ml decoding of r = 1/3 irregular ldpc codes of length n = 576 , where the following notations are used : “ shannon bound ” denotes the bound ( ) , “ random code ” denotes the bound ( 6 ) computed for ensemble of linear codes , “ ldpc ( ,6 ) -regular ” denotes the bound ( 6 ) computed for the gallager ensemble of the ( ,6 ) -regular ldpc codes .\nE. bounds and fer performance for bp and near-ml decoding of r = 2/ irregular ldpc codes of length n = 576 , where the following notations are used : “ shannon bound ” denotes the bound ( 4 ) , “ random code ” denotes the bound ( 6 ) computed for ensemble of linear codes , “ ldpc ( ,9 ) -regular ” denotes the bound ( 6 ) computed for the gallager ensemble of the ( ,9 ) -regular ldpc codes .", "pid": 190, "answer": "B", "category": "cross-list"}, {"image": "images/image_191.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. hme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the qr , fs and be heuristics .\nB. lme : ετz ( ϕr , e ) and ετz ( ϕr , e ) vs. nϕr for the qr , fs , and be heuristics .\nC. lme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the qr , fs and be heuristics .\nD. lme : nop vs. nϕr for functions dq and [ mqq|δq ] for the qr , fs and be heuristics .\nE. lme : ǫτz ( ϕr , e ) vs. nop for functions dq and [ mqq|δq ] for the qr , fs and be heuristics .", "pid": 191, "answer": "B", "category": "cs.R<PERSON>"}, {"image": "images/image_192.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. lme : nop vs. nϕr for functions dq and [ mqq|δq ] for the or , fs and be heuristics .\nB. hme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the qr , fs and be heuristics .\nC. lme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the qr , fs and be heuristics .\nD. lme : ǫτz ( ϕr , e ) vs. nop for functions dq and [ mqq|δq ] for the qr , fs and be heuristics .\nE. lme : ετz ( ϕr , e ) and ετz ( ϕr , e ) vs. nϕr for the qr , fs , and be heuristics .", "pid": 192, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_193.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. lme : nop vs. nϕr for functions dq and [ mqq|δq ] for the qr , fs and be heuristics .\nB. lme : ǫτz ( ϕr , e ) vs. nop for functions dq and [ mqq|δq ] for the qr , fs and be heuristics .\nC. lme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the qr , fs and be heuristics .\nD. hme : ǫz̈ ( ϕr , e ) and ǫz̈ ( ϕr , v ) vs. nϕr for the or , fs and be heuristics .\nE. lme : ετz ( ϕr , e ) and ετz ( ϕr , e ) vs. nϕr for the qr , fs , and be heuristics .", "pid": 193, "answer": "C", "category": "cs.R<PERSON>"}, {"image": "images/image_194.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. median of information time as a function of degree .\nB. information spreading capacity as a function of eccentricity .\nC. information spreading capacity as a function of out-degree .\nD. information spreading capacity as a function of degree .\nE. information spreading capacity as a function of in-degree .", "pid": 194, "answer": "D", "category": "other cs"}, {"image": "images/image_195.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. information spreading capacity as a function of in-degree .\nB. information spreading capacity as a function of eccentricity .\nC. median of information time as a function of degree .\nD. information spreading capacity as a function of degree .\nE. information spreading capacity as a function of out-degree .", "pid": 195, "answer": "A", "category": "other cs"}, {"image": "images/image_196.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. results of different entropy coefficient λ .\nB. information spreading capacity as a function of in-degree .\nC. information spreading capacity as a function of eccentricity .\nD. information spreading capacity as a function of degree .\nE. information spreading capacity as a function of out-degree .", "pid": 196, "answer": "E", "category": "other cs"}, {"image": "images/image_197.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. simulation results for 3σ under the disturbance torque .\nB. simulation results for 3σ under the initial cmgs momentum error .\nC. simulation results for 1σ under the disturbance torque .\nD. simulation results for 1σ under the initial cmgs momentum error .\nE. simulation results of the total momentum error magnitude under the initial cmgs momentum error .", "pid": 197, "answer": "D", "category": "cs.SY"}, {"image": "images/image_198.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. simulation results for 1σ under the disturbance torque .\nB. simulation results for 1σ under the initial cmgs momentum error .\nC. simulation results of the total momentum error magnitude under the initial cmgs momentum error .\nD. simulation results for 3σ under the disturbance torque .\nE. simulation results for 3σ under the initial cmgs momentum error .", "pid": 198, "answer": "E", "category": "cs.SY"}, {"image": "images/image_199.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. simulation results for 3σ under the disturbance torque .\nB. simulation results for 3σ under the initial cmgs momentum error .\nC. simulation results of the total momentum error magnitude under the initial cmgs momentum error .\nD. simulation results for 1σ under the initial cmgs momentum error .\nE. simulation results for 1σ under the disturbance torque .", "pid": 199, "answer": "A", "category": "cs.SY"}, {"image": "images/image_200.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. convergence curves of 3 neural networks with binary activations on the wsj1 dataset .\nB. the rmse convergence curves of synthetic data of our proposed methods ( 5db ) .\nC. the convergence curves of the four methods for training the neural network .\nD. convergence curves of different networks trained with resnet-50 architecture on imagenet .\nE. convergence curves of sr networks trained on observed data combined with different types of synthetic data .", "pid": 200, "answer": "A", "category": "cs.CL"}, {"image": "images/image_201.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mse versus snr for doa estimation in the case of mutual coupling .\nB. rmse versus snr of the proposed snr estimator .\nC. mse versus snr for doa estimation in the case of gain-phase error .\nD. rmse versus snr for esprit-based doa estimators .\nE. channel estimation mse versus snr .", "pid": 201, "answer": "C", "category": "cs.LG"}, {"image": "images/image_202.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. power of the proposed 0.05-level test under the -community stochastic block models .\nB. the stochastic block model with 1000 and 1000 nodes in each community .\nC. the stochastic block model with 100 and 1900 nodes in each community .\nD. the stochastic block model with 700 and 1300 nodes in each community .\nE. the stochastic block model with 400 and 1600 nodes in each community .", "pid": 202, "answer": "B", "category": "other cs"}, {"image": "images/image_203.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. average number of iterations required to decode the pldpc-hadamard code with r = 10 and k = 460 , 800 .\nB. average number of iterations for convergence by nash equilibrium computation algorithm .\nC. average number of iterations needed for the convergence of algorithm 1 , versus the number of data points n ( 1000 realizations ; k = 3 , n = 30 ) .\nD. average number of iterations needed for the convergence of algorithm 1 , versus the number of data points n ( 1000 realizations ; d = 10 , k = 3 ) .\nE. average number of iterations needed for the convergence of algorithm 1 , versus the number of data points n ( 1000 realizations ; d = 10 , n = 20 ) .", "pid": 203, "answer": "D", "category": "other cs"}, {"image": "images/image_204.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the evolution of numerical solutions of 2y to the optimal solution .\nB. the evolution of numerical solutions in the xy coordinate plane to the optimal solution .\nC. the evolution of numerical solutions of 2x to the optimal solution .\nD. the evolution of numerical solutions in the m m coordinate plane to the optimal solution .\nE. the evolution of numerical solutions of 1x to the optimal solution .", "pid": 204, "answer": "B", "category": "cross-list"}, {"image": "images/image_205.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. parallel case ( n = 8 ) : % ∆optsum .\nB. parallel case ( n = 8 ) : % ∆opt .\nC. parallel case ( n = 4 ) : % ∆opt for 12 runs .\nD. parallel case ( n = ) : % ∆optsum .\nE. parallel case ( n = 4 ) : % ∆optsum .", "pid": 205, "answer": "B", "category": "cross-list"}, {"image": "images/image_206.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. parallel case ( n = 8 ) : % ∆opty .\nB. parallel case ( n = 4 ) : % ∆opt .\nC. parallel case ( n = 4 ) : % ∆optsum .\nD. sequential case : % ∆optx .\nE. parallel case ( n = 8 ) : % ∆optsum .", "pid": 206, "answer": "B", "category": "cross-list"}, {"image": "images/image_207.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. : roc curves of different weight factor configurations in the range [ 100,200 ] .\nB. : roc curves of different threshold of the cnn probability with false positive suppression .\nC. : roc curves of multiple trainings of the same network configuration .\nD. : roc curves of different weight factor configurations in the range [ 1,100 ] .\nE. : roc curves of different step learning rate configurations .", "pid": 207, "answer": "E", "category": "cs.CV"}, {"image": "images/image_208.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. : roc curves of different weight factor configurations in the range [ 1,100 ] .\nB. : roc curves of different step learning rate configurations .\nC. : roc curves of different weight factor configurations in the range [ 100,200 ] .\nD. : roc curves of different threshold of the cnn probability with false positive suppression .\nE. : roc curves of multiple trainings of the same network configuration .", "pid": 208, "answer": "C", "category": "cs.CV"}, {"image": "images/image_209.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. throughput performance in 100 heterogeneous servers .\nB. throughput performance in 10 heterogeneous servers .\nC. throughput performance in 10 heterogeneous servers with poisson arrival and constant service .\nD. throughput performance in 10 heterogeneous servers with poisson arrival and bursty service .\nE. throughput performance in 50 heterogeneous servers .", "pid": 209, "answer": "A", "category": "cs.DC"}, {"image": "images/image_210.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. throughput performance in 10 heterogeneous servers .\nB. delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nC. throughput performance in 10 heterogeneous servers with poisson arrival and constant service .\nD. throughput performance in 10 heterogeneous servers with bursty arrival and poisson service .\nE. throughput performance in 10 heterogeneous servers with poisson arrival and bursty service .", "pid": 210, "answer": "D", "category": "cs.DC"}, {"image": "images/image_211.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nB. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and constant service .\nC. delay performance under 10 homogeneous servers with poisson arrival and constant service .\nD. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and bursty service .\nE. delay performance under 10 homogeneous servers with poisson arrival and bursty service .", "pid": 211, "answer": "C", "category": "cs.DC"}, {"image": "images/image_212.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nB. delay performance under 10 homogeneous servers with poisson arrival and constant service .\nC. heavy-traffic delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nD. delay performance under 10 homogeneous servers with poisson arrival and bursty service .\nE. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and bursty service .", "pid": 212, "answer": "D", "category": "cs.DC"}, {"image": "images/image_213.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. delay performance under 10 homogeneous servers with poisson arrival and bursty service .\nB. delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nC. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and bursty service .\nD. delay performance under 10 homogeneous servers with poisson arrival and constant service .\nE. heavy-traffic delay performance under 10 homogeneous servers with bursty arrival and poisson service .", "pid": 213, "answer": "B", "category": "cs.DC"}, {"image": "images/image_214.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and constant service .\nB. delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nC. delay performance under 10 homogeneous servers with poisson arrival and bursty service .\nD. heavy-traffic delay performance under 10 homogeneous servers with bursty arrival and poisson service .\nE. heavy-traffic delay performance under 10 homogeneous servers with poisson arrival and bursty service .", "pid": 214, "answer": "D", "category": "cs.DC"}, {"image": "images/image_215.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. precision-recall curves of different methods .\nB. precision-recall curves of our model ( candis ) against traditional methods .\nC. precision-recall curves for the proposed model and various baseline models .\nD. the precision/recall curves of previous state-of-the-art methods and our proposed framework .\nE. precision-recall curves for the proposed model and various baselines .", "pid": 215, "answer": "B", "category": "cs.CL"}, {"image": "images/image_216.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. change of cumulative regret with respect to number of turns where s = 10 , n = 5 , α = 1 and µ = µ1 .\nB. change of cumulative regret with respect to number of turns where s = 10 , n = , α = 0 and µ = µ2 .\nC. change of cumulative regret with respect to number of turns where s = 10 , n = 5 , α = 1 and µ = µ2 .\nD. change of cumulative regret with respect to α where s = 10 , n = 5 .\nE. change of cumulative regret with respect to number of turns where s = 10 , n = 5 , α = 1 and µ = µ2 .", "pid": 216, "answer": "B", "category": "cs.LG"}, {"image": "images/image_217.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. discrete transformation using 30 hidden states .\nB. discrete transformation using 5 hidden states .\nC. discrete transformation using 20 hidden states .\nD. eventid transformation using 15 hidden states .\nE. discrete transformation using 15 hidden states .", "pid": 217, "answer": "B", "category": "cs.CR"}, {"image": "images/image_218.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. eventid transformation using 30 hidden states .\nB. eventid transformation using 15 hidden states .\nC. eventid transformation using 5 hidden states .\nD. eventid transformation using 20 hidden states .\nE. discrete transformation using 200 hidden states .", "pid": 218, "answer": "D", "category": "cs.CR"}, {"image": "images/image_219.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. eventid transformation using 20 hidden states .\nB. eventid transformation using 30 hidden states .\nC. discrete transformation using 30 states .\nD. eventid transformation using 15 hidden states .\nE. eventid transformation using 5 hidden states .", "pid": 219, "answer": "B", "category": "cs.CR"}, {"image": "images/image_220.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. discrete transformation using 15 hidden states .\nB. discrete transformation using 5 hidden states .\nC. discrete transformation using 20 hidden states .\nD. discrete transformation using 30 hidden states .\nE. eventid transformation using 31 hidden states .", "pid": 220, "answer": "D", "category": "cs.CR"}, {"image": "images/image_221.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves on subset 10-20-30-100 .\nB. roc curves for dr detection .\nC. roc curves on full dataset .\nD. roc curves for different models .\nE. roc curves with w values .", "pid": 221, "answer": "E", "category": "cs.CR"}, {"image": "images/image_222.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. error time-history of the crank-nicolson scheme .\nB. error time-history of the regularized scheme .\nC. error time-history of the lax-wendroff scheme .\nD. error time-history of the scheme of higher accuracy .\nE. time-histories of the solution for µ = 1 .", "pid": 222, "answer": "C", "category": "other cs"}, {"image": "images/image_223.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. recognition error ( % ) on ar face database .\nB. recognition accuracy for gatech face database .\nC. recognition rates on faces 95 .\nD. recognition rates on faces 96 .\nE. recognition rates on faces 94 .", "pid": 223, "answer": "D", "category": "cs.CV"}, {"image": "images/image_224.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. recognition rates on faces 96 .\nB. recognition rates on faces 94 .\nC. recognition rates on faces 95 .\nD. face recognition rate of pca-like methods for the color feret face database .\nE. recognition error ( % ) on ar face database .", "pid": 224, "answer": "B", "category": "cs.CV"}, {"image": "images/image_225.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the comparison of bpcrw with different α against rmss under the different decaying parameters λ. tkde is the source object .\nB. comparison of nmi for rmss under different λ with optimal nmi for pathsim under different α on bioin .\nC. comparison of nmi for rmss under different λ with optimal nmi for pathsim under different α on dblpc .\nD. the comparison of pathsim with different α against rmss under the different decaying parameters λ. tkde is the source object .\nE. comparison of nmi for rmss under different λ with optimal nmi for bscse under different α on dblpc .", "pid": 225, "answer": "E", "category": "other cs"}, {"image": "images/image_226.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. sensitivity of bpcrw to different meta-paths on dblpr in terms of ranking with tkde as the source object .\nB. the comparison of pathsim with different α against rmss under the different decaying parameters λ. cikm is the source object .\nC. the comparison of pathsim with different α against rmss under the different decaying parameters λ. tkde is the source object .\nD. comparison of nmi for rmss under different λ with optimal nmi for bscse under different α on dblpc .\nE. the comparison of bpcrw with different α against rmss under the different decaying parameters λ. tkde is the source object .", "pid": 226, "answer": "E", "category": "other cs"}, {"image": "images/image_227.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the evolution of numerical solutions in the x y coordinate plane to the optimal solution .\nB. the evolution of numerical solutions of 1x to the optimal solution .\nC. the evolution of numerical solutions of 2x to the optimal solution .\nD. the evolution of numerical solutions of u to the optimal solution .\nE. the evolution of numerical solutions in the x y coordinate plane to the optimal solution .", "pid": 227, "answer": "C", "category": "cs.SY"}, {"image": "images/image_228.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber results for bch ( 6,6 ) code .\nB. ber results for bch ( 127,106 ) code .\nC. ber results for bch ( 3,45 ) code trained with multiloss .\nD. ber results for bch ( 63,45 ) code .\nE. ber results for bch ( 63,5 ) code .", "pid": 228, "answer": "D", "category": "cross-list"}, {"image": "images/image_229.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. validation accuracy while training for wisconsin breast cancer dataset .\nB. validation error while training for wisconsin breast cancer dataset .\nC. training time error for breast cancer problem .\nD. training time error for breast cancer problem .\nE. training error while training for wisconsin breast cancer dataset .", "pid": 229, "answer": "E", "category": "cs.LG"}, {"image": "images/image_230.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. error rate while training a single hmm .\nB. training time error for breast cancer problem .\nC. validation error while training for wisconsin breast cancer dataset .\nD. training error while training for wisconsin breast cancer dataset .\nE. validation accuracy while training for wisconsin breast cancer dataset .", "pid": 230, "answer": "C", "category": "cs.LG"}, {"image": "images/image_231.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. time evolution of θ ( t ) of nodes with degree 20 for different values of δ20 .\nB. time evolution of the probability θ ( t ) for the iobt mean-field game and the finite iobt game .\nC. time evolution of the probability θ ( t ) for different values of βe20 .\nD. time evolution of the potential function v for different choices of α .\nE. time evolution of the acceptance probability of nodes with degree 20 for different values of βe20 .", "pid": 231, "answer": "C", "category": "other cs"}, {"image": "images/image_232.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. variation ( interpolated ) of previous word λ about distance 0 .\nB. variation ( interpolated ) of posterior word λ about distance 0 .\nC. variation ( interpolated ) of posterior word λ about distance -2 .\nD. variation ( interpolated ) of previous word λ about distance 1 .\nE. variation ( interpolated ) of posterior word λ about distance -1 .", "pid": 232, "answer": "C", "category": "cs.CL"}, {"image": "images/image_233.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. variation of error throughout the epochs .\nB. log-log error plot .\nC. error rates generated at different values of 351 .\nD. training error and test error ( up to 7500 epochs ) .\nE. error variation depending on the size of history .", "pid": 233, "answer": "A", "category": "cs.CL"}, {"image": "images/image_234.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the control inputs of the actuators of agent 1 .\nB. the control inputs of agent 1 with −8.5 ≤ u1 , j ( t ) ≤ 8.5 .\nC. the control inputs of agent 3 with −.5 ≤ u3 , j ( t ) ≤ .5 .\nD. the control inputs of the actuators of agent 3 .\nE. the control inputs of agent 2 with −8.5 ≤ u2 , j ( t ) ≤ 8.5 .", "pid": 234, "answer": "B", "category": "cs.R<PERSON>"}, {"image": "images/image_235.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the control inputs of agent 2 with −8.5 ≤ u2 , j ( t ) ≤ 8.5 .\nB. the control inputs of the actuators of agent 1 .\nC. the control inputs of the actuators of agent 2 .\nD. the control inputs of agent 1 with −8.5 ≤ u1 , j ( t ) ≤ 8.5 .\nE. the control inputs of agent 3 with −.5 ≤ u3 , j ( t ) ≤ .5 .", "pid": 235, "answer": "A", "category": "cs.R<PERSON>"}, {"image": "images/image_236.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the control inputs of agent 2 with −8.5 ≤ u2 , j ( t ) ≤ 8.5 .\nB. the control inputs of agent 1 with −8.5 ≤ u1 , j ( t ) ≤ 8.5 .\nC. the control inputs of the actuators of agent 3 .\nD. the control inputs of the actuators of agent 1 .\nE. the control inputs of agent 3 with −.5 ≤ u3 , j ( t ) ≤ .5 .", "pid": 236, "answer": "E", "category": "cs.R<PERSON>"}, {"image": "images/image_237.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. effect of number of epochs on model accuracy .\nB. mean accuracy versus number of epochs .\nC. number of epochs vs loss val and accuracy .\nD. accuracy vs. epochs .\nE. loss and accuracy vs epochs .", "pid": 237, "answer": "C", "category": "cs.CL"}, {"image": "images/image_238.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. asc versus λ for different values of µd , µe , κd = κe = 3 , and md = me = 2 .\nB. spsc versus λ for different values of md , me , κd = κe = 3 , and µd = µe = 2 .\nC. spso versus λ for different values of µd , µe , κd = κe = 3 , and md = me = 2 .\nD. sopl versus λ for different values of md , me , κd = κe = 3 , µd = µe = 2 , and rs = 1 .\nE. sop versus λ for different values of md , me , κd = κe = 3 , µd = µe = 2 , and rs = 1 .", "pid": 238, "answer": "B", "category": "cross-list"}, {"image": "images/image_239.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. eδ2 versus snr for the rate /7 optimized code compared with hamming ( 7 , ) code .\nB. eδ2 versus snr for the rate 8/12 optimized code compared with hamming ( 12 , 8 ) code .\nC. eδ2 versus snr for the rate 3/8 optimized code compared with hadamard ( 8,3 ) code .\nD. eδ1 versus snr for the rate 4/7 optimized code compared with hamming ( 7,4 ) code .\nE. eδ2 versus snr for the rate 4/7 optimized linear code compared with hamming ( 7,4 ) code .", "pid": 239, "answer": "D", "category": "cross-list"}, {"image": "images/image_240.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves for nist special database 4 .\nB. roc curves when 300 points are used for training .\nC. roc curves for fvc2002 datasets .\nD. roc curves of the algorithms for ssi data .\nE. roc curves on lfw dataset .", "pid": 240, "answer": "C", "category": "cs.CR"}, {"image": "images/image_241.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves of classification techniques .\nB. roc curves for each classification model .\nC. roc curves for the different machine learning models .\nD. roc curves for classifying process name spoof attacks .\nE. roc curves for classifying domain name spoof attacks .", "pid": 241, "answer": "D", "category": "cs.CR"}, {"image": "images/image_242.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves for classifying domain name spoof attacks .\nB. roc curves for the different machine learning models .\nC. intra-domain roc curves for the classifiers on the computer science dataset .\nD. roc curves of classification techniques .\nE. roc curves for classifying process name spoof attacks .", "pid": 242, "answer": "A", "category": "cs.CR"}, {"image": "images/image_243.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. matchings using pattern 3 .\nB. pattern counts .\nC. comparison of the matchings for different patterns .\nD. matchings using pattern 1 .\nE. matchings using pattern 2 .", "pid": 243, "answer": "D", "category": "other cs"}, {"image": "images/image_244.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. matchings using pattern 3 .\nB. pattern counts .\nC. matchings using pattern 1 .\nD. comparison of the matchings for different patterns .\nE. matchings using pattern 2 .", "pid": 244, "answer": "E", "category": "other cs"}, {"image": "images/image_245.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. matchings using pattern 1 .\nB. pattern counts .\nC. matchings using pattern 3 .\nD. comparison of the matchings for different patterns .\nE. matchings using pattern 2 .", "pid": 245, "answer": "C", "category": "other cs"}, {"image": "images/image_246.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the figure depicts pmd over snr for n = 104 , m = 300 , ks = 3 , u = 4 .\nB. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = , u = 8 .\nC. the figure depicts pmd over snr for n = 1024 , m = 00 , ks = , u = 8 .\nD. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = 6 , u = 4 .\nE. the figure depicts pmd over snr for n = 102 , m = 300 , ks = 3 , u = 16 .", "pid": 246, "answer": "C", "category": "cross-list"}, {"image": "images/image_247.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the figure depicts pmd over snr for n = 1024 , m = 00 , ks = , u = 8 .\nB. the figure depicts pmd over snr for n = 102 , m = 300 , ks = 3 , u = 16 .\nC. the figure depicts pmd over snr for n = 104 , m = 300 , ks = 3 , u = 4 .\nD. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = 6 , u = 4 .\nE. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = , u = 8 .", "pid": 247, "answer": "B", "category": "cross-list"}, {"image": "images/image_248.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = , u = 8 .\nB. the figure depicts pmd over snr for n = 104 , m = 300 , ks = 3 , u = 4 .\nC. the figure depicts pmd over snr for n = 102 , m = 300 , ks = 3 , u = 16 .\nD. the figure depicts pmd over snr for n = 1024 , m = 300 , ks = 6 , u = 4 .\nE. the figure depicts pmd over snr for n = 1024 , m = 00 , ks = , u = 8 .", "pid": 248, "answer": "A", "category": "cross-list"}, {"image": "images/image_249.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. f1-score evolution with increasing number of observations from the user in the production experiment .\nB. evolution of accuracy for the splitcifar100 dataset .\nC. prediction accuracies in the comprehension experiment with synthetic data .\nD. accuracy evolution with increasing number of observations from the user in the comprehension experiment with synthetic data .\nE. results of the experiment ( i ) , accuracy for each model on different data proportions ( english corpus ) .", "pid": 249, "answer": "D", "category": "cs.CL"}, {"image": "images/image_250.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the recognition rates using iclap , product-based ( icp*iclap ) and weighted sum-based ( icp+iclap ) hybrid fusion approaches against different number of touches .\nB. the recognition rates using iclap , product-based ( bow*icp ) and weighted sum-based ( bow+icp ) decision fusion approaches against different number of touches .\nC. the recognition rates using iclap , productbased ( bow*icp*iclap ) and weighted sum-based ( bow+icp+iclap ) decision fusion approaches with both tactile and kinesthetic sensing pipelines against different number of touches .\nD. the recognition rates using iclap , product-based ( bow*iclap ) and weighted sum-based ( bow*iclap ) hybrid fusion approaches against different number of touches .\nE. the recognition rates with icp , bow and iclap when different number of touches are taken .", "pid": 250, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_251.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the recognition rates using iclap , product-based ( bow*icp ) and weighted sum-based ( bow+icp ) decision fusion approaches against different number of touches .\nB. the recognition rates with icp , bow and iclap when different number of touches are taken .\nC. the recognition rates using iclap , product-based ( icp*iclap ) and weighted sum-based ( icp+iclap ) hybrid fusion approaches against different number of touches .\nD. the recognition rates using iclap , productbased ( bow*icp*iclap ) and weighted sum-based ( bow+icp+iclap ) decision fusion approaches with both tactile and kinesthetic sensing pipelines against different number of touches .\nE. the recognition rates using iclap , product-based ( bow*iclap ) and weighted sum-based ( bow*iclap ) hybrid fusion approaches against different number of touches .", "pid": 251, "answer": "D", "category": "cs.R<PERSON>"}, {"image": "images/image_252.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the recognition rates using iclap , product-based ( bow*iclap ) and weighted sum-based ( bow*iclap ) hybrid fusion approaches against different number of touches .\nB. the recognition rates using iclap , product-based ( bow*icp ) and weighted sum-based ( bow+icp ) decision fusion approaches against different number of touches .\nC. the recognition rates with icp , bow and iclap when different number of touches are taken .\nD. the recognition rates using iclap , product-based ( icp*iclap ) and weighted sum-based ( icp+iclap ) hybrid fusion approaches against different number of touches .\nE. the recognition rates using iclap , productbased ( bow*icp*iclap ) and weighted sum-based ( bow+icp+iclap ) decision fusion approaches with both tactile and kinesthetic sensing pipelines against different number of touches .", "pid": 252, "answer": "B", "category": "cs.R<PERSON>"}, {"image": "images/image_253.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the recognition rates using iclap , product-based ( bow*icp ) and weighted sum-based ( bow+icp ) decision fusion approaches against different number of touches .\nB. the recognition rates using iclap , product-based ( bow*iclap ) and weighted sum-based ( bow*iclap ) hybrid fusion approaches against different number of touches .\nC. the recognition rates using iclap , productbased ( bow*icp*iclap ) and weighted sum-based ( bow+icp+iclap ) decision fusion approaches with both tactile and kinesthetic sensing pipelines against different number of touches .\nD. the recognition rates with icp , bow and iclap when different number of touches are taken .\nE. the recognition rates using iclap , product-based ( icp*iclap ) and weighted sum-based ( icp+iclap ) hybrid fusion approaches against different number of touches .", "pid": 253, "answer": "E", "category": "cs.R<PERSON>"}, {"image": "images/image_254.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. accuracy @ topn on 80 % training data .\nB. accuracy of proposed methods with different k value set-up .\nC. comparison of top-p accuracy with existing techniques .\nD. top-k accuracy , mean average precision @ k , and mean recall @ k .\nE. top-k accuracy comparison with existing techniques .", "pid": 254, "answer": "E", "category": "cs.SE"}, {"image": "images/image_255.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber performance of bicm-id with 4-d 16- and 16-qam over rayleigh fading channels .\nB. ber performance of bicm-id with 4-d 128- and 256-qam over rayleigh fading channels .\nC. error-floor bounds of ber for bicm-id with 4-d 16- and 32-qam over rayleigh fading channels .\nD. error-floor bounds of ber for bicm-id with 4-d 64-qam over rayleigh fading channels .\nE. error-floor bounds of ber for bicm-id with 4-d 8- and 256-qam over rayleigh fading channels .", "pid": 255, "answer": "C", "category": "cross-list"}, {"image": "images/image_256.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. function f ( x ) = x4 .\nB. graph of function f ( x ) = x .\nC. the function ϕε ( x ) for ε = 14 .\nD. the function f ( x ) given in ( 20 ) .\nE. function f ( x ) = x3 for x ∈ [ 1 , 3 ] .", "pid": 256, "answer": "E", "category": "other cs"}, {"image": "images/image_257.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pdcch al2 , rbs with 3db stronger interferences : [ 1:24 ] , tdla , s : 300ns .\nB. pdcch al , rbs with 3db stronger interferences : [ 1:12 ] , tdla , ds : 300ns .\nC. pdcch al1 , rbs with 3db stronger interferences : [ 1:1 ] , tdla , ds : 300ns .\nD. pdcch al8 , rbs with 3db stronger interferences : [ 1:12 ] , tdla , ds : 300ns .\nE. pdcch al1 , rbs with 3db stronger interferences : [ 1:24 ] , tdla , ds : 300ns .", "pid": 257, "answer": "A", "category": "cross-list"}, {"image": "images/image_258.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. ber comparison in 4×4 system between msm and generalized method which is proposed in [ 5 ] in terms of average number of molecules per bit .\nB. ber comparison for different m ’ s , with egc .\nC. ber of 2×2 system based on msm and 2×2 system based on proposed method in [ 5 ] in terms of average number of molecules .\nD. ber of 2×1 system based on msm and 1×1 system based on qcsk in terms of average number of molecules .\nE. ber comparison for differential stc schemes in a 4×1 system at 3 bits/s/hz .", "pid": 258, "answer": "A", "category": "other cs"}, {"image": "images/image_259.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. optimal solutions for g variation ( i− 1 = ip ) .\nB. optimal solutions for v0i variation ( i− 1 > ip ) .\nC. optimal solutions for v0i variation ( i− 1 = ip ) .\nD. optimal solutions for t0i variation ( i− 1 > ip ) .\nE. optimal solutions for β variation ( i− 1 > ip ) .", "pid": 259, "answer": "E", "category": "cross-list"}, {"image": "images/image_260.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time of multi-reference cosine ( 2000 to 4000 ) .\nB. execution time of multi-reference cosine 1000 3-grams .\nC. execution time of multi-reference cosine ( 6000 to 8000 ) .\nD. execution time of multi-reference cosine ( 4000 to 6000 ) .\nE. execution time of multi-reference cosine ( 0 to 2000 ) .", "pid": 260, "answer": "D", "category": "other cs"}, {"image": "images/image_261.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mean absolute error of multi-reference cosine with zero frequency 3-grams removed reference .\nB. mean absolute error of multi-reference cosine 1000 3-grams .\nC. mean absolute error of multi-reference cosine ( 6000 to 8000 ) .\nD. mean absolute error of multi-reference cosine 2000 3-grams .\nE. mean absolute error of multi-reference cosine 00 3-grams .", "pid": 261, "answer": "C", "category": "other cs"}, {"image": "images/image_262.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time of multi-reference cosine 1000 3-grams .\nB. execution time of multi-reference cosine ( 0 to 2000 ) .\nC. execution time of multi-reference cosine 2000 3-grams .\nD. execution time of multi-reference cosine ( 4000 to 6000 ) .\nE. execution time of multi-reference cosine 1500 3-grams .", "pid": 262, "answer": "A", "category": "other cs"}, {"image": "images/image_263.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time of multi-reference cosine ( 2000 to 4000 ) .\nB. execution time of multi-reference cosine 2000 3-grams .\nC. execution time of multi-reference cosine 1500 3-grams .\nD. execution time of multi-reference cosine 1000 3-grams .\nE. execution time of multi-reference cosine ( 6000 to 8000 ) .", "pid": 263, "answer": "B", "category": "other cs"}, {"image": "images/image_264.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. execution time of multi-reference cosine ( 0 to 2000 ) .\nB. execution time of multi-reference cosine ( 2000 to 4000 ) .\nC. execution time of multi-reference cosine ( 4000 to 6000 ) .\nD. execution time of multi-reference cosine ( 6000 to 8000 ) .\nE. execution time of multi-reference cosine 2000 3-grams .", "pid": 264, "answer": "A", "category": "other cs"}, {"image": "images/image_265.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. performance at different ψ of our methods .\nB. performance of our method on dev set for different γ .\nC. performance on hybrid datasets .\nD. performance of different models on several datasets .\nE. the performance of our method by varying λ .", "pid": 265, "answer": "A", "category": "cs.CL"}, {"image": "images/image_266.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. convergence of the cost with iteration step for 30-bus model ( blue graph ) and for the polish model ( red graph ) .\nB. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scales of 160 80 .\nC. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scale of 80 80 .\nD. comparison of the convergence behavior between algorithm ( solid blue line ) and 3 ( dotted red line ) .\nE. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scale of 80 40 .", "pid": 266, "answer": "C", "category": "other cs"}, {"image": "images/image_267.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. convergence of the cost with iteration step for 30-bus model ( blue graph ) and for the polish model ( red graph ) .\nB. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scale of 80 80 .\nC. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scale of 80 40 .\nD. comparison of the convergence behavior between algorithm ( solid blue line ) and 3 ( dotted red line ) .\nE. comprehensive convergence diagrams of objective functions with respect to full analysis ( blue ) and reanalysis ( red ) with scales of 160 80 .", "pid": 267, "answer": "E", "category": "other cs"}, {"image": "images/image_268.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. sum rate versus transmit powers k = 16 users and bref = bits .\nB. sum rate versus transmit powers k = 8 users and bref = 8 bits .\nC. sum rate versus transmit powers k = 1 users and bref = 4 bits .\nD. sum rate versus transmit powers k = 16 users and bref = 2 bits .\nE. sum rate versus transmit powers k = 8 users and bref = 4 bits .", "pid": 268, "answer": "E", "category": "cross-list"}, {"image": "images/image_269.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. model accuracy on listops test set by size of training dataset .\nB. comparison of model performance vs. training size on the se07 data set in pearson ’ s r .\nC. influence of the training set size for the bach dataset .\nD. performance comparison on the nyt dataset .\nE. performance vs. training size ( log scale in x-axis ) on snli dataset .", "pid": 269, "answer": "B", "category": "cs.CL"}, {"image": "images/image_270.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. time behaviour of the vehicles ’ collision point distance during the simulation run .\nB. velocity and distance between the vehicles with communication delay 0.333 s .\nC. time behaviour of the vehicles ’ acceleration during the simulation run .\nD. time behaviour of the vehicles ’ speed during the simulation run .\nE. time behaviour of the vehicles ’ speed ratio during the simulation run .", "pid": 270, "answer": "D", "category": "cs.SY"}, {"image": "images/image_271.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. sop for various α1 with r1 = 0.1 , r2 = 0.2 , k = 2 , αj = 0.5 , p = 10 db , mu = me = mr = 2 , ε1 = 1.5 , ε2 = 2 , and ωe = −10 db .\nB. sop with dpa scheme for various k with r1 = 0.1 , r2 = 0.2 , αj = 0.5 , p = 10 db , mu = me = mr = 2 , ε1 = 1.5 , ε2 = 2 , ̟ = 0.1 , µ = 5 , and ωe = −5 db .\nC. sop for various k with r1 = 0.1 , r2 = 0.2 , mu = me = mr = 2 , p = 15 db , α1 = 0.2 , αj = 0.5 , ε1 = 1.5 , ε2 = 2 , and ωe = −12 db .\nD. sop with dpa scheme for various µ with r1 = 0.1 , r2 = 0.2 , αj = 0.5 , p = 10 db , mu = me = mr = 2 , k = 3 , ε1 = 1.5 , ε2 = 2 , ̟ = 0.2 , and ωe = −5 db .\nE. sop for various m with r1 = 0.1 , r2 = 0.2 , mu = me = mr = m , αj = 0.3 , k = 3 , p = 10 db , ε1 = 1.5 , ε2 = 2 , and ωe = −10 db .", "pid": 271, "answer": "D", "category": "cross-list"}, {"image": "images/image_272.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. links per year , positive covariance wind speed anomalies network after thresholding .\nB. heaviest links per year , positive covariance wind speed anomalies network .\nC. heaviest links per year , negative covariance wind speed anomalies network .\nD. links per year , positive covariance carbon emissions network after thresholding .\nE. links per year , negative covariance wind speed anomalies network after thresholding .", "pid": 272, "answer": "E", "category": "other cs"}, {"image": "images/image_273.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. results for semi-black box fgs attack with standalone dae and dimensionality reduction defenses .\nB. results for black box fg attack with a cascaded dae and dimensionality reduction defense .\nC. results for semi-white box fgs attack with a cascaded dae and dimensionality reduction defense .\nD. results for semi-white box fg attack with a cascaded dae and dimensionality reduction defense .\nE. results for black box fgs attack with a cascaded dae and dimensionality reduction defense .", "pid": 273, "answer": "E", "category": "cross-list"}, {"image": "images/image_274.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. surowiec-south flow duration curves for 2025 scenarios .\nB. orrington-south flow duration curves for 2030 scenarios .\nC. orrington-south flow duration curves for 2025 scenarios .\nD. north-south flow duration curves for 2030 scenarios .\nE. north-south flow duration curves for 2025 scenarios .", "pid": 274, "answer": "E", "category": "cs.SY"}, {"image": "images/image_275.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. orrington-south flow duration curves for 2030 scenarios .\nB. surowiec-south flow duration curves for 2030 scenarios .\nC. north-south flow duration curves for 2030 scenarios .\nD. surowiec-south flow duration curves for 2025 scenarios .\nE. north-south flow duration curves for 2025 scenarios .", "pid": 275, "answer": "B", "category": "cs.SY"}, {"image": "images/image_276.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. surowiec-south flow duration curves for 2025 scenarios .\nB. orrington-south flow duration curves for 2030 scenarios .\nC. north-south flow duration curves for 2025 scenarios .\nD. north-south flow duration curves for 2030 scenarios .\nE. surowiec-south flow duration curves for 2030 scenarios .", "pid": 276, "answer": "D", "category": "cs.SY"}, {"image": "images/image_277.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the roc curves corresponding to ablation 1 .\nB. the roc curves corresponding to the ablation study .\nC. the roc curves corresponding to the ablation study .\nD. the roc curves corresponding to ablation 2 .\nE. the roc curves corresponding to ablation 3 .", "pid": 277, "answer": "D", "category": "cs.CV"}, {"image": "images/image_278.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification accuracy vs. snr using an lstm with different degrees of uniform subsampling .\nB. classification accuracy vs. snr using a resnet with different degrees of uniform subsampling .\nC. classification accuracy vs. snr using a cldnn with different degrees of uniform subsampling .\nD. classification accuracy vs. snr using a cldnn with different degrees of magnitude rank subsampling .\nE. classification accuracy vs. snr using a cldnn with different degrees of random subsampling .", "pid": 278, "answer": "C", "category": "cross-list"}, {"image": "images/image_279.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification accuracy vs. snr using a cldnn with different degrees of uniform subsampling .\nB. classification accuracy vs. snr using an lstm with different degrees of uniform subsampling .\nC. classification accuracy vs. snr using a resnet with different degrees of uniform subsampling .\nD. classification accuracy vs. snr using an lstm with different degrees of random subsampling .\nE. classification accuracy vs. snr using an lstm with different degrees of magnitude rank subsampling .", "pid": 279, "answer": "B", "category": "cross-list"}, {"image": "images/image_280.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification accuracy vs. snr using an lstm with different degrees of uniform subsampling .\nB. classification accuracy vs. snr using a resnet with different degrees of random subsampling .\nC. classification accuracy vs. snr using a cldnn with different degrees of random subsampling .\nD. classification accuracy vs. snr using an lstm with different degrees of magnitude rank subsampling .\nE. classification accuracy vs. snr using an lstm with different degrees of random subsampling .", "pid": 280, "answer": "E", "category": "cross-list"}, {"image": "images/image_281.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. classification accuracy vs. snr using a cldnn with different degrees of uniform subsampling .\nB. classification accuracy vs. snr using a cldnn with different degrees of magnitude rank subsampling .\nC. classification accuracy vs. snr using a cldnn with different degrees of random subsampling .\nD. classification accuracy vs. snr using a resnet with different degrees of magnitude rank subsampling .\nE. classification accuracy vs. snr using an lstm with different degrees of magnitude rank subsampling .", "pid": 281, "answer": "B", "category": "cross-list"}, {"image": "images/image_282.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. mean rt vs. the number of commands for each condition .\nB. error rate vs. the number of commands for each condition .\nC. total time vs. the number of commands for each condition .\nD. mean mt vs. the number of commands for each condition .\nE. average condition numbers for different time increments .", "pid": 282, "answer": "A", "category": "other cs"}, {"image": "images/image_283.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. number of explored nodes for randomly generated problems as a function of threshold δ .\nB. computation times for randomly generated problems without queue algorithm .\nC. computation times for randomly generated problems .\nD. number of explored nodes for randomly generated problems .\nE. computation times for randomly generated problems as a function of threshold δ .", "pid": 283, "answer": "E", "category": "cs.R<PERSON>"}, {"image": "images/image_284.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. number of explored nodes for randomly generated problems .\nB. computation times for randomly generated problems .\nC. number of explored nodes for randomly generated problems without queue algorithm .\nD. computation times for randomly generated problems as a function of threshold δ .\nE. number of explored nodes for randomly generated problems as a function of threshold δ .", "pid": 284, "answer": "E", "category": "cs.R<PERSON>"}, {"image": "images/image_285.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. nmpc charging : voltage over the single cells .\nB. cc-cv charging : voltage over the single cells .\nC. cc-cv charging : temperature of the single cells .\nD. voltage-based charging : voltage over the single cells .\nE. voltage-based charging : temperature of the single cells .", "pid": 285, "answer": "B", "category": "cs.SY"}, {"image": "images/image_286.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. nmpc charging : voltage over the single cells .\nB. voltage-based charging : temperature of the single cells .\nC. cc-cv charging : voltage over the single cells .\nD. voltage-based charging : state of charge ( soc ) .\nE. voltage-based charging : voltage over the single cells .", "pid": 286, "answer": "E", "category": "cs.SY"}, {"image": "images/image_287.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. voltage-based charging : voltage over the single cells .\nB. cc-cv charging : temperature of the single cells .\nC. nmpc charging : temperature of the single cells .\nD. cc-cv charging : voltage over the single cells .\nE. voltage-based charging : temperature of the single cells .", "pid": 287, "answer": "E", "category": "cs.SY"}, {"image": "images/image_288.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves for both the standard and adpo line-based fracture detection scheme .\nB. figure illustrating roc curve for the standard line-based fracture detection .\nC. figure illustrating roc curve for the adpo line-based fracture detection .\nD. figure illustrating roc curve for the improved chfb fracture detection .\nE. figure illustrating roc curve for the standard chfb fracture detection .", "pid": 288, "answer": "B", "category": "cs.CV"}, {"image": "images/image_289.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. figure illustrating roc curve for the improved chfb fracture detection .\nB. roc curves for both the standard and adpo line-based fracture detection scheme .\nC. figure illustrating roc curve for the standard chfb fracture detection .\nD. figure illustrating roc curve for the adpo line-based fracture detection .\nE. figure illustrating roc curve for the standard line-based fracture detection .", "pid": 289, "answer": "D", "category": "cs.CV"}, {"image": "images/image_290.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. estimation error of f0 ( t ) .\nB. estimation of β under different noise levels .\nC. detection performance for varying level of noise parameter σ .\nD. estimation of γ under different noise levels .\nE. estimation of α under different noise levels .", "pid": 290, "answer": "B", "category": "cs.SY"}, {"image": "images/image_291.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. roc curves of the target classifiers .\nB. roc plot of different classifiers using tfp-2 features .\nC. roc curve for vgo-based classifiers .\nD. roc plot for the 5 top-ranked classifiers without using the upvotes-related features .\nE. roc plot for the 4 top-ranked classifiers .", "pid": 291, "answer": "E", "category": "cs.SE"}, {"image": "images/image_292.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. kaplan-meier curve for survival analysis .\nB. the kaplan-meier survival model for three groups of experience factors over a period of one month .\nC. the kaplan-meier survival model for two groups of activity factors over a period of two months .\nD. the kaplan-meier survival model for one group of divisional and non-divisional variables over a period of one month .\nE. the kaplan-meier survival model for two groups of experience factors over a period of one month .", "pid": 292, "answer": "E", "category": "other cs"}, {"image": "images/image_293.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the kaplan-meier survival model for one group of divisional and non-divisional variables over a period of one month .\nB. kaplan-meier curve for survival analysis .\nC. the kaplan-meier survival model for two groups of activity factors over a period of one month .\nD. the kaplan-meier survival model for three groups of experience factors over a period of one month .\nE. the kaplan-meier survival model for Group 1, Group 2 and Group 3 of experience factors over a period of one month .", "pid": 293, "answer": "C", "category": "other cs"}, {"image": "images/image_294.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. held-out evaluation precision / recall curves for all nn-based models on nytfb-280k .\nB. precision-recall curves for the proposed model and various attention-based neural models .\nC. held-out evaluation precision / recall curves for pcnn+att , multir , dnmar and our proposed model pcnnnmar ( weighted ) on nytfb-68k .\nD. held-out evaluation precision / recall curves for pcnn+att model on original nytfb-280k and its shared-entity-pairs-removed version .\nE. the precision/recall curves of previous state-of-the-art methods and our proposed framework .", "pid": 294, "answer": "C", "category": "cs.CL"}, {"image": "images/image_295.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. pibr4 ( λ ) versus λ .\nB. g ( λ̃1 ( δ ) ) versus λ̃1 ( δ ) in appendix e .\nC. α as a function of λ .\nD. the loss function eθ as function of ∆θ .\nE. f ( λ̃1 ( δ ) ) versus λ̃1 ( δ ) in appendix i .", "pid": 295, "answer": "B", "category": "other cs"}, {"image": "images/image_296.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. p ( δ = j ) .\nB. µ versus q1 for p1 = 0.3 .\nC. p ( j=j ) for q = 0.7 , % ∗ = 0.7 .\nD. p ( j=j ) for q = 0.5 , % ∗ = 0 .\nE. ρ∗ ( α , p ) against p for different α .", "pid": 296, "answer": "C", "category": "other cs"}, {"image": "images/image_297.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. excess probability of distortion for δ = 0.01 .\nB. probability distribution of wt { e∗ ( x ) } .\nC. asymptotic of the common distribution α and δ .\nD. probability distribution function of b̃ .\nE. probability distribution function of δ and b̃ .", "pid": 297, "answer": "E", "category": "other cs"}, {"image": "images/image_298.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. frequency histogram with iteration planning as dependent variable and all the individual skills as factors .\nB. frequency histogram with productivity as dependent variable .\nC. productivity histogram of ds3 .\nD. frequency histogram for the second model with scale 2 as dependent variable .\nE. histogram of residuals .", "pid": 298, "answer": "B", "category": "cs.SE"}, {"image": "images/image_299.jpg", "question": "You'll be given an image, a question, and some choices. You have to select the correct one. The last line of your response should be of the following format: 'Answer: $LETTER' (without quotes) where LETTER is one of ABCDEFG. \nWhich caption best matches the image?\nA. the cumulative percentage of female first authored uk scopus-indexed journal articles 1996-2018 in each top cited percentile ( using nlcs values ) .\nB. the cumulative percentage of female first authored us scopus-indexed journal articles 1996-2018 in each top cited percentile ( using nlcs values ) .\nC. the cumulative percentage of female first authored canadian scopus-indexed journal articles 16-2018 in each top cited percentile ( using nlcs values ) .\nD. the cumulative percentage of female first authored new zealand scopus-indexed journal articles 1996-2018 in each top cited percentile ( using nlcs values ) .\nE. the cumulative percentage of female first authored irish scopus-indexed journal articles 1996-2018 in each top cited percentile ( using nlcs values ) .", "pid": 299, "answer": "D", "category": "other cs"}]