{"cells": [{"cell_type": "code", "execution_count": null, "id": "b2009479", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "\n", "with open('pathcls_test_ids.json', 'r') as f:\n", "    test_ids = json.load(f)\n", "\n", "with open('pathcls_val_ids.json', 'r') as f:\n", "    val_ids = json.load(f)\n", "\n", "with open('path/to/pathmmu/data.json', 'r') as f: # change to your path\n", "    data = json.load(f)\n", "\n", "pathcls = data['PathCLS']\n", "\n", "val, test = [], []\n", "\n", "for item in pathcls['test']:\n", "    if item['No'] in test_ids:\n", "        test.append(item)\n", "    elif item['No'] in val_ids:\n", "        val.append(item)\n", "\n", "combined = val + test"]}, {"cell_type": "code", "execution_count": null, "id": "f6570215", "metadata": {}, "outputs": [], "source": ["reformatted = []\n", "for idx, item in enumerate(combined):\n", "    question = item['question']\n", "    options = item['options']\n", "    options_str = '\\n'.join(options)\n", "    query = f'{question}\\n{options_str}\\nChoose the correct option.'\n", "    new_item = {\n", "        'pid': idx,\n", "        'answer': item['answer'],\n", "        'choices': item['options'],\n", "        'image': item['img'], # change to your path\n", "        'query': query\n", "    }\n", "    reformatted.append(new_item)"]}, {"cell_type": "code", "execution_count": null, "id": "16c45344", "metadata": {}, "outputs": [], "source": ["os.makedirs('data', exist_ok=True)\n", "\n", "with open(os.path.join('data', 'data.json'), 'w') as f:\n", "    json.dump(reformatted, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "993442e3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "octotools", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}