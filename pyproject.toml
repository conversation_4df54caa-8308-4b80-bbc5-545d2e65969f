[build-system]
requires = ["setuptools >= 77.0.3"]
build-backend = "setuptools.build_meta"

[project]
name = "octotoolkit"
version = "0.3.6"
authors = [
  { name="Pan Lu", email="<EMAIL>" },
]
description = "An effective and easy-to-use agentic framework with extendable tools for complex reasoning."
readme = "README.md"
requires-python = ">=3.10"
dynamic = ["dependencies"]

classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
license = "MIT"
license-files = ["LICEN[CS]E*"]

[tool.setuptools.packages.find]
where = ["."]
include = ["octotools"]

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[project.urls]
Homepage = "https://octotools.github.io"
Arxiv = "https://arxiv.org/pdf/2502.11271"
Demo = "https://huggingface.co/spaces/OctoTools/octotools"
Github = "https://github.com/octotools/octotools"
Issues = "https://github.com/octotools/octotools/issues"

[project.optional-dependencies]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1"
]

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"