"""
Simple test tool with no external dependencies to test the loading logic.
"""

class Simple_Test_Tool:
    def __init__(self):
        self.tool_name = "Simple Test Tool"
        self.tool_description = "A simple test tool with no dependencies"
        self.tool_version = "1.0.0"
        self.input_types = {"text": str}
        self.output_type = str
        self.demo_commands = ["test"]

    def run(self, text: str = "Hello World") -> str:
        return f"Simple Test Tool processed: {text}"
