{'tool_name': 'Text_Detector_Tool', 'tool_description': 'A tool that detects text in an image using EasyOCR.', 'tool_version': '1.0.0', 'input_types': {'image': 'str - The path to the image file.', 'languages': 'list - A list of language codes for the OCR model.', 'detail': 'int - The level of detail in the output. Set to 0 for simpler output, 1 for detailed output.'}, 'output_type': 'list - A list of detected text blocks.', 'demo_commands': [{'command': 'execution = tool.execute(image="path/to/image.png", languages=["en"])', 'description': 'Detect text in an image using the default language (English).'}, {'command': 'execution = tool.execute(image="path/to/image.png", languages=["en", "de"])', 'description': 'Detect text in an image using multiple languages (English and German).'}, {'command': 'execution = tool.execute(image="path/to/image.png", languages=["en"], detail=0)', 'description': 'Detect text in an image with simpler output (text without coordinates and scores).'}], 'require_llm_engine': False, 'user_metadata': {'frequently_used_language': {'ch_sim': 'Simplified Chinese', 'ch_tra': 'Traditional Chinese', 'de': 'German', 'en': 'English', 'es': 'Spanish', 'fr': 'French', 'hi': 'Hindi', 'ja': 'Japanese'}}}
[[[[231, 32], [674, 32], [674, 64], [231, 64]], "Reduce your risk of coronavirus infection:", 0.84], [[[326, 98], [598, 98], [598, 124], [326, 124]], "Clean hands with soap and water", 0.96], [[[328, 124], [542, 124], [542, 148], [328, 148]], "or alcohol-based hand rub", 0.89], [[[246, 169], [595, 169], [595, 196], [246, 196]], "Cover nose and mouth when coughing and", 1.0], [[[245, 194], [546, 194], [546, 222], [245, 222]], "sneezing with tissue or flexed elbow", 0.96], [[[320, 240], [624, 240], [624, 266], [320, 266]], "Avoid close contact with anyone with", 0.86], [[[318, 266], [528, 266], [528, 292], [318, 292]], "cold or flu-like symptoms", 0.77], [[[248, 322], [510, 322], [510, 348], [248, 348]], "Thoroughly cook meat and eggs", 0.72], [[[332, 370], [640, 370], [640, 396], [332, 396]], "No unprotected contact with live wild", 0.83], [[[334, 396], [464, 396], [464, 420], [334, 420]], "or farm animals", 0.72], [[[595, 427], [683, 427], [683, 447], [595, 447]], "World Health", 1.0], [[[595, 445], [685, 445], [685, 463], [595, 463]], "Organization", 1.0]]
Detected Text: [([[231, 32], [674, 32], [674, 64], [231, 64]], 'Reduce your risk of coronavirus infection:', 0.84), ([[326, 98], [598, 98], [598, 124], [326, 124]], 'Clean hands with soap and water', 0.96), ([[328, 124], [542, 124], [542, 148], [328, 148]], 'or alcohol-based hand rub', 0.89), ([[246, 169], [595, 169], [595, 196], [246, 196]], 'Cover nose and mouth when coughing and', 1.0), ([[245, 194], [546, 194], [546, 222], [245, 222]], 'sneezing with tissue or flexed elbow', 0.96), ([[320, 240], [624, 240], [624, 266], [320, 266]], 'Avoid close contact with anyone with', 0.86), ([[318, 266], [528, 266], [528, 292], [318, 292]], 'cold or flu-like symptoms', 0.77), ([[248, 322], [510, 322], [510, 348], [248, 348]], 'Thoroughly cook meat and eggs', 0.72), ([[332, 370], [640, 370], [640, 396], [332, 396]], 'No unprotected contact with live wild', 0.83), ([[334, 396], [464, 396], [464, 420], [334, 420]], 'or farm animals', 0.72), ([[595, 427], [683, 427], [683, 447], [595, 447]], 'World Health', 1.0), ([[595, 445], [685, 445], [685, 463], [595, 463]], 'Organization', 1.0)]
Done!
