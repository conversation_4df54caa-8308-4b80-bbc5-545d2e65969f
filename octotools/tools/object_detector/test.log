Could not load the custom kernel for multi-scale deformable attention: Command '['which', 'c++']' returned non-zero exit status 1.
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
Could not load the custom kernel for multi-scale deformable attention: /root/.cache/torch_extensions/py310_cu121/MultiScaleDeformableAttention/MultiScaleDeformableAttention.so: cannot open shared object file: No such file or directory
CUDA_HOME is not set
{'tool_name': 'Object_Detector_Tool', 'tool_description': 'A tool that detects objects in an image using the Grounding DINO model and saves individual object images with empty padding.', 'tool_version': '1.0.0', 'input_types': {'image': 'str - The path to the image file.', 'labels': 'list - A list of object labels to detect.', 'threshold': 'float - The confidence threshold for detection (default: 0.35).', 'model_size': "str - The size of the model to use ('tiny' or 'base', default: 'tiny').", 'padding': 'int - The number of pixels to add as empty padding around detected objects (default: 20).'}, 'output_type': 'list - A list of detected objects with their scores, bounding boxes, and saved image paths.', 'demo_commands': [{'command': 'execution = tool.execute(image="path/to/image.png", labels=["baseball", "basket"])', 'description': 'Detect baseball and basket in an image, save the detected objects with default empty padding, and return their paths.'}, {'command': 'execution = tool.execute(image="path/to/image.png", labels=["car", "person"], threshold=0.5, model_size="base", padding=15)', 'description': 'Detect car and person in an image using the base model, save the detected objects with 15 pixels of empty padding, and return their paths.'}], 'require_llm_engine': False, 'user_metadata': {'limitation': 'The model may not always detect objects accurately, and its performance can vary depending on the input image and the associated labels. It typically struggles with detecting small objects, objects that are uncommon, or objects with limited or specific attributes. For improved accuracy or better detection in certain situations, consider using supplementary tools or image processing techniques to provide additional information for verification.'}}
Detected Objects:
Detected baseball with confidence 0.69
Bounding box: (558, 48, 615, 107)
Saved image (with padding): detected_objects/baseball_baseball_1.png

Detected baseball with confidence 0.69
Bounding box: (614, 137, 671, 191)
Saved image (with padding): detected_objects/baseball_baseball_2.png

Detected baseball with confidence 0.68
Bounding box: (132, 67, 189, 126)
Saved image (with padding): detected_objects/baseball_baseball_3.png

Detected baseball with confidence 0.68
Bounding box: (632, 67, 690, 126)
Saved image (with padding): detected_objects/baseball_baseball_4.png

Detected baseball with confidence 0.68
Bounding box: (57, 289, 115, 346)
Saved image (with padding): detected_objects/baseball_baseball_5.png

Detected baseball with confidence 0.68
Bounding box: (535, 111, 592, 170)
Saved image (with padding): detected_objects/baseball_baseball_6.png

Detected baseball with confidence 0.68
Bounding box: (307, 48, 365, 107)
Saved image (with padding): detected_objects/baseball_baseball_7.png

Detected baseball with confidence 0.68
Bounding box: (114, 137, 171, 191)
Saved image (with padding): detected_objects/baseball_baseball_8.png

Detected baseball with confidence 0.68
Bounding box: (35, 351, 91, 410)
Saved image (with padding): detected_objects/baseball_baseball_9.png

Detected baseball with confidence 0.68
Bounding box: (57, 48, 115, 107)
Saved image (with padding): detected_objects/baseball_baseball_10.png

Detected baseball with confidence 0.68
Bounding box: (35, 111, 91, 170)
Saved image (with padding): detected_objects/baseball_baseball_11.png

Detected baseball with confidence 0.68
Bounding box: (364, 137, 421, 191)
Saved image (with padding): detected_objects/baseball_baseball_12.png

Detected baseball with confidence 0.68
Bounding box: (114, 377, 171, 430)
Saved image (with padding): detected_objects/baseball_baseball_13.png

Detected baseball with confidence 0.67
Bounding box: (132, 307, 189, 366)
Saved image (with padding): detected_objects/baseball_baseball_14.png

Detected baseball with confidence 0.67
Bounding box: (285, 111, 342, 170)
Saved image (with padding): detected_objects/baseball_baseball_15.png

Detected baseball with confidence 0.67
Bounding box: (382, 67, 439, 126)
Saved image (with padding): detected_objects/baseball_baseball_16.png

Detected baseball with confidence 0.65
Bounding box: (587, 94, 643, 153)
Saved image (with padding): detected_objects/baseball_baseball_17.png

Detected baseball with confidence 0.65
Bounding box: (86, 94, 143, 153)
Saved image (with padding): detected_objects/baseball_baseball_18.png

Detected baseball with confidence 0.65
Bounding box: (86, 335, 143, 393)
Saved image (with padding): detected_objects/baseball_baseball_19.png

Detected baseball with confidence 0.63
Bounding box: (336, 95, 393, 153)
Saved image (with padding): detected_objects/baseball_baseball_20.png

Detected basket with confidence 0.59
Bounding box: (252, 2, 468, 215)
Saved image (with padding): detected_objects/baseball_basket_1.png

Detected basket with confidence 0.55
Bounding box: (503, 2, 717, 215)
Saved image (with padding): detected_objects/baseball_basket_2.png

Detected basket with confidence 0.54
Bounding box: (2, 2, 217, 215)
Saved image (with padding): detected_objects/baseball_basket_3.png

Detected basket with confidence 0.5
Bounding box: (2, 242, 217, 455)
Saved image (with padding): detected_objects/baseball_basket_4.png

Done!
