{'code': 0, 'msg': 'ok', 'data': {'task_uuid': '2d4337f5-403d-419b-9832-64b0f888f146'}}
task_uuid:2d4337f5-403d-419b-9832-64b0f888f146
[
    {
        "label": "baseball",
        "confidence score": 0.73,
        "box": [
            614,
            137,
            671,
            191
        ],
        "saved_image_path": "detected_objects/baseball_baseball_1.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.73,
        "box": [
            114,
            377,
            171,
            431
        ],
        "saved_image_path": "detected_objects/baseball_baseball_2.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.72,
        "box": [
            632,
            67,
            689,
            126
        ],
        "saved_image_path": "detected_objects/baseball_baseball_3.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.72,
        "box": [
            132,
            67,
            189,
            126
        ],
        "saved_image_path": "detected_objects/baseball_baseball_4.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.71,
        "box": [
            382,
            67,
            439,
            126
        ],
        "saved_image_path": "detected_objects/baseball_baseball_5.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.71,
        "box": [
            364,
            137,
            421,
            191
        ],
        "saved_image_path": "detected_objects/baseball_baseball_6.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.71,
        "box": [
            132,
            307,
            189,
            366
        ],
        "saved_image_path": "detected_objects/baseball_baseball_7.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.71,
        "box": [
            114,
            136,
            171,
            191
        ],
        "saved_image_path": "detected_objects/baseball_baseball_8.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.7,
        "box": [
            57,
            49,
            115,
            107
        ],
        "saved_image_path": "detected_objects/baseball_baseball_9.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.69,
        "box": [
            307,
            49,
            365,
            106
        ],
        "saved_image_path": "detected_objects/baseball_baseball_10.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.68,
        "box": [
            57,
            289,
            115,
            346
        ],
        "saved_image_path": "detected_objects/baseball_baseball_11.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.68,
        "box": [
            86,
            335,
            143,
            393
        ],
        "saved_image_path": "detected_objects/baseball_baseball_12.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.68,
        "box": [
            557,
            49,
            615,
            107
        ],
        "saved_image_path": "detected_objects/baseball_baseball_13.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.68,
        "box": [
            35,
            352,
            92,
            410
        ],
        "saved_image_path": "detected_objects/baseball_baseball_14.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.68,
        "box": [
            86,
            95,
            143,
            153
        ],
        "saved_image_path": "detected_objects/baseball_baseball_15.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.67,
        "box": [
            586,
            95,
            643,
            153
        ],
        "saved_image_path": "detected_objects/baseball_baseball_16.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.66,
        "box": [
            285,
            111,
            342,
            169
        ],
        "saved_image_path": "detected_objects/baseball_baseball_17.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.66,
        "box": [
            35,
            111,
            91,
            170
        ],
        "saved_image_path": "detected_objects/baseball_baseball_18.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.66,
        "box": [
            535,
            111,
            592,
            169
        ],
        "saved_image_path": "detected_objects/baseball_baseball_19.png"
    },
    {
        "label": "baseball",
        "confidence score": 0.66,
        "box": [
            337,
            95,
            393,
            153
        ],
        "saved_image_path": "detected_objects/baseball_baseball_20.png"
    },
    {
        "label": "basket",
        "confidence score": 0.41,
        "box": [
            1,
            2,
            218,
            216
        ],
        "saved_image_path": "detected_objects/baseball_basket_1.png"
    },
    {
        "label": "basket",
        "confidence score": 0.39,
        "box": [
            501,
            2,
            718,
            216
        ],
        "saved_image_path": "detected_objects/baseball_basket_2.png"
    },
    {
        "label": "basket",
        "confidence score": 0.38,
        "box": [
            2,
            242,
            218,
            456
        ],
        "saved_image_path": "detected_objects/baseball_basket_3.png"
    },
    {
        "label": "basket",
        "confidence score": 0.38,
        "box": [
            251,
            2,
            468,
            216
        ],
        "saved_image_path": "detected_objects/baseball_basket_4.png"
    }
]
Detected Objects:
Detected baseball with confidence 0.73
Bounding box: [614, 137, 671, 191]
Saved image (with padding): detected_objects/baseball_baseball_1.png

Detected baseball with confidence 0.73
Bounding box: [114, 377, 171, 431]
Saved image (with padding): detected_objects/baseball_baseball_2.png

Detected baseball with confidence 0.72
Bounding box: [632, 67, 689, 126]
Saved image (with padding): detected_objects/baseball_baseball_3.png

Detected baseball with confidence 0.72
Bounding box: [132, 67, 189, 126]
Saved image (with padding): detected_objects/baseball_baseball_4.png

Detected baseball with confidence 0.71
Bounding box: [382, 67, 439, 126]
Saved image (with padding): detected_objects/baseball_baseball_5.png

Detected baseball with confidence 0.71
Bounding box: [364, 137, 421, 191]
Saved image (with padding): detected_objects/baseball_baseball_6.png

Detected baseball with confidence 0.71
Bounding box: [132, 307, 189, 366]
Saved image (with padding): detected_objects/baseball_baseball_7.png

Detected baseball with confidence 0.71
Bounding box: [114, 136, 171, 191]
Saved image (with padding): detected_objects/baseball_baseball_8.png

Detected baseball with confidence 0.7
Bounding box: [57, 49, 115, 107]
Saved image (with padding): detected_objects/baseball_baseball_9.png

Detected baseball with confidence 0.69
Bounding box: [307, 49, 365, 106]
Saved image (with padding): detected_objects/baseball_baseball_10.png

Detected baseball with confidence 0.68
Bounding box: [57, 289, 115, 346]
Saved image (with padding): detected_objects/baseball_baseball_11.png

Detected baseball with confidence 0.68
Bounding box: [86, 335, 143, 393]
Saved image (with padding): detected_objects/baseball_baseball_12.png

Detected baseball with confidence 0.68
Bounding box: [557, 49, 615, 107]
Saved image (with padding): detected_objects/baseball_baseball_13.png

Detected baseball with confidence 0.68
Bounding box: [35, 352, 92, 410]
Saved image (with padding): detected_objects/baseball_baseball_14.png

Detected baseball with confidence 0.68
Bounding box: [86, 95, 143, 153]
Saved image (with padding): detected_objects/baseball_baseball_15.png

Detected baseball with confidence 0.67
Bounding box: [586, 95, 643, 153]
Saved image (with padding): detected_objects/baseball_baseball_16.png

Detected baseball with confidence 0.66
Bounding box: [285, 111, 342, 169]
Saved image (with padding): detected_objects/baseball_baseball_17.png

Detected baseball with confidence 0.66
Bounding box: [35, 111, 91, 170]
Saved image (with padding): detected_objects/baseball_baseball_18.png

Detected baseball with confidence 0.66
Bounding box: [535, 111, 592, 169]
Saved image (with padding): detected_objects/baseball_baseball_19.png

Detected baseball with confidence 0.66
Bounding box: [337, 95, 393, 153]
Saved image (with padding): detected_objects/baseball_baseball_20.png

Detected basket with confidence 0.41
Bounding box: [1, 2, 218, 216]
Saved image (with padding): detected_objects/baseball_basket_1.png

Detected basket with confidence 0.39
Bounding box: [501, 2, 718, 216]
Saved image (with padding): detected_objects/baseball_basket_2.png

Detected basket with confidence 0.38
Bounding box: [2, 242, 218, 456]
Saved image (with padding): detected_objects/baseball_basket_3.png

Detected basket with confidence 0.38
Bounding box: [251, 2, 468, 216]
Saved image (with padding): detected_objects/baseball_basket_4.png

Done!
