{'tool_name': 'URL_Text_Extractor_Tool', 'tool_description': 'A tool that extracts all text from a given URL.', 'tool_version': '1.0.0', 'input_types': {'url': 'str - The URL from which to extract text.'}, 'output_type': 'dict - A dictionary containing the extracted text and any error messages.', 'demo_commands': [{'command': 'execution = tool.execute(url="https://example.com")', 'description': 'Extract all text from the example.com website.'}, {'command': 'execution = tool.execute(url="https://en.wikipedia.org/wiki/Python_(programming_language)")', 'description': 'Extract all text from the Wikipedia page about Python programming language.'}], 'require_llm_engine': False}
Execution Result:
{
    "url": "https://en.wikipedia.org/wiki/Python_(programming_language)",
    "extracted_text": "Python (programming language) - Wikipedia\nJump to content\nMain menu\nMain menu\nmove to sidebar\nhide\nNavigation\nMain page\nContents\nCurrent events\nRandom article\nAbout Wikipedia\nContact us\nSpecial pages\nContribute\nHelp\nLearn to edit\nCommunity portal\nRecent changes\nUpload file\nSearch\nSearch\nAppearance\nDonate\nCreate account\nLog in\nPersonal tools\nDonate\nCreate account\nLog in\nPages for logged out editors\nlearn more\nContributions\nTalk\nContents\nmove to sidebar\nhide\n(Top)\n1\nHistory\n2\nDesign philosophy and features\n3\nSyntax and semantics\nToggle Syntax and semantics subsection\n3.1\nIndentation\n3.2\nStatements and control flow\n3.3\nExpressions\n3.4\nMethods\n3.5\nTyping\n3.6\nArithmetic operations\n3.7\nFunction syntax\n4\nProgramming examples\n5\nLibraries\n6\nDevelopment environments\n7\nImplementations\nToggle Implementations subsection\n7.1\nReference implementation\n7.2\nOther implementations\n7.3\nNo longer supported implementations\n7.4\nCross-compilers to other languages\n7.5\nPerformance\n8\nDevelopment\n9\nAPI documentation generators\n10\nNaming\n11\nPopularity\n12\nUses\n13\nLanguages influenced by Python\n14\nSee also\n15\nReferences\nToggle References subsection\n15.1\nSources\n16\nFurther reading\n17\nExternal links\nToggle the table of contents\nPython (programming language)\n112 languages\nAfrikaans\nAlemannisch\n\u0627\u0644\u0639\u0631\u0628\u064a\u0629\nAragon\u00e9s\n\u0985\u09b8\u09ae\u09c0\u09af\u09bc\u09be\nAsturianu\nAz\u0259rbaycanca\n\u062a\u06c6\u0631\u06a9\u062c\u0647\nBasa Bali\n\u09ac\u09be\u0982\u09b2\u09be\n\u95a9\u5357\u8a9e / B\u00e2n-l\u00e2m-g\u00fa\n\u0411\u0435\u043b\u0430\u0440\u0443\u0441\u043a\u0430\u044f\n\u0411\u0435\u043b\u0430\u0440\u0443\u0441\u043a\u0430\u044f (\u0442\u0430\u0440\u0430\u0448\u043a\u0435\u0432\u0456\u0446\u0430)\n\u092d\u094b\u091c\u092a\u0941\u0930\u0940\n\u0411\u044a\u043b\u0433\u0430\u0440\u0441\u043a\u0438\nBosanski\nBrezhoneg\nCatal\u00e0\nCebuano\n\u010ce\u0161tina\nCymraeg\nDansk\nDeutsch\nEesti\n\u0395\u03bb\u03bb\u03b7\u03bd\u03b9\u03ba\u03ac\nEspa\u00f1ol\nEsperanto\nEuskara\n\u0641\u0627\u0631\u0633\u06cc\nFran\u00e7ais\nGalego\n\u0a97\u0ac1\u0a9c\u0ab0\u0abe\u0aa4\u0ac0\n\ud55c\uad6d\uc5b4\nHausa\n\u0540\u0561\u0575\u0565\u0580\u0565\u0576\n\u0939\u093f\u0928\u094d\u0926\u0940\nHrvatski\nIdo\nBahasa Indonesia\nInterlingua\n\u00cdslenska\nItaliano\n\u05e2\u05d1\u05e8\u05d9\u05ea\n\u10e5\u10d0\u10e0\u10d7\u10e3\u10da\u10d8\n\u049a\u0430\u0437\u0430\u049b\u0448\u0430\nKiswahili\nKurd\u00ee\n\u041a\u044b\u0440\u0433\u044b\u0437\u0447\u0430\n\u0ea5\u0eb2\u0ea7\nLatina\nLatvie\u0161u\nLietuvi\u0173\nLa .lojban.\nLombard\nMagyar\n\u041c\u0430\u043a\u0435\u0434\u043e\u043d\u0441\u043a\u0438\n\u0d2e\u0d32\u0d2f\u0d3e\u0d33\u0d02\n\u092e\u0930\u093e\u0920\u0940\nBahasa Melayu\n\u041c\u043e\u043d\u0433\u043e\u043b\n\u1019\u103c\u1014\u103a\u1019\u102c\u1018\u102c\u101e\u102c\nNa Vosa Vakaviti\nNederlands\n\u0928\u0947\u092a\u093e\u0932\u0940\n\u65e5\u672c\u8a9e\n\u07d2\u07de\u07cf\nNorsk bokm\u00e5l\nNorsk nynorsk\n\u0b13\u0b21\u0b3c\u0b3f\u0b06\nO\u02bbzbekcha / \u045e\u0437\u0431\u0435\u043a\u0447\u0430\n\u0a2a\u0a70\u0a1c\u0a3e\u0a2c\u0a40\n\u067e\u0646\u062c\u0627\u0628\u06cc\n\u1797\u17b6\u179f\u17b6\u1781\u17d2\u1798\u17c2\u179a\nPlattd\u00fc\u00fctsch\nPolski\nPortugu\u00eas\nQaraqalpaqsha\nRom\u00e2n\u0103\nRuna Simi\n\u0420\u0443\u0441\u0441\u043a\u0438\u0439\n\u0421\u0430\u0445\u0430 \u0442\u044b\u043b\u0430\n\u1c65\u1c5f\u1c71\u1c5b\u1c5f\u1c72\u1c64\nScots\nShqip\n\u0dc3\u0dd2\u0d82\u0dc4\u0dbd\nSimple English\nSloven\u010dina\nSloven\u0161\u010dina\n\u06a9\u0648\u0631\u062f\u06cc\n\u0421\u0440\u043f\u0441\u043a\u0438 / srpski\nSrpskohrvatski / \u0441\u0440\u043f\u0441\u043a\u043e\u0445\u0440\u0432\u0430\u0442\u0441\u043a\u0438\nSuomi\nSvenska\nTagalog\n\u0ba4\u0bae\u0bbf\u0bb4\u0bcd\n\u0422\u0430\u0442\u0430\u0440\u0447\u0430 / tatar\u00e7a\n\u107d\u1083\u1087\u101e\u1083\u1087\u1010\u1086\u1038\n\u0c24\u0c46\u0c32\u0c41\u0c17\u0c41\n\u0e44\u0e17\u0e22\n\u0422\u043e\u04b7\u0438\u043a\u04e3\nT\u00fcrk\u00e7e\nBasa Ugi\n\u0423\u043a\u0440\u0430\u0457\u043d\u0441\u044c\u043a\u0430\n\u0627\u0631\u062f\u0648\n\u0626\u06c7\u064a\u063a\u06c7\u0631\u0686\u06d5 / Uyghurche\nTi\u1ebfng Vi\u1ec7t\nWalon\n\u6587\u8a00\nWinaray\n\u5434\u8bed\n\u7cb5\u8a9e\n\u4e2d\u6587\nEdit links\nArticle\nTalk\nEnglish\nRead\nEdit\nView history\nTools\nTools\nmove to sidebar\nhide\nActions\nRead\nEdit\nView history\nGeneral\nWhat links here\nRelated changes\nUpload file\nPermanent link\nPage information\nCite this page\nGet shortened URL\nDownload QR code\nPrint/export\nDownload as PDF\nPrintable version\nIn other projects\nWikimedia Commons\nMediaWiki\nWikibooks\nWikifunctions\nWikiquote\nWikiversity\nWikidata item\nAppearance\nmove to sidebar\nhide\nFrom Wikipedia, the free encyclopedia\nGeneral-purpose programming language\nPython\nParadigm\nMulti-paradigm\n:\nobject-oriented\n,\n[\n1\n]\nprocedural\n(\nimperative\n),\nfunctional\n,\nstructured\n,\nreflective\nDesigned\u00a0by\nGuido van Rossum\nDeveloper\nPython Software Foundation\nFirst\u00a0appeared\n20\u00a0February 1991\n; 33 years ago\n(\n1991-02-20\n)\n[\n2\n]\nStable release\n3.13.2\n   / 4 February 2025\n; 10 days ago\n(\n4 February 2025\n)\nTyping discipline\nduck\n,\ndynamic\n,\nstrong\n;\n[\n3\n]\noptional type annotations\n(since 3.5, but those hints are ignored, except with unofficial tools)\n[\n4\n]\nOS\nTier 1\n: 64-bit\nLinux\n,\nmacOS\n; 64- and 32-bit\nWindows\n10+\n[\n5\n]\nTier 2\n: E.g. 32-bit\nWebAssembly\n(WASI)\nTier 3\n: 64-bit\nAndroid\n,\n[\n6\n]\niOS\n,\nFreeBSD\n, and (32-bit)\nRaspberry Pi OS\nUnofficial (or has been known to work): Other\nUnix-like\n/\nBSD\nvariants) and a few other platforms\n[\n7\n]\n[\n8\n]\n[\n9\n]\nLicense\nPython Software Foundation License\nFilename extensions\n.py, .pyw, .pyz,\n[\n10\n]\n.pyi, .pyc, .pyd\nWebsite\npython.org\nMajor\nimplementations\nCPython\n,\nPyPy\n,\nStackless Python\n,\nMicroPython\n,\nCircuitPython\n,\nIronPython\n,\nJython\nDialects\nCython\n,\nRPython\n,\nStarlark\n[\n11\n]\nInfluenced by\nABC\n,\n[\n12\n]\nAda\n,\n[\n13\n]\nALGOL 68\n,\n[\n14\n]\nAPL\n,\n[\n15\n]\nC\n,\n[\n16\n]\nC++\n,\n[\n17\n]\nCLU\n,\n[\n18\n]\nDylan\n,\n[\n19\n]\nHaskell\n,\n[\n20\n]\n[\n15\n]\nIcon\n,\n[\n21\n]\nLisp\n,\n[\n22\n]\nModula-3\n,\n[\n14\n]\n[\n17\n]\nPerl\n,\n[\n23\n]\nStandard ML\n[\n15\n]\nInfluenced\nApache Groovy\n,\nBoo\n,\nCobra\n,\nCoffeeScript\n,\n[\n24\n]\nD\n,\nF#\n,\nGDScript\n,\nGo\n,\nJavaScript\n,\n[\n25\n]\n[\n26\n]\nJulia\n,\n[\n27\n]\nMojo\n,\n[\n28\n]\nNim\n,\nRing\n,\n[\n29\n]\nRuby\n,\n[\n30\n]\nSwift\n,\n[\n31\n]\nV\n[\n32\n]\nPython Programming\nat Wikibooks\nPython\nis a\nhigh-level\n,\ngeneral-purpose programming language\n. Its design philosophy emphasizes\ncode readability\nwith the use of\nsignificant indentation\n.\n[\n33\n]\nPython is\ndynamically type-checked\nand\ngarbage-collected\n. It supports multiple\nprogramming paradigms\n, including\nstructured\n(particularly\nprocedural\n),\nobject-oriented\nand\nfunctional programming\n. It is often described as a \"batteries included\" language due to its comprehensive\nstandard library\n.\n[\n34\n]\n[\n35\n]\nGuido van Rossum\nbegan working on Python in the late 1980s as a successor to the\nABC\nprogramming language and first released it in 1991 as Python\u00a00.9.0.\n[\n36\n]\nPython\u00a02.0 was released in 2000. Python\u00a03.0, released in 2008, was a major revision not completely\nbackward-compatible\nwith earlier versions. Python\u00a02.7.18, released in 2020, was the last release of Python\u00a02.\n[\n37\n]\nPython consistently ranks as one of the most popular programming languages, and has gained widespread use in the\nmachine learning\ncommunity.\n[\n38\n]\n[\n39\n]\n[\n40\n]\n[\n41\n]\nHistory\n[\nedit\n]\nMain article:\nHistory of Python\nThe designer of Python,\nGuido van Rossum\n, at PyCon US 2024\nPython was conceived in the late 1980s\n[\n42\n]\nby\nGuido van Rossum\nat\nCentrum Wiskunde & Informatica\n(CWI) in the\nNetherlands\nas a successor to the\nABC\nprogramming language, which was inspired by\nSETL\n,\n[\n43\n]\ncapable of\nexception handling\nand interfacing with the\nAmoeba\noperating system.\n[\n12\n]\nIts implementation began in December\u00a01989.\n[\n44\n]\nVan Rossum shouldered sole responsibility for the project, as the lead developer, until 12 July 2018, when he announced his \"permanent vacation\" from his responsibilities as Python's \"\nbenevolent dictator for life\n\" (BDFL), a title the Python community bestowed upon him to reflect his long-term commitment as the project's chief decision-maker\n[\n45\n]\n(he has since come out of retirement and is self-titled \"BDFL-emeritus\"). In January\u00a02019, active Python core developers elected a five-member Steering Council to lead the project.\n[\n46\n]\n[\n47\n]\nThe name Python is said to come from the British comedy series\nMonty Python's Flying Circus\n.\n[\n48\n]\nPython 2.0 was released on 16 October 2000, with many major new features such as\nlist comprehensions\n,\ncycle-detecting\ngarbage collection,\nreference counting\n, and\nUnicode\nsupport.\n[\n49\n]\nPython 2.7's\nend-of-life\nwas initially set for 2015, then postponed to 2020 out of concern that a large body of existing code could not easily be forward-ported to Python\u00a03.\n[\n50\n]\n[\n51\n]\nNo further security patches or other improvements will be released for it.\n[\n52\n]\n[\n53\n]\nWhile Python 2.7 and older versions are officially unsupported, a different unofficial Python implementation,\nPyPy\n, continues to support Python 2, i.e. \"2.7.18+\" (plus 3.10), with the plus meaning (at least some) \"\nbackported\nsecurity updates\".\n[\n54\n]\nPython\u00a03.0 was released on 3 December 2008, with some new semantics and changed syntax. At least every Python release since (now unsupported) 3.5 has added some syntax to the language, and a few later releases have dropped outdated modules, or changed semantics, at least in a minor way.\nSince 7\u00a0October\u00a02024\n[update]\n, Python 3.13 is the latest stable release, and it and, for few more months, 3.12 are the only releases with active support including for bug fixes (as opposed to just for security) and Python 3.9,\n[\n55\n]\nis the oldest supported version of Python (albeit in the 'security support' phase), due to Python 3.8 reaching\nend-of-life\n.\n[\n56\n]\n[\n57\n]\nStarting with 3.13, it and later versions have 2 years of full support (up from one and a half), followed by 3 years of security support (for same total support as before).\nSecurity updates were expedited in 2021 (and again twice in 2022, and more fixed in 2023 and in September 2024 for Python 3.12.6 down to 3.8.20), since all Python versions were insecure (including 2.7\n[\n58\n]\n) because of security issues leading to possible\nremote code execution\n[\n59\n]\nand\nweb-cache poisoning\n.\n[\n60\n]\nPython 3.10 added the\n|\nunion type operator\n[\n61\n]\nand the\nmatch\nand\ncase\nkeywords (for structural\npattern matching\nstatements). 3.11 expanded\nexception handling\nfunctionality. Python 3.12 added the new keyword\ntype\n. Notable changes in 3.11 from 3.10 include increased program execution speed and improved error reporting.\n[\n62\n]\nPython 3.11 claims to be between 10 and 60% faster than Python 3.10, and Python 3.12 adds another 5% on top of that. It also has improved error messages (again improved in 3.14), and many other changes.\nPython 3.13 introduces more syntax for types, a new and improved interactive interpreter (\nREPL\n), featuring multi-line editing and color support; an incremental garbage collector (producing shorter pauses for collection in programs with a lot of objects, and addition to the improved speed in 3.11 and 3.12),  and an\nexperimental\njust-in-time (JIT) compiler\n(such features, can/needs to be enabled specifically for the increase in speed),\n[\n63\n]\nand an\nexperimental\nfree-threaded build mode, which disables the\nglobal interpreter lock\n(GIL), allowing threads to run more concurrently, that latter feature enabled with\npython3.13t\nor\npython3.13t.exe\n.\nPython 3.13 introduces some change in behavior, i.e. new \"well-defined semantics\", fixing bugs (plus many removals of deprecated classes, functions and methods, and removed some of the C\u00a0API and outdated modules): \"The  [old] implementation of\nlocals()\nand\nframe.f_locals\nis slow, inconsistent and buggy [and it] has many corner cases and oddities. Code that works around those may need to be changed. Code that uses\nlocals()\nfor simple templating, or print debugging, will continue to work corr"
}
url:
https://en.wikipedia.org/wiki/Python_(programming_language)

extracted_text:
Python (programming language) - Wikipedia
Jump to content
Main menu
Main menu
move to sidebar
hide
Navigation
Main page
Contents
Current events
Random article
About Wikipedia
Contact us
Special pages
Contribute
Help
Learn to edit
Community portal
Recent changes
Upload file
Search
Search
Appearance
Donate
Create account
Log in
Personal tools
Donate
Create account
Log in
Pages for logged out editors
learn more
Contributions
Talk
Contents
move to sidebar
hide
(Top)
1
History
2
Design philosophy and features
3
Syntax and semantics
Toggle Syntax and semantics subsection
3.1
Indentation
3.2
Statements and control flow
3.3
Expressions
3.4
Methods
3.5
Typing
3.6
Arithmetic operations
3.7
Function syntax
4
Programming examples
5
Libraries
6
Development environments
7
Implementations
Toggle Implementations subsection
7.1
Reference implementation
7.2
Other implementations
7.3
No longer supported implementations
7.4
Cross-compilers to other languages
7.5
Performance
8
Development
9
API documentation generators
10
Naming
11
Popularity
12
Uses
13
Languages influenced by Python
14
See also
15
References
Toggle References subsection
15.1
Sources
16
Further reading
17
External links
Toggle the table of contents
Python (programming language)
112 languages
Afrikaans
Alemannisch
العربية
Aragonés
অসমীয়া
Asturianu
Azərbaycanca
تۆرکجه
Basa Bali
বাংলা
閩南語 / Bân-lâm-gú
Беларуская
Беларуская (тарашкевіца)
भोजपुरी
Български
Bosanski
Brezhoneg
Català
Cebuano
Čeština
Cymraeg
Dansk
Deutsch
Eesti
Ελληνικά
Español
Esperanto
Euskara
فارسی
Français
Galego
ગુજરાતી
한국어
Hausa
Հայերեն
हिन्दी
Hrvatski
Ido
Bahasa Indonesia
Interlingua
Íslenska
Italiano
עברית
ქართული
Қазақша
Kiswahili
Kurdî
Кыргызча
ລາວ
Latina
Latviešu
Lietuvių
La .lojban.
Lombard
Magyar
Македонски
മലയാളം
मराठी
Bahasa Melayu
Монгол
မြန်မာဘာသာ
Na Vosa Vakaviti
Nederlands
नेपाली
日本語
ߒߞߏ
Norsk bokmål
Norsk nynorsk
ଓଡ଼ିଆ
Oʻzbekcha / ўзбекча
ਪੰਜਾਬੀ
پنجابی
ភាសាខ្មែរ
Plattdüütsch
Polski
Português
Qaraqalpaqsha
Română
Runa Simi
Русский
Саха тыла
ᱥᱟᱱᱛᱟᱲᱤ
Scots
Shqip
සිංහල
Simple English
Slovenčina
Slovenščina
کوردی
Српски / srpski
Srpskohrvatski / српскохрватски
Suomi
Svenska
Tagalog
தமிழ்
Татарча / tatarça
ၽႃႇသႃႇတႆး
తెలుగు
ไทย
Тоҷикӣ
Türkçe
Basa Ugi
Українська
اردو
ئۇيغۇرچە / Uyghurche
Tiếng Việt
Walon
文言
Winaray
吴语
粵語
中文
Edit links
Article
Talk
English
Read
Edit
View history
Tools
Tools
move to sidebar
hide
Actions
Read
Edit
View history
General
What links here
Related changes
Upload file
Permanent link
Page information
Cite this page
Get shortened URL
Download QR code
Print/export
Download as PDF
Printable version
In other projects
Wikimedia Commons
MediaWiki
Wikibooks
Wikifunctions
Wikiquote
Wikiversity
Wikidata item
Appearance
move to sidebar
hide
From Wikipedia, the free encyclopedia
General-purpose programming language
Python
Paradigm
Multi-paradigm
:
object-oriented
,
[
1
]
procedural
(
imperative
),
functional
,
structured
,
reflective
Designed by
Guido van Rossum
Developer
Python Software Foundation
First appeared
20 February 1991
; 33 years ago
(
1991-02-20
)
[
2
]
Stable release
3.13.2
   / 4 February 2025
; 10 days ago
(
4 February 2025
)
Typing discipline
duck
,
dynamic
,
strong
;
[
3
]
optional type annotations
(since 3.5, but those hints are ignored, except with unofficial tools)
[
4
]
OS
Tier 1
: 64-bit
Linux
,
macOS
; 64- and 32-bit
Windows
10+
[
5
]
Tier 2
: E.g. 32-bit
WebAssembly
(WASI)
Tier 3
: 64-bit
Android
,
[
6
]
iOS
,
FreeBSD
, and (32-bit)
Raspberry Pi OS
Unofficial (or has been known to work): Other
Unix-like
/
BSD
variants) and a few other platforms
[
7
]
[
8
]
[
9
]
License
Python Software Foundation License
Filename extensions
.py, .pyw, .pyz,
[
10
]
.pyi, .pyc, .pyd
Website
python.org
Major
implementations
CPython
,
PyPy
,
Stackless Python
,
MicroPython
,
CircuitPython
,
IronPython
,
Jython
Dialects
Cython
,
RPython
,
Starlark
[
11
]
Influenced by
ABC
,
[
12
]
Ada
,
[
13
]
ALGOL 68
,
[
14
]
APL
,
[
15
]
C
,
[
16
]
C++
,
[
17
]
CLU
,
[
18
]
Dylan
,
[
19
]
Haskell
,
[
20
]
[
15
]
Icon
,
[
21
]
Lisp
,
[
22
]
Modula-3
,
[
14
]
[
17
]
Perl
,
[
23
]
Standard ML
[
15
]
Influenced
Apache Groovy
,
Boo
,
Cobra
,
CoffeeScript
,
[
24
]
D
,
F#
,
GDScript
,
Go
,
JavaScript
,
[
25
]
[
26
]
Julia
,
[
27
]
Mojo
,
[
28
]
Nim
,
Ring
,
[
29
]
Ruby
,
[
30
]
Swift
,
[
31
]
V
[
32
]
Python Programming
at Wikibooks
Python
is a
high-level
,
general-purpose programming language
. Its design philosophy emphasizes
code readability
with the use of
significant indentation
.
[
33
]
Python is
dynamically type-checked
and
garbage-collected
. It supports multiple
programming paradigms
, including
structured
(particularly
procedural
),
object-oriented
and
functional programming
. It is often described as a "batteries included" language due to its comprehensive
standard library
.
[
34
]
[
35
]
Guido van Rossum
began working on Python in the late 1980s as a successor to the
ABC
programming language and first released it in 1991 as Python 0.9.0.
[
36
]
Python 2.0 was released in 2000. Python 3.0, released in 2008, was a major revision not completely
backward-compatible
with earlier versions. Python 2.7.18, released in 2020, was the last release of Python 2.
[
37
]
Python consistently ranks as one of the most popular programming languages, and has gained widespread use in the
machine learning
community.
[
38
]
[
39
]
[
40
]
[
41
]
History
[
edit
]
Main article:
History of Python
The designer of Python,
Guido van Rossum
, at PyCon US 2024
Python was conceived in the late 1980s
[
42
]
by
Guido van Rossum
at
Centrum Wiskunde & Informatica
(CWI) in the
Netherlands
as a successor to the
ABC
programming language, which was inspired by
SETL
,
[
43
]
capable of
exception handling
and interfacing with the
Amoeba
operating system.
[
12
]
Its implementation began in December 1989.
[
44
]
Van Rossum shouldered sole responsibility for the project, as the lead developer, until 12 July 2018, when he announced his "permanent vacation" from his responsibilities as Python's "
benevolent dictator for life
" (BDFL), a title the Python community bestowed upon him to reflect his long-term commitment as the project's chief decision-maker
[
45
]
(he has since come out of retirement and is self-titled "BDFL-emeritus"). In January 2019, active Python core developers elected a five-member Steering Council to lead the project.
[
46
]
[
47
]
The name Python is said to come from the British comedy series
Monty Python's Flying Circus
.
[
48
]
Python 2.0 was released on 16 October 2000, with many major new features such as
list comprehensions
,
cycle-detecting
garbage collection,
reference counting
, and
Unicode
support.
[
49
]
Python 2.7's
end-of-life
was initially set for 2015, then postponed to 2020 out of concern that a large body of existing code could not easily be forward-ported to Python 3.
[
50
]
[
51
]
No further security patches or other improvements will be released for it.
[
52
]
[
53
]
While Python 2.7 and older versions are officially unsupported, a different unofficial Python implementation,
PyPy
, continues to support Python 2, i.e. "2.7.18+" (plus 3.10), with the plus meaning (at least some) "
backported
security updates".
[
54
]
Python 3.0 was released on 3 December 2008, with some new semantics and changed syntax. At least every Python release since (now unsupported) 3.5 has added some syntax to the language, and a few later releases have dropped outdated modules, or changed semantics, at least in a minor way.
Since 7 October 2024
[update]
, Python 3.13 is the latest stable release, and it and, for few more months, 3.12 are the only releases with active support including for bug fixes (as opposed to just for security) and Python 3.9,
[
55
]
is the oldest supported version of Python (albeit in the 'security support' phase), due to Python 3.8 reaching
end-of-life
.
[
56
]
[
57
]
Starting with 3.13, it and later versions have 2 years of full support (up from one and a half), followed by 3 years of security support (for same total support as before).
Security updates were expedited in 2021 (and again twice in 2022, and more fixed in 2023 and in September 2024 for Python 3.12.6 down to 3.8.20), since all Python versions were insecure (including 2.7
[
58
]
) because of security issues leading to possible
remote code execution
[
59
]
and
web-cache poisoning
.
[
60
]
Python 3.10 added the
|
union type operator
[
61
]
and the
match
and
case
keywords (for structural
pattern matching
statements). 3.11 expanded
exception handling
functionality. Python 3.12 added the new keyword
type
. Notable changes in 3.11 from 3.10 include increased program execution speed and improved error reporting.
[
62
]
Python 3.11 claims to be between 10 and 60% faster than Python 3.10, and Python 3.12 adds another 5% on top of that. It also has improved error messages (again improved in 3.14), and many other changes.
Python 3.13 introduces more syntax for types, a new and improved interactive interpreter (
REPL
), featuring multi-line editing and color support; an incremental garbage collector (producing shorter pauses for collection in programs with a lot of objects, and addition to the improved speed in 3.11 and 3.12),  and an
experimental
just-in-time (JIT) compiler
(such features, can/needs to be enabled specifically for the increase in speed),
[
63
]
and an
experimental
free-threaded build mode, which disables the
global interpreter lock
(GIL), allowing threads to run more concurrently, that latter feature enabled with
python3.13t
or
python3.13t.exe
.
Python 3.13 introduces some change in behavior, i.e. new "well-defined semantics", fixing bugs (plus many removals of deprecated classes, functions and methods, and removed some of the C API and outdated modules): "The  [old] implementation of
locals()
and
frame.f_locals
is slow, inconsistent and buggy [and it] has many corner cases and oddities. Code that works around those may need to be changed. Code that uses
locals()
for simple templating, or print debugging, will continue to work corr

Done!
